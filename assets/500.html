
<!DOCTYPE html>
<html lang="en">
  <head>

    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">

    
    <title>Myoperator</title>
    <link rel="shortcut icon" href="/static/images/favicon.ico">

    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i,800,800i" rel="stylesheet" />
    <meta name="robots" content="noindex, nofollow">

    <link rel="canonical" href="http://www.Myoperator.co/"/>
    <meta name="author" content="http://www.Myoperator.co/"/>
     
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.min.css" rel="stylesheet" />

    

    </head>

    <body class="fixed-left">
      
      
<!-- Begin page -->
<div id="wrapper">
<!-- Top Bar Start -->
<div id="wrapper">
<style type="text/css" media="screen">
    .toggle,
[id^=drop] {
    display: none;
}

/* Giving a background-color to the nav container. */
nav { 
    margin:0;
    padding: 0;
    background-color: #4fe7ef;
}



/* Since we'll have the "ul li" "float:left"
 * we need to add a clear after the container. */

nav:after {
    content:"";
    display:table;
    clear:both;
}

/* Removing padding, margin and "list-style" from the "ul",
 * and adding "position:reltive" */
nav ul {
    float: right;
    padding:0;
    margin:0;
    list-style: none;
    position: relative;
    }
    
/* Positioning the navigation items inline */
nav ul li {
    margin: 0px;
    display:inline-block;
    float: left;
    background-color: #186fa1;
    padding: 0px 15px;
   

    }

/* Styling the links */
nav a {
    display:block;
    padding:3px 20px;  
    color:#FFF;
    font-size:14px;
    text-decoration:none;
    padding-top: 12px;
    padding-bottom: 12px;
}


nav ul li ul li:hover { background: #186fa1; }

/* Background color change on Hover */
nav a:hover { 
    background-color: #186fa1; 
    color: #fff;
}

/* Hide Dropdowns by Default
 * and giving it a position of absolute */
nav ul ul {
    display: none;
    position: absolute; 
    /* has to be the same number as the "line-height" of "nav a" */
    top: 50px; 
    padding-bottom: 10px;
    z-index: 3;
    
}
    
/* Display Dropdowns on Hover */
nav ul li:hover > ul {
    display:inherit;
}
    
/* Fisrt Tier Dropdown */
nav ul ul li {
    width:170px;
    float:none;
    display:list-item;
    position: relative;
}

/* Second, Third and more Tiers 
 * We move the 2nd and 3rd etc tier dropdowns to the left
 * by the amount of the width of the first tier.
*/
nav ul ul ul li {
    position: relative;
    top:-40px;
    /* has to be the same number as the "width" of "nav ul ul li" */ 
    left:170px; 
}

    
/* Change ' +' in order to change the Dropdown symbol */
li > a:after { content:  ' +'; }
li > a:only-child:after { content: ''; }


.drop-down-arrow{
    float: right; color: #fff; margin-top: -20px;
}

.menu{
    padding: 17px;
}

/* Media Queries
--------------------------------------------- */

@media all and (max-width : 768px) {

   

    nav {
        margin: 0;
    }

    /* Hide the navigation menu by default */
    /* Also hide the  */
    .toggle + a,
    .menu {
        display: none;
        position: absolute;
        z-index: 3;
        top: 74px;
        height: 100%;
        background: #186fa1;
        padding: 0px;
    }

     .menu h3 {
        display: none;
    }
    .drop-down-arrow{
       margin-top: 0px;
    }


    .mobile-menu{
        float: right;
        font-size: 24px;
        margin: 10px
    }

    /* Stylinf the toggle lable */
    .toggle {
        display: block;
        background-color: #186fa1;
        padding:14px 12px;  
        color:#FFF;
        font-size:17px;
        text-decoration:none;
        border:none;
    }

    .toggle:hover {
        background-color: #186fa1;
    }

    /* Display Dropdown when clicked on Parent Lable */
    [id^=drop]:checked + ul {
        display: block;
    }

    /* Change menu item's width to 100% */
    nav ul li {
        display: block;
        width: 100%;
        }

    nav ul ul .toggle,
    nav ul ul a {
        padding: 0 40px;
    }

    nav ul ul ul a {
        padding: 0 80px;
    }

    nav a:hover,
    nav ul ul ul a {
        background-color: #186fa1;
    }
  
    nav ul li ul li .toggle,
    nav ul ul a,
  nav ul ul ul a{
        padding:14px 12px;  
        color:#FFF;
        font-size:17px; 
    }
  
  
    nav ul li ul li .toggle,
    nav ul ul a {
        background-color: #4aa3d6; 
    }

    /* Hide Dropdowns by Default */
    nav ul ul {
        float: none;
        position:static;
        color: #ffffff;
        /* has to be the same number as the "line-height" of "nav a" */
    }
        
    /* Hide menus on hover */
    nav ul ul li:hover > ul,
    nav ul li:hover > ul {
        display: none;
    }
        
    /* Fisrt Tier Dropdown */
    nav ul ul li {
        display: block;
        width: 100%;
    }

    nav ul ul ul li {
        position: static;
        /* has to be the same number as the "width" of "nav ul ul li" */ 

    }

}

@media all and (max-width : 330px) {

    nav ul li {
        display:block;
        width: 94%;
    }

}
.hover a:hover{
    background-color: #4aa3d6;
}
</style>
<nav>
<div style="float: left;"><a href="/"></a></div>
</div>
<!-- Top Bar End -->
    <div class="content" style="text-align: center;">
        <h1 style="font-size: 120px;margin-top: 104px;">500</h1>
        <h3 style="margin-top:40px;">Internal Server Error</h1>
        <h4 style="margin-top:10px;">Click <a href="http://www.Myoperator.co">here</a> to head back to homepage</h1>
    </div>
  </body>
</html>
