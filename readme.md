- [Getting Started](#getting-started)
  - [Dependencies](#dependencies)
  - [Local Setup](#local-setup)
  - [Deployment](#deployment)
- [OBD Central](#obd-central)
  - [1. Central\_1](#1-central_1)
    - [External Dependencies](#external-dependencies)
  - [2. Central\_2](#2-central_2)
    - [External Dependencies](#external-dependencies-1)
  - [3. On-Hold Processor](#3-on-hold-processor)
    - [External Dependencies](#external-dependencies-2)
  - [4. On-Hold Circulator](#4-on-hold-circulator)
    - [External Dependencies](#external-dependencies-3)
  - [5. Expiry Manager](#5-expiry-manager)
    - [External Dependencies](#external-dependencies-4)
  - [6. Response Manager](#6-response-manager)
    - [External Dependencies](#external-dependencies-5)
  - [7. Group IVR Relation Handler Manager](#7-group-ivr-relation-handler-manager)
    - [External Dependencies](#external-dependencies-6)
  - [8. Group Server Consistency Handler](#8-group-server-consistency-handler)
    - [External Dependencies](#external-dependencies-7)
  - [9. Central Delay Queue Handler](#9-central-delay-queue-handler)
    - [External Dependencies](#external-dependencies-8)
  - [10. Company Usage Check Handler](#10-company-usage-check-handler)
    - [External Dependencies](#external-dependencies-9)
  - [11. GRTPuller](#11-grtpuller)
    - [External Dependencies](#external-dependencies-10)
  - [12. S3Pusher](#12-s3pusher)
    - [External Dependencies](#external-dependencies-11)
  - [13.Cache Consistency Check Handler](#13cache-consistency-check-handler)
    - [External Dependencies](#external-dependencies-12)
  - [14. IVR Queue Remover Handler](#14-ivr-queue-remover-handler)
    - [External Dependencies](#external-dependencies-13)
  - [15. Testing Group Queues Messages Remover Handler](#15-testing-group-queues-messages-remover-handler)
    - [External Dependencies](#external-dependencies-14)
  - [16. Data Deletion Handler](#16-data-deletion-handler)
  - [17. SQS Attributes Changer Handler](#17-sqs-attributes-changer-handler)
  - [18. Jobs Status Sync Handler](#18-jobs-status-sync-handler)
  - [Commands](#commands)
  - [Fixtures and Seeds](#fixtures-and-seeds)
  - [Dependencies](#dependencies-1)
    - [APIs](#apis)
    - [Shared Cache](#shared-cache)
    - [Central Cache](#central-cache)
    - [SQS Queues](#sqs-queues)
  - [Environment Variables](#environment-variables)
  - [Group Queue Routing Manager Algo](#group-queue-routing-manager-algo)
  - [fabfile description (/deploy-config/fab-file.py)](#fabfile-description-deploy-configfab-filepy)
  - [Caveats](#caveats)
  - [Test Cases](#test-cases)

# Getting Started

## Dependencies

- mysql
- redis


## Local Setup

1. create and activate venv
1. create .env with [Environment Variables](#environment-variables)
1. Add ```export DJANGO_SETTINGS_MODULE=central.settings.local``` in ~/.bashrc , if bash is being used. Similarly can be added in the configuration files of other UNIX shells like zsh etc.
<br/> or <br/>
Open an terminal and first run ```DJANGO_SETTINGS_MODULE=central.settings.local``` and then you can run any command with ```python manage.py <command>``` in the same terminal

1. python manage.py migrate
1. sample handler
    1. python manage.py central_1_handler

## Deployment

1. `python deploy.py`
Read `deployment/deployment.md` file for more information

# OBD Central

OBD Central is a part of **MyOperator OBD**, its sole purpose is to route the requests from **ivr_queues** to **group_queues**.
This module has multiple handlers which are described below.

## 1. Central_1

**Central_1** moves requests from **ivr_queues** to **priority_queues**.

### External Dependencies

1. **Cache**: IVR Requests Count **obd-central-ivrid-{ivr_id}**.
2. **SQS Queues**: IVR Queues - **obd-central-ivrid-{ivr_id}**, Priority Queues - **obd-central-pq{1, 2, 3}**.
3. **IVR Rule API**: To check whether it is right time to initite a call or not.
4. **Routing Info API**: To get data of how to call **IVR Rule API**.

   - **Routing Into URL**: "http://10./api/1/routinginfo"
   - **Routing Into Data**: {'method':'GET','url': ROUTING_INFO_URL,'data':{'body':{},'headers':{},'params':{'name':''}}}

**Note**: We have to register **Rule API** to **API Info** before consuming it.

**Rule API Registration request body**:

```json
{
    "name": "ivr_rule",
    "method": "GET",
    "data": "{'body': {}, 'headers': {}, 'dynamic_args': { 'ivr_id': 'ivr_id', 'company_id': 'company_id' }, 'params': {}}",
    "is_external": 1,
    "ip_url": "",
    "relative_url": "https://microapi.myoperator.dev/obd/ivr/company_id/ivr_id/rules",
    "url": "https://microapi.myoperator.dev/obd/ivr/company_id/ivr_id/rules"
}
```

- **Global Cancellation GET API Registration request body:**

```json
{
    "name": "global_cancellation1",
    "method": "GET",
    "data": "{'body': {}, 'headers': {'Authorization': 'Token {token}'}, 'params': {'request_status': 'request_status'}}",
    "is_external": 0,
    "ip_url": "http://*************",
    "relative_url": "/api/1/cancellation/",
    "url": "http://*************/api/1/cancellation/"
}
```

- Processor Manager Body in case of global cancel request:

```json
{
    "request_id": req_id,
    "company_id": c_id,
    "ivr_id": ivr_id,
    "response": {"emitter": "call", "failure_reason": "request is globally cancelled!", "message": ""},
    "response_status": "200",
    "service_name": "cancel_service",
    "context": "service_invoker"
}
```

## 2. Central_2

**Central_2** moves requests from **priority_queues** to **group_queues**.

### External Dependencies

1. **SQS Queues**: Priority Queues - **obd-central-pq{1, 2, 3}**, On-Hold Queues - **obd-central-on-hold**, Group Queues - **obd-central-group-queues_{p_q, np_q}_{kam_group_id}** (Default Visibility Time: 600sec), Process Manager Queue - **obd_pm_invoker**.
2. **Shared Cache**: To store requests count of **group_queues** that will be used by **IVR Processor**.
3. **UDC API**: To check whether **channel/user/user_of_department** is available ot not.

**Note**: We are not using the Routing Manager to hit UDC.

- **UDC API request format**:
  
```json

    UDC_CHANNEL_API_URL = "{udc_base}/{company_id}/channel"
    UDC_USER_CHANNEL_API_URL = "{udc_base}/{company_id}/{user_id}/user-channel"
    UDC_USER_DEPARTMENT_API_URL = "{udc_base}/{company_id}/{ivr_id}/dept-channel?max_users=1"
```

- Processor Manager Body when request is successfully routed to Group Queue:

```json
{
    "request_id": req_id,
    "company_id": c_id,
    "ivr_id": ivr_id,
    "response": {"emitter": "call", "failure_reason": "", "message": "success"},
    "response_status": "200",
    "service_name": "call",
    "context": "service_invoker"
}
```

- Processor Manager Body in case of cancel request:

```json
{
    "request_id": req_id,
    "company_id": c_id,
    "ivr_id": ivr_id,
    "response": {"emitter": "call", "failure_reason": "obd_central group not found!", "message": ""},
    "response_status": "404",
    "service_name": "cancel_service",
    "context": "service_invoker"
}
```

## 3. On-Hold Processor

**On-Hold Processor** pulls the requests from oh-hold-queue and marks the request as on-hold-request in CentralRequestInfo.
Also checks for **ivr_tracing count** to **pause ivrs**, and **request_tracing count** to **cancel the request**.
**vars description:**
    1. **on_hold_circulation_req_process_count**
        No of requests circulation pulls from DB for one execution.
    2. **on_hold_duplicate_delay**
        Interval between successive failed UDC Checks
    3. **on_hold_max_attempts**
        No of times a request can come to on-hold handlers
    4. **on_hold_max_user_check_count**
        No of times UDC can be checked for a specific request
    5. **on_hold_max_ivr_threshold**
        Max no of requests that can come to on-hold for the same ivr_id, else it will get paused for 2 mins

### External Dependencies

1. **SQS Queues**: On-Hold Queues - **obd-central-on-hold**, Process Manager Queue - **obd_pm_invoker**.

- Processor Manager Body in case of cancel request:

```json
{
    "request_id": req_id,
    "company_id": c_id,
    "ivr_id": ivr_id,
    "response": {"emitter": "call", "failure_reason": "obd_central - on-hold request tracing count has exceeded the threshold", ""},
    "response_status": "500",
    "service_name": "cancel_service",
    "context": "service_invoker"
}
```

## 4. On-Hold Circulator

**On-Hold Circulator** picks the requests from DB and checks for UDC, If UDC is available then moves the request to **priority_queues**,
if not and if max UDC check count gets exhausted then cancel the request.

### External Dependencies

1. **SQS Queues**: Priority Queues - **obd-central-pq{1, 2, 3}**, Process Manager Queue - **obd_pm_invoker**.

- Processor Manager Body in case of cancel request:

```json
{
    "request_id": req_id,
    "company_id": c_id,
    "ivr_id": ivr_id,
    "response": {"emitter": "call", "failure_reason": "obd_central - on-hold: UDC check count has exceeded threshold", ""},
    "response_status": "500",
    "service_name": "cancel_service",
    "context": "service_invoker"
}
```

## 5. Expiry Manager

**Expiry Manager** picks the requests from DB which are not complete and are expired and then marks them as complete from expiry manager, and routes the request based on process flow api.

### External Dependencies

1. **SQS Queues**: Service Queue Router **obd_service_route_queue**, Process Manager Queue - **obd_pm_invoker**.
2. **Process Flow API**: To check where to route the request after completion.
3. **Routing Info API**: To get data of how to call **Process Flow API**.

**Note**: We have to register **Process Flow** to **Routing Info** first to consume it, and also have to make rules on **Process Flow API**.

- **Process Flow Registration request body**:

```json
{
    "name": "process_flow_get",
    "method": "GET",
    "data": "{'body': {}, 'headers': {'Authorization': 'Token {token}'}, 'params': {'company_id': 'c_id', 'iteration': 'iteration', 'ivr_id': 'ivr_id', 'source_name': 'source_name', 'event': 'event_response'}}",
    "is_external": 0,
    "ip_url": "http://**********",
    "relative_url": "/api/1/process-flow/",
    "url": "http://**********/api/1/process-flow/"
}
```

- Processor Manager Body when process flow is found

```json
{
    "request_id": req_id,
    "company_id": c_id,
    "ivr_id": ivr_id,
    "response": {"emitter": "expiry_manager", "failure_reason": "", "message": "Expired"},
    "response_status": "200",
    "service_name": "expiry_manager",
    "context": "service_invoker"
}
```

- Processor Manager Body in case of completed request (process flow not found):

```json
{
    "request_id": req_id,
    "company_id": c_id,
    "ivr_id": ivr_id,
    "response": {"emitter": "expiry_manager", "failure_reason": "obd_central - process_flow not found!", "message": "Expired"},
    "response_status": "200",
    "service_name": "complete_service",
    "context": "service_invoker"
}
```

## 6. Response Manager

**Response Manager** pulls the requests from the response manager queue, and marks them as complete from response manager, and routes the request based on process flow API.
### External Dependencies

1. **SQS Queues**: Response Manager Queue: **obd_central_rm**, Service Queue Router **obd_service_route_queue**, Process Manager Queue - **obd_pm_invoker**.
2. **Process Flow API**: To check where to route the request after completion.
3. **Routing Info API**: To get data on how to call **Process Flow API**.

- Processor Manager Body when process flow is found

```json
{
    "request_id": req_id,
    "company_id": c_id,
    "ivr_id": ivr_id,
    "response": {"emitter": "response_manager", "failure_reason": "", "message": "Completed"},
    "response_status": "200",
    "service_name": "response_manager",
    "context": "service_invoker"
}
```

- Processor Manager Body in case of completed request (process flow not found):


```json
{
    "request_id": req_id,
    "company_id": c_id,
    "ivr_id": ivr_id,
    "response": {"emitter": "response_manager", "failure_reason": "obd_central - process_flow not found!", "message": "Completed"},
    "response_status": "200",
    "service_name": "complete_service",
    "context": "service_invoker"
}
```

## 7. Group IVR Relation Handler Manager

This Handler pulls the requests from obd-central-group-ivr-relation queue, and creates group, servers, and number,
and also makes the relation between **ivrinfo, groups and numbers**, **group and servers** and **group and numbers**.

### External Dependencies

1. **SQS Queues**: Group IVRInfo Relation Queue: **obd-central-group-ivr-relation**.
2. **Kamailio Group Server API**: To check where to route the request after completion.
3. **Routing Info API**: To get data on how to call **Kamailio Group Server API**.
4. **Fix-DID API**: To get a group and IVRs for a given did and company_id.

- **Group IVRInfo Reletion Queue DATA Format:**

```json
{
    "event": {
        "namespace": "fixdid",
        "version": "0.1.1",
        "etag": "Qwerty===",
        "action": "map_did_ivr",
        "created_at": "ISO-8601 timestamp",
    },
    "data": {
        "did": "********",
        "company_id": "1"
    }
}
```

- **Valid Event Actions:** map_did_ivr, unmap_did_ivr
- **Discard Event Actions:** book_did, release_did

**Note**: We are not using the Routing Manager to hit Fix-DID API

- **Fix-DID API request format**:

```json
    FIXDID_API_URL = "https://microapi.myoperator.dev/fix-did//did/{company_id}/{did}"

```

**Note**: We have to register **Kamailio Group Server API** to **API Info** first to consume it.

- **Kamailio Group Server API Registration request body**:

```json
{
    "name": "kam_group_servers",
    "method": "POST",
    "data": "{'body': {'token': 'token', 'kam_group_id': 'kam_group_id', 'is_obd': 'is_obd'}, 'headers': {}, 'params': {}}",
    "is_external": 1,
    "ip_url": null,
    "relative_url": "http://kamdev.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_servers",
    "url": "http://kamdev.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_servers"
}
```

## 8. Group Server Consistency Handler

This handler is responsible for managing consistency between Kamiolio and OBD Central groups and servers.

### External Dependencies

1. **Kamailio Group Server API**: To check where to route the request after completion.
2. **Kamailio Active Group Server API**: Te gets the list of active groups and servers.
3. **Routing Info API**: To get data of how to call **Kamailio Group Server API** and **Kamailio Active Group Server API**

**Note**: We have to register **Kamailio Group Server API** and **Kamailio Active Group Server API** to **API Info** before consuming it.

- **Kamailio Active Group Server API Registration request body**:

```json
{
    "name": "kam_group_server_list",
    "method": "POST",
    "data": "{'body': {'token': 'token', 'is_obd': 'is_obd'}, 'headers': {}, 'params': {}}",
    "is_external": 1,
    "ip_url": null,
    "relative_url": "http://kamdev.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_server_list",
    "url": "http://kamdev.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_server_list"
}
```

## 9. Central Delay Queue Handler

This handler pulls the requests from obd_central_delay_queue and push it to ivr_queues.
### External Dependencies

1. **SQS Queues**: Delay Queue: **obd_central_delay_queue**, IVR Queues - **obd-central-ivrid-{ivr_id}**.

## 10. Company Usage Check Handler

This handler Pulls request from company_usage_check_queue and checks Whether service is activated , deactivated or if a feature is changed against a company.

### External Dependencies

1. **SQS Queues**: Company Usage Check Queue: **obd_central_company_usage_check**.

## 11. GRTPuller

**GRTPuller** is responsible for getting completed requests of a given day from **GRT API** and stores them to a file.

### External Dependencies

1. **Completed Requests Discovery (GRT) API**: To get completed requests of a specific date
2. **Routing Info API**: To get data of how to call **GRT API**.

**Note**: We have to register **GRT API** to **API Info** before consuming it.

- **GRT API Registration request body**:

```json
{
    "name": "completed_requests_discovery",
    "method": "GET",
    "data": "{'body': {}, 'headers': {'Authorization': 'Token 521b849c6fa3dd1d18ae2d819c60cf546138d2a2'}, 'params': {'date': 'date', 'page': 'page'}}",
    "is_external": 0,
    "ip_url": "http://*************",
    "relative_url": "/api/1/completed_requests_discovery/",
    "url": "http://*************/api/1/completed_requests_discovery/"
}
```

## 12. S3Pusher

**S3Pusher** is responsible for migrating data from DB to S3 bucket.

### External Dependencies

1. **S3 bucket**: obd_central, can be changed from DB common settings.

## 13.Cache Consistency Check Handler

Checks consistency of Cache and SQS Queues.

### External Dependencies

1. **SQS Queues**: All Queues
2. **Cache**: Local and Shared Cache

## 14. IVR Queue Remover Handler

Delete all those IVR Queues that have not been used for a long time.

### External Dependencies

1. **SQS Queues**: IVR Queues
2. **Cache**: Local and Shared Cache

## 15. Testing Group Queues Messages Remover Handler

This handler deletes requests from all active groups. This was made for testing purposes.

### External Dependencies

1. **SQS Queues**: Group Queues - **obd-central-group-queues_{p_q, np_q}_{kam_group_id}**

## 16. Data Deletion Handler

This handler deletes data from given modules with given configurations in DB(common_settings-data_deletion).

## 17. SQS Attributes Changer Handler

This handler changes the attributes of queues with given prefix, by given attributes.

## 18. Jobs Status Sync Handler

if a job/sheet gets aborted, this handler aborts all the pending requests of that job,  This handler reads the job id from the cache, and hits a paginated api of JP to get all the requests related to that job, and then mark them IVR_PROCESSOR_FAILED as completion event type, only if the requests are pending and hasn't been already sent to group queues, and then delete the cache key when no request is fetched from JP.

## Commands

1. python3 manage.py central_1_handler
2. python3 manage.py central_2_handler
3. python3 manage.py on_hold_processor_handler
4. python3 manage.py on_hold_circulation_handler
5. python3 manage.py expiry_manager_handler #
6. python3 manage.py response_manager_handler
7. python3 manage.py group_ivrinfo_relation_handler
8. python3 manage.py group_server_consistancy_handler #c 18h
9. python3 manage.py central_delay_queue_handler
10. python3 manage.py company_usage_check_handler
11. python3 manage.py db_cleaner_grt_puller_handler
12. python3 manage.py db_cleaner_s3_pusher_handler
13. python3 manage.py cache_consistency_check_handler
14. python3 manage.py ivr_queue_remover_handler
15. python3 manage.py testing_group_queues_messages_remover_handler
16. python3 manage.py data_deletion_handler
17. python3 manage.py sqs_attributes_changer_handler
18. python3 manage.py jobs_status_sync_handler

## Fixtures and Seeds

- python manage.py loaddata central/fixtures/sqs_queue_info.json
- python manage.py loaddata central/seeds/*.json


## Dependencies

### APIs

1. Routing Info API (Process Manager)
2. IVR Rule API
3. UDC API
4. Process Flow API (Process Manager)
5. Kamailio Group Server API
6. Kamailio Active Group Server Lists API
7. Global Cancellation GET API (Process Manager)
8. Completed Requests Discovery (GRT) (Process Manager)
9. Job Processor Requests List API (Job Processor)

### Shared Cache

1. **Central_2** will use this to inform **ivr_processor** about the number of requests in group queues.

### Central Cache

1. Logic & Central_1
2. Service Router & Central Delay Queue
3. Response Manager & Response Manager
4. Notify Lambda & Company Usage Check
5. Notify Lambda & GroupIVR Relation
6. JOBS_STATUS_SYNC_HANDLER

### SQS Queues

1. IVR Queues: **obd-central-ivrid-{ivr_id}**
2. Priority Queues: **obd-central-pq{1, 2, 3}**
3. On-Hold Queues - **obd-central-on-hold**
4. Group Queues - **obd-central-group-queues_{p_q, np_q}_{kam_group_id}** (Central Will take care of these queues)
5. Process Manager Queue - **obd_pm_invoker**
6. Service Queue Router **obd_service_route_queue** 4 min
7. Response Manager Queue: **obd_central_rm**  4 min
8. Group IVRInfo Reletion Queue: **obd-central-group-ivr-relation**
9. Delay Queue: **obd_central_delay_queue**
10. Company Usage Check Queue: **obd_central_company_usage_check**

obd_api 4 min
lambda default timeout 5 min

## Environment Variables

```env

    DJANGO_SETTINGS_MODULE=central.settings.local

    #optional
    DJANGO_SECRET=asdhlasdjl212e1

    #production only
    DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1
    LOGGER_SLACK_URL=https://hooks.slack.com/services/sadsdnbe
    CELERY_DEFAULT_QUEUE=asd

    REDIS_HOST=localhost
    REDIS_PORT=6379
    REDIS_DB=0
    LOGFILE_PATH=common.log

    DB_ENGINE=django.db.backends.mysql
    DB_NAME=central
    DB_USER=root
    DB_PASSWORD=root
    DB_HOST=localhost
    DB_PORT=3306

    READ_DB_ENGINE=django.db.backends.mysql
    READ_DB_NAME=central_Read
    READ_DB_USER=root
    READ_DB_PASSWORD=root
    READ_DB_HOST=localhost
    READ_DB_PORT=3306

    AWS_ACCESS_KEY_ID=asdsad
    AWS_SECRET_ACCESS_KEY=fafsf
    AWS_REGION_NAME=ap-southeast-1

    SHARED_REDIS_HOST=localhost
    SHARED_REDIS_PORT=6379
    SHARED_REDIS_DB=1

    #constants.py vars
    API_CACHE_TTL=10
    CANCELLATION_API_URL=http://host/api/1/cancellation
    CANCELLATION_ENABLED=0
    CANCELLED_REQUEST_STATUS=2

    #optional
    SOURCE_NAME=call
    CANCEL_SERVICE=cancel_service
    RESPONSE_MANAGER=response_manager
    EXPIRY_MANAGER=expiry_manager
    COMPLETE_SERVICE=complete_service

    ROUTING_INFO_URL=http://host/api/1/routinginfo
    PM_AUTH_TOKEN=asdjklsadhlasd
    SERVER_ROUTE_QUEUE_NAME=obd_service_route_queue
    IVR_RULE_ROUTE_NAME=ivr_rule
    PROCESS_FLOW_ROUTER_NAME=process_flow_get
    KAM_GROUP_SERVERS_ROUTE_NAME=kam_group_servers
    KAM_GROUP_SERVERS_LIST_ROUTE_NAME=kam_group_server_list
    GLOBAL_CANCELLATION_ROUTE_NAME=global_cancellation
    COMPLETED_REQUESTS_ROUTE_NAME=completed_requests_discovery
    KAM_GROUP_SERVERS_TOKEN=asdasdasdasda
    UDC_API_BASE_URL=https://host/obd/availability
    RULE_API_BASE_URL=https://host/obd/ivr/
    KAM_API_CACHE_TTL=

    #optional
    OBD_QUEUE_TAG_KEY=obd
    OBD_QUEUE_TAG_VALUE=obd

    FIXDID_API_BASE_URL=https://hostev/fix-did/
    SHARED_REDIS_VERSION=cs
    CANCELED_REQUEST_KEY=
    JP_REQUESTS_LIST_API_BASE_URL=http://host/api/1/list
    JP_REQUESTS_LIST_API_TOKEN=asdsdasdadsad

```

## Group Queue Routing Manager Algo

```mermaid
graph TD
    START -->|Case 1: Filters Given| A{source_number given}
    START -->|Case 2: No Filters Given| B{related_group_exist}
    A -->|YES| A1{group_exist}
    A --> |NO| A2
    A1 --> |YES| A5{group_avail}
    A1 --> |NO| A2{group or region given}
    A2 --> |YES| A3{group_exist}
    A3 --> |YES| A5
    A3 --> |NO| A4{shared_backup_enable}
    A4 --> |YES| ASSIGN_DEFAULT_GROUP
    A4 --> |NO| CANCEL_REQUEST
    A5 --> |YES| RETURN_GROUP
    A5 --> |NO| IGNORE_REQUEST
    B --> |YES| B1{related_group_avail}
    B --> |NO| A4{shared_backup_enable}
    B1 --> |YES| ASSIGN_RELETED_GROUP
    B1 --> |NO| B2{shared_backup_enable}
    B2 --> |YES| ASSIGN_DEFAULT_GROUP
    B2 --> |NO| IGNORE_REQUEST
```

## fabfile description (/deploy-config/fab-file.py)

python3 <file_name.py> <collect_static:install_reqs:migrate:restart_nginx:restart_server>

1. if only collect_static is required:
    python3 <file_name.py> y:n:n:n:n
2. if only install_reqs is required: [provide new_requirements_file_name in code if required name as *new_requirements_txt_file_name*]
    python3 <file_name.py> n:y:n:n:n
3. if only migrate is required:
    python3 <file_name.py> n:n:y:n:n

general-commands:
    1. change branch name
    2. python3 fab-file.py n:n:y:n:y

Complete-push:

1. change management_command_transaction_enabled = True
2. check assigned_branch
3. check host mapping for handler named as *host_mapping_management_handler*
4. if new_requirements has to be install:
    4.1: give file name in var *new_requirements_txt_file_name* in file
5. python3 fab-file.py y:n:y:n:y

[push only on one server]

1. assign only one host in *hosts* var in file
2. check/change handlers in *host_mapping_management_handler* if required

## Caveats

1. We Cache Rule API, for 2 Hours (7200s), in central's local cache.
    - Key: **:cs:obd_central_ivr_rules_{ivr_id}**
2. We cache Aborted Job ids in Cache for 5 days (432000s)
    - Key_Prefix: **:cs:obd_central_jp_job_abort_**

## Test Cases

- ### Test case command

    ```bash
      pytest -v2
    ```
