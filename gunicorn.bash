NAME="central"
DJANGODIR="$(cd "$( dirname "${BASH_SOURCE[0]}")/.." && pwd )"
USER=ubuntu
# GROUP=root
NUM_WORKERS=5
DJANGO_WSGI_MODULE=central.wsgi


export DJANGO_SETTINGS_MODULE="central.settings.production"
export PYTHONPATH=$DJANGODIR:$PYTHONPATH

#gunicorn3 for apt install gunicorn3
exec $(pwd)/venv/bin/gunicorn ${DJANGO_WSGI_MODULE}:application \
        --name $NAME \
        --workers $NUM_WORKERS \
        --max-requests 2000 \
        --user=$USER \
        --bind=0.0.0.0:8009



