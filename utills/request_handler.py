import logging
from functools import partial
from typing import Callable, Dict, Union

from handlers.models import CentralRequestInfo
from utills.destination.group_destination import GroupDestination
from utills.exceptions import (
    GroupUnavailableException,
    UDCLockedException,
    UDCUnavailable,
)
from utills.group_queues_routing_manager.main_v2 import <PERSON><PERSON>andler_V2
from utills.helpers.helper import Helper
from utills.udc_helper import UDCHelper

logger = logging.getLogger(__name__)


class RequestHandler:
    def __init__(self) -> None:
        self.udc_helper: UDCHelper = None
        self.group_handler = None

        self.destination: GroupDestination = GroupDestination()

    def setup(self, cri_obj: CentralRequestInfo):
        self.udc_helper = UDCHelper(cri_obj)
        self.group_handler = GroupHandler_V2(cri_obj)
        self.request: CentralRequestInfo = cri_obj

    def check_udc(self) -> bool:
        try:
            toggle_view_only = Helper.get_toggle_view_only_value(
                self.request.ivr_id,
            )
            self.udc_helper.udc_checks(user_view_only=toggle_view_only)
            return True
        except UDCUnavailable as error:
            logger.info(f"UDCUnavailable with error: {str(error)}")
            return False

    def get_group_data(self) -> Dict:
        group_data = self.group_handler.assign_group()

        if not group_data:
            logger.info(
                "Ignoring requests because of no available group found!"
            )
            raise GroupUnavailableException()

        if "is_cancle_request" in group_data:
            logger.info("canceling request because of group not found!")
            raise GroupUnavailableException(
                cancel_request=True,
            )

        return group_data

    def process(
        self,
        on_udc_unavailable: Callable[[CentralRequestInfo], None],
        on_success: Callable[
            [UDCHelper, CentralRequestInfo, Union[Dict, str]],
            None,
        ],
        on_failure: Callable[
            [CentralRequestInfo, Union[Dict, str], str],
            None,
        ],
        on_retry: Callable[[CentralRequestInfo], None] = None,
    ):
        group_data = self.get_group_data()

        try:
            if self.check_udc():
                self.destination.push(
                    self.request,
                    group_data,
                    self.udc_helper.udc_response,
                    partial(on_success, self.udc_helper),
                    on_failure,
                    on_retry=on_retry,
                )

            else:
                on_udc_unavailable(self.request)

        except UDCLockedException as error:
            logger.info(f"UDCLockedException with error: {error.message}")
