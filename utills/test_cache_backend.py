from django.core.cache.backends.locmem import LocMemCache


class CustomLocMemCache(LocMemCache):
    def keys(self, key_prefix: str, version: int = None) -> list:
        assert key_prefix.endswith("*"), "key_prefix must be ends with *"
        keys: list = []

        key_prefix: str = key_prefix[:-1]  # remove `*` from key prefix
        key_prefix = self.make_key(key_prefix, version=version)

        for key in self._cache.keys():
            if key.startswith(key_prefix):
                keys.append(key.split(":")[-1])

        return keys
