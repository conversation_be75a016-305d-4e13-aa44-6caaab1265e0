import copy
from datetime import datetime

from django.conf import settings
from django.db import transaction

from handlers.models import Group, GroupServerRelationship, Number, Server
from utills.helpers.helper import Helper


class GroupServerRelationManager:
    @staticmethod
    def add_group_servers(kam_group_id, group_data):
        """
        Manager funtion to call create group and create server methods.
        Args:
                kam_group_id: {kam_group_id}
                group_data: {'name': 'TCS-TA-MP', 'c_id': 'TCS-TA-MP', 'd_id': 'MYOP', 'operator': 'TA_TCS', 'caller_id': '11615680', 'servers': ['d1.voicetree.info', 'd2.voicetree.info', 'd3.voicetree.info', 'd4.voicetree.info', 'd5.voicetree.info']}
        Returns:
                False
        """
        assigned_queues = Helper.get_group_assigned_queues(kam_group_id)
        group_name = group_data.pop("name")
        group_alias = group_data.pop("group_alias")
        region = group_data.pop("region")
        group_servers = group_data.pop("servers")
        group_id = GroupServerRelationManager.create_group(
            group_name,
            assigned_queues,
            kam_group_id,
            group_data,
            group_alias,
            region,
        )
        if not group_id:
            return
        GroupServerRelationManager.manage_servers(group_id, group_servers)
        GroupServerRelationManager.manage_default_number(group_id, group_data)

    @staticmethod
    def update_group_servers(group_obj, kam_group_id, group_data):
        """
        Manager funtion to call update group and create server methods.
        Args:
                group_obj: {django model instance of Group}
                kam_group_id: kam_group_id
                group_data: {'name': 'TCS-TA-MP', 'c_id': 'TCS-TA-MP', 'd_id': 'MYOP', 'operator': 'TA_TCS', 'caller_id': '11615680', 'servers': ['d1.voicetree.info', 'd2.voicetree.info', 'd3.voicetree.info', 'd4.voicetree.info', 'd5.voicetree.info']}
        Returns:
                False
        """
        group = group_obj.first()

        group_name = group_data.pop("name")
        group_alias = group_data.pop("group_alias")
        region = group_data.pop("region")
        group_servers = group_data.pop("servers")

        old_settings = dict(group.settings or {})
        old_settings = copy.deepcopy(old_settings)
        old_settings.update(group_data)

        group.is_enable = Group.YES
        group.name = group_name
        group.group_alias = group_alias
        group.region = region
        group.settings = old_settings
        group.save(
            update_fields=[
                "is_enable",
                "name",
                "group_alias",
                "region",
                "settings",
                "updated_on",
            ]
        )

        GroupServerRelationManager.manage_servers(group.id, group_servers)
        GroupServerRelationManager.manage_default_number(group.id, group_data)

    @staticmethod
    def manage_servers(group_id, group_servers):
        """
        Manager funtion to create server if not  exist and also responsible for creating reletion between group and servers
        Args:
                group_id: {group_id}
                group_servers: ["s1.voicettree.info"]
        Returns:
                False
        """
        server_ids = []
        for server in group_servers:
            server_obj = Server.objects.filter(name=server)
            if not server_obj.exists():
                server_id = GroupServerRelationManager.create_server(server)
            else:
                server_obj.invalidated_update(
                    is_enable=Server.YES, updated_on=datetime.now()
                )
                server_id = server_obj.first().id

            server_ids.append(server_id)
            group_server_relation_obj = GroupServerRelationship.objects.filter(
                group_id=group_id, server_id=server_id
            )
            if not group_server_relation_obj.exists():
                GroupServerRelationManager.create_group_server_relation(
                    group_id, server_id
                )

        # removing extra entries from group_server_relation table
        GroupServerRelationship.objects.filter(group_id=group_id).exclude(
            server_id__in=server_ids
        ).delete()

    @staticmethod
    def create_group(
        group_name,
        assigned_queues,
        kam_group_id,
        settings_dict,
        group_alias,
        region,
    ):
        """
        Adds a new Group object
        Args:
                group_name: {group_name}
                assigned_queues: {'p_q': {'name': '{gateway}_p_q_{name}',
                        'url': 'https://ap-south-1.queue.amazonaws.com/472952060482/gateway_p_q-{name}',
                        'threshold': 10},
                        'np_q': {'name': 'gateway_np_q-{name}',
                        'url': 'https://ap-south-1.queue.amazonaws.com/472952060482/gateway_np_q-{name}',
                        'threshold': 30}}
                kam_group_id: {kam_group_id}
                settings_dict: {"c_id":"NOIDA-VPS-AI-CO","d_id":"SIP","operator":"VPS_Airtel","caller_id":"12043850"}
                group_alias: {gp_alias}
                region: {region}
        Returns:
                group.id: new created group object's id
        """
        try:
            with transaction.atomic():
                group_obj = Group.objects.create(
                    name=group_name,
                    group_alias=group_alias,
                    region=region,
                    assigned_queues=assigned_queues,
                    is_enable=Group.YES,
                    is_default=Group.NO,
                    kam_group_id=kam_group_id,
                    settings=settings_dict,
                )
            settings.API_LOGGER.info(
                "group_server_relation_manager - creating group, kam_group_id: {kam_group_id}, group_name: {group_name}".format(
                    kam_group_id=kam_group_id, group_name=group_name
                )
            )
            return group_obj.id
        except Exception as e:
            settings.API_LOGGER.error(
                "group_server_relation_manager - Unable to create a group, kam_group_id: {kam_group_id}".format(
                    kam_group_id=kam_group_id
                )
            )

    @staticmethod
    def create_server(server_name):
        """
        Adds a new Server object
        Args:
                server_name: "s1.voicettree.info"
        Returns:
                servers.id: new created server object's id
        """
        with transaction.atomic():
            server_obj = Server.objects.create(name=server_name)
        settings.API_LOGGER.info(
            "group_server_relation_manager - creating server, server_name: {server_name}".format(
                server_name=server_name
            )
        )
        return server_obj.id

    @staticmethod
    def create_group_server_relation(group_id, server_id):
        """
        Adds a new GroupServerRelationship object
        Args:
                group_id: {group_id}
                server_id: {server_id}
        Returns:
                False
        """
        settings.API_LOGGER.info(
            "group_server_relation_manager - creating group server relation, server_id: {server_id}, group_id:{group_id}".format(
                server_id=server_id, group_id=group_id
            )
        )
        with transaction.atomic():
            GroupServerRelationship.objects.create(
                group_id=group_id, server_id=server_id
            )

    @staticmethod
    def manage_default_number(group_id, group_data):
        caller_id = group_data.get("caller_id", None)
        last_digit = group_data.get("last_digit", None)
        if not caller_id:
            settings.API_LOGGER.error(
                "group_server_relation_manager - caller_id not found in data: {data}, group_id:{group_id}".format(
                    data=group_data, group_id=group_id
                )
            )
            return

        if not last_digit:
            settings.API_LOGGER.error(
                "group_server_relation_manager - last_digit not found in data: {data}, group_id:{group_id}".format(
                    data=group_data, group_id=group_id
                )
            )
            return

        shared_did = "{caller_id}{last_digit}".format(
            caller_id=caller_id, last_digit=last_digit
        )

        number_filter_obj = Number.objects.filter(number=shared_did)
        if not number_filter_obj.exists():
            with transaction.atomic():
                Number.objects.create(
                    number=shared_did,
                    number_priority=1,
                    group_id=group_id,
                    is_fix_did=Number.NO,
                )

            settings.API_LOGGER.info(
                "group_server_relation_manager - creating shared Number, number: {did}, group_id:{group_id}".format(
                    did=shared_did, group_id=group_id
                )
            )

    @staticmethod
    def disable_groups(kam_group_ids_list):
        """
        Disables all groups not present in given list
        Args:
                kam_group_ids_list: ["1", "2"]
        Returns:
                False
        """
        if not kam_group_ids_list:
            return
        settings.API_LOGGER.info(
            "group_server_relation_manager - Disabling all groups excluding, kam_group_ids: {kam_group_ids_list}".format(
                kam_group_ids_list=kam_group_ids_list
            )
        )
        Group.objects.exclude(
            kam_group_id__in=kam_group_ids_list
        ).invalidated_update(is_enable=Group.NO, updated_on=datetime.now())

    @staticmethod
    def disable_servers(kam_server_names_list):
        """
        Disables all serevrs not present in given list
        Args:
                kam_server_names_list: ["s1.voicetree.info"]
        Returns:
                False
        """
        if not kam_server_names_list:
            return
        settings.API_LOGGER.info(
            "group_server_relation_manager - Disabling all servers excluding, kam_server_names: {kam_server_names_list}".format(
                kam_server_names_list=kam_server_names_list
            )
        )
        Server.objects.exclude(
            name__in=kam_server_names_list
        ).invalidated_update(is_enable=Server.NO, updated_on=datetime.now())
