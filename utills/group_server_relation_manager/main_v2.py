import copy
from typing import Dict

from handlers.models import Group
from utills.external_apis import ExternalAPIs
from utills.group_server_relation_manager.main import (
    GroupServerRelationManager,
)
from utills.helpers.helper import Helper


class GroupServerRelationManager_V2(GroupServerRelationManager):
    @staticmethod
    def get_pilot_number_details(pilot_number: str) -> Dict:
        data = ExternalAPIs.talk_to_pilot_number_list_api(
            pilot_number=pilot_number
        )
        if data:
            for detail in data["data"]:
                return detail
        else:
            return dict()

    @staticmethod
    def get_server_group_type_via_pilot_number(pilot_number: str) -> bool:
        if not pilot_number:
            return False
        pilot_data = GroupServerRelationManager_V2.get_pilot_number_details(
            pilot_number
        )

        server_group = GroupServerRelationManager_V2.get_server_group_type_from_pilot_data(
            pilot_data
        )

        return server_group

    @staticmethod
    def get_server_group_type_from_pilot_data(pilot_data: Dict):
        if pilot_data and pilot_data.get("servers"):
            for server in pilot_data["servers"]:
                if server.get("server_group") in Group.AWS_GROUP_TYPES:
                    return server["server_group"]

        return Group.PHY_GROUP_TYPE

    @staticmethod
    def add_group_servers(
        kam_group_id: str, group_data: Dict, pilot_number: str
    ):
        assigned_queues = {}
        group_type = GroupServerRelationManager_V2.get_server_group_type_via_pilot_number(
            pilot_number
        )
        if group_type not in Group.AWS_GROUP_TYPES:
            assigned_queues = Helper.get_group_assigned_queues(kam_group_id)

        group_name = group_data.pop("name")
        group_alias = group_data.pop("group_alias")
        region = group_data.pop("region")
        group_servers = group_data.pop("servers")
        group_data["group_type"] = group_type
        group_id = GroupServerRelationManager.create_group(
            group_name,
            assigned_queues,
            kam_group_id,
            group_data,
            group_alias,
            region,
        )
        if not group_id:
            return
        GroupServerRelationManager.manage_servers(group_id, group_servers)
        GroupServerRelationManager.manage_default_number(group_id, group_data)

    @staticmethod
    def update_group_servers(
        group_obj: Group, kam_group_id: str, group_data: Dict
    ):
        group = group_obj.first()

        group_type = Group.PHY_GROUP_TYPE
        if group_data.get("pilot_number"):
            group_type = GroupServerRelationManager_V2.get_server_group_type_via_pilot_number(
                group_data["pilot_number"]
            )

        group_name = group_data.pop("name")
        group_alias = group_data.pop("group_alias")
        region = group_data.pop("region")
        group_servers = group_data.pop("servers")

        group_data["group_type"] = group_type

        old_settings = dict(group.settings or {})
        old_settings = copy.deepcopy(old_settings)
        old_settings.update(group_data)

        group.is_enable = Group.YES
        group.name = group_name
        group.group_alias = group_alias
        group.region = region
        group.settings = old_settings
        group.save(
            update_fields=[
                "is_enable",
                "name",
                "group_alias",
                "region",
                "settings",
                "updated_on",
            ]
        )

        GroupServerRelationManager.manage_servers(group.id, group_servers)
        GroupServerRelationManager.manage_default_number(group.id, group_data)
