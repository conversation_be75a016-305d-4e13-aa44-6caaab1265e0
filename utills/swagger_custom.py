# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import os

from django.conf import settings
from django.conf.urls import url
from django.conf.urls import include
from django.conf.urls.static import static
from django.contrib import admin
from django.views.static import serve

from drf_yasg import openapi
from rest_framework import permissions
from drf_yasg.generators import OpenAPISchemaGenerator
from drf_yasg.views import get_schema_view

from api.views import IvrInfoDiscovery, ServerGroups, CompanyUsageCheck, GroupIVR


class SchemaGenerator(OpenAPISchemaGenerator):
	def get_schema(self, request=None, public=False):
		schema = super(SchemaGenerator, self).get_schema(request, public)
		schema.basePath = os.path.join(schema.basePath, 'api/1/')
		return schema


# For swagger doc

docs_urls=[
	url(r'^ivrinfo', IvrInfoDiscovery.as_view(), name='api.ivr_info'),
	url(r'^group-server', ServerGroups.as_view(), name='api.group_server'),
	url(r'^companyusage-check', CompanyUsageCheck.as_view(), name='api.comapny_usage_check'),
	url(r'^group-ivr', GroupIVR.as_view(), name='api.group_ivr'),

]

schema_view = get_schema_view(
	openapi.Info(
		title="Central APIs ",
		default_version='1',
		description=".",
	),
	public=True,
	permission_classes=(permissions.AllowAny,),#(permissions.IsAuthenticated,)
	patterns=docs_urls,
	# urlconf="management_server.api.urls",
	generator_class=SchemaGenerator,
)
