import os

from django.conf import settings

from .import conf

class S3Manager:
	@staticmethod
	def upload(_file, bucket, s3_name=None, is_delete=False):
		"""
		uploads a file to s3
		Parameters:
			_file: file to uplaod
			bucket: bucket name
			s3_name(optional): name of file on s3
			is_delete: True or False
		Returns:
			str: "https://sqs.ap-south-1.amazonaws.com/472952060482/obd_central_company_usage_check"
		"""

		if not s3_name: s3_name = _file.split('/')[-1]

		conf.s3_resource.meta.client.upload_file(_file, bucket, s3_name)
		settings.API_LOGGER.info("S3Manager - uploading file: {file}".format(file=_file))

		if is_delete:
			settings.API_LOGGER.info("S3Manager - removing file: {file}".format(file=_file))
			os.system("rm {}".format(_file))