from importlib import import_module

from django.conf import settings

engine = import_module(settings.SESSION_ENGINE)


class ProcessExceptionMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        return self.get_response(request)

    def process_exception(self, request, exception):
        settings.API_LOGGER.error(
            "Middleware Error message exception {},request {}, Get request-param{}, Post request-param{}".format(
                exception,
                request,
                dict(request.GET.items()),
                dict(request.POST.items()),
            )
        )
