from typing import Optional

from django.core.exceptions import ValidationError


class UDCLockedException(ValidationError):
    def __init__(self, message, udc_response, **kwargs) -> None:
        super().__init__(message, **kwargs)
        self.response = udc_response


class UDCUnavailable(Exception):
    pass


class DIDSyncException(Exception):

    def __init__(
        self,
        message=None,
        is_deletable=True,
        log_exception=True,
        *args,
    ) -> None:
        super().__init__(*args)
        self.is_deletable = is_deletable
        if message:
            self.message = message
        else:
            self.message = ""
        self.log_exception = log_exception


class GroupUnavailableException(Exception):
    def __init__(self, *args: object, cancel_request: bool = False) -> None:
        super().__init__(*args)
        self.cancel_request = cancel_request


class DestinationResponseException(Exception):
    def __init__(
        self,
        *args: object,
        response: Optional[str] = None,
        status_code: int = None,
        retry: bool = False,
    ) -> None:
        super().__init__(*args)

        if bool(response) != bool(status_code):
            raise ValueError(
                "Both response and status_code must be provided together, or neither should be provided."
            )

        self.response = response
        self.status_code = str(status_code)
        self.retry = retry
