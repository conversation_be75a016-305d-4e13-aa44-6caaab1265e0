from django.conf import settings

from cacheops import cached

from central.settings.handlers import conf
from handlers.models import Group, GroupIvrInfoRelationship, IvrInfo, Number
from utills.sqs_manager.main import SQSManager

# from cacheops import cached, cached_view


class GroupQueuesRoutingManager:
    def assign_group(self, req_body):
        self.filter1 = "caller_id"
        self.filter2 = "region"
        self.filter3 = "group"

        self.failover = "failover"
        self.shared_backup = "shared_backup"

        self.pq_key = "np_q"
        self.req_body = req_body

        self.ivr_info = IvrInfo.objects.filter(
            ivr_id=self.req_body["ivr_id"]
        ).first()

        priority = self.ivr_info.ivr_priority or 5
        if int(priority) in conf.CENTRAL_HIGH_PRIORITIES:
            self.pq_key = "p_q"

        if (
            (self.filter1 in self.req_body)
            or (self.filter2 in self.req_body)
            or (self.filter3 in self.req_body)
        ):
            return self.handle_filter_given_case()

        return self.handle_no_filter_given_case()

    def handle_filter_given_case(self):
        avail_group = {}

        # case 1: getting group based on given filters
        gp_condition = self.get_group_filter_condition()

        group_ivr_obj = GroupIvrInfoRelationship.objects.filter(**gp_condition)

        if group_ivr_obj.exists():
            group_ivr = group_ivr_obj.all().order_by("group__group_priority")
            groups = [g_ivr.group for g_ivr in group_ivr]
            if self.filter1 in self.req_body:
                avail_group = self.get_avail_group(
                    groups, True, self.req_body[self.filter1]
                )
            else:
                avail_group = self.get_avail_group(groups, True)
            # returning avail_group even if it is empty, it will get process in next execution.
            return avail_group

        settings.API_LOGGER.info(
            "group_queues_routing_manager - request_id: {request_id}, ivr_id: {ivr_id}, group not found for given filter(s): {filters}".format(
                request_id=self.req_body["request_id"],
                filters=gp_condition,
                ivr_id=self.req_body["ivr_id"],
            )
        )

        # case 2: failover check, cancel request if failover is not enabled.
        if not self.is_setting_enable(self.failover):
            settings.API_LOGGER.info(
                "group_queues_routing_manager - request_id: {request_id}, ivr_id: {ivr_id}, failover is disabled!".format(
                    request_id=self.req_body["request_id"],
                    ivr_id=self.req_body["ivr_id"],
                )
            )

            avail_group["is_cancle_request"] = "1"
            return avail_group

        failover_groups = self.get_failover_groups()
        if failover_groups:
            avail_failover_group = self.get_avail_group(failover_groups, True)
            return avail_failover_group

        # case 3: shared_backup check, cancel request if shared_backup is not enabled.
        if not self.is_setting_enable(self.shared_backup):
            settings.API_LOGGER.info(
                "group_queues_routing_manager - request_id: {request_id}, ivr_id: {ivr_id}, failover group not found and shared_backup is also disabled!".format(
                    request_id=self.req_body["request_id"],
                    ivr_id=self.req_body["ivr_id"],
                )
            )

            avail_group["is_cancle_request"] = "1"
            return avail_group

        default_groups_obj = self.get_default_groups()
        if not default_groups_obj.exists():
            settings.API_LOGGER.error(
                "group_queues_routing_manager - request_id: {request_id}, ivr_id:{ivr_id}, default groups not found in db!".format(
                    request_id=self.req_body["request_id"],
                    ivr_id=self.req_body["ivr_id"],
                )
            )
            return avail_group

        avail_group = self.get_avail_group(default_groups_obj.all())

        return avail_group

    def handle_no_filter_given_case(self):
        avail_group = {}

        # case 1: getting groups based on other fix_dids of given IVR(failover-dids).
        failover_groups = self.get_failover_groups()
        if failover_groups:
            avail_failover_group = self.get_avail_group(failover_groups, True)
            return avail_failover_group

        # failover dids not found, cancelling request if failover is not enabled.
        if not self.is_setting_enable(self.failover):
            settings.API_LOGGER.info(
                "group_queues_routing_manager - request_id: {request_id}, ivr_id: {ivr_id}, failover is disabled!".format(
                    request_id=self.req_body["request_id"],
                    ivr_id=self.req_body["ivr_id"],
                )
            )

            avail_group["is_cancle_request"] = "1"
            return avail_group

        settings.API_LOGGER.info(
            "group_queues_routing_manager - request_id: {request_id}, ivr_id: {ivr_id}, failover fixdid not found!".format(
                request_id=self.req_body["request_id"],
                ivr_id=self.req_body["ivr_id"],
            )
        )

        # temp-off not major cases coming in this case
        # # case 2: failover is enabled, and fix-did is not available, checking for non-fix did mappings
        # non_failover_groups = self.get_non_failover_groups()
        # if non_failover_groups:
        # 	avail_group = self.get_avail_group(non_failover_groups)
        # 	if avail_group: return avail_group

        # case 3: shared_backup check, cancel request if shared_backup is not enabled.
        if not self.is_setting_enable(self.shared_backup):
            settings.API_LOGGER.info(
                "group_queues_routing_manager - request_id: {request_id}, ivr_id:{ivr_id}, releted groups not found and shared backup is disable!".format(
                    request_id=self.req_body["request_id"],
                    ivr_id=self.req_body["ivr_id"],
                )
            )
            avail_group["is_cancle_request"] = "1"
            return avail_group

        settings.API_LOGGER.info(
            "group_queues_routing_manager - request_id: {request_id}, ivr_id: {ivr_id}, releted groups not found getting shared numbers!".format(
                request_id=self.req_body["request_id"],
                ivr_id=self.req_body["ivr_id"],
            )
        )

        default_groups_obj = self.get_default_groups()
        if not default_groups_obj.exists():
            settings.API_LOGGER.error(
                "group_queues_routing_manager - request_id: {request_id}, ivr_id:{ivr_id}, default groups not found in db!".format(
                    request_id=self.req_body["request_id"],
                    ivr_id=self.req_body["ivr_id"],
                )
            )
            return avail_group

        avail_group = self.get_avail_group(default_groups_obj.all())
        return avail_group

    def is_setting_enable(self, setting):
        obd_v2_settings = self.req_body.get("obd_v2_settings", {})
        if (
            setting in obd_v2_settings
            and str(obd_v2_settings.get(setting, None)) == "1"
        ):
            return True
        return False

    def get_avail_group(self, groups, only_fixdid=False, source_number=False):
        group_data = {}

        if not groups:
            return group_data

        # groups = groups.order_by('?')

        for group in groups:
            if self.is_group_resource_avail(group.get_assigned_queues):
                if not source_number:
                    if only_fixdid:
                        condition = {
                            "group_id": group.id,
                            "is_fix_did": Number.YES,
                            "groupivrinforelationship__ivr_info__ivr_id": self.req_body[
                                "ivr_id"
                            ],
                        }
                    else:
                        condition = {
                            "group_id": group.id,
                            "is_fix_did": Number.NO,
                        }

                    source_number_obj = Number.objects.filter(
                        **condition
                    ).order_by("number_priority")

                    # if source_number_obj.count()>1:
                    # 	source_number_obj = self.get_number_attached_with_ivr(condition, self.req_body['ivr_id'])

                    if not source_number_obj.exists():
                        settings.API_LOGGER.error(
                            "group_queues_routing_manager - request_id: {request_id}, ivr_id: {ivr_id}, number not found for:{condition}".format(
                                request_id=self.req_body["request_id"],
                                condition=condition,
                                ivr_id=self.req_body["ivr_id"],
                            )
                        )
                        continue
                    source_number = source_number_obj.first().number

                group_data = {
                    "url": group.get_assigned_queues[self.pq_key]["url"],
                    "name": group.get_assigned_queues[self.pq_key]["name"],
                    "ivr_settings": self.ivr_info.common_setting,
                    "ivr_type": self.ivr_info.ivr_type,
                    "source_number": source_number,
                    "group_settings": group.settings,
                }
                break

        return group_data

    # @file_cache.cached(timeout=180)
    @cached(timeout=180)
    def get_number_attached_with_ivr(self, condition, ivr_id):
        return Number.objects.filter(**condition).order_by("?").nocache()

    def is_group_resource_avail(self, assigned_queues):
        try:
            group_url = assigned_queues[self.pq_key]["url"]
            threshold = assigned_queues[self.pq_key]["threshold"]
            resources = SQSManager.get_queue_attributes(group_url)[
                "ApproximateNumberOfMessages"
            ]
            settings.API_LOGGER.info(
                "group_queues_routing_manager - request_id: {request_id}, ivr_id: {ivr_id}, sqs_msg_count: {resources}, group_url: {group_url}".format(
                    request_id=self.req_body["request_id"],
                    resources=resources,
                    group_url=group_url,
                    ivr_id=self.req_body["ivr_id"],
                )
            )
            if int(threshold) - int(resources) > 0:
                return True
            settings.API_LOGGER.info(
                "group_queues_routing_manager - request_id: {request_id}, ivr_id: {ivr_id}, resource not available, group_url: {group_url}".format(
                    request_id=self.req_body["request_id"],
                    group_url=group_url,
                    ivr_id=self.req_body["ivr_id"],
                )
            )
        except Exception as e:
            settings.API_LOGGER.info(
                "group_queues_routing_manager - request_id: {request_id}, ivr_id: {ivr_id}, exception occurred!, e: {e}".format(
                    request_id=self.req_body["request_id"],
                    ivr_id=self.req_body["ivr_id"],
                    e=e,
                )
            )

        return False

    def get_group_filter_condition(self):
        if self.filter3 in self.req_body:
            try:
                get_group_name = Group.objects.filter(
                    kam_group_id=self.req_body[self.filter3].split("-")[-1]
                )
                get_group_name = get_group_name.first().group_alias
            except Exception as e:
                get_group_name = self.req_body[self.filter3]
                pass

        if self.filter1 in self.req_body:
            condition = {
                "ivr_info__ivr_id": self.req_body["ivr_id"],
                "number__is_fix_did": Number.YES,
                "group__is_enable": Group.YES,
                "number__number": str(self.req_body[self.filter1]).lstrip("0"),
            }
        elif (self.filter2 in self.req_body) and (
            self.filter3 in self.req_body
        ):
            condition = {
                "ivr_info__ivr_id": self.req_body["ivr_id"],
                "number__is_fix_did": Number.YES,
                "group__is_enable": Group.YES,
                "group__region": self.req_body[self.filter2],
                "group__group_alias": get_group_name,
            }
        elif self.filter2 in self.req_body:
            condition = {
                "ivr_info__ivr_id": self.req_body["ivr_id"],
                "number__is_fix_did": Number.YES,
                "group__is_enable": Group.YES,
                "group__region": self.req_body[self.filter2],
            }
        elif self.filter3 in self.req_body:
            condition = {
                "ivr_info__ivr_id": self.req_body["ivr_id"],
                "number__is_fix_did": Number.YES,
                "group__is_enable": Group.YES,
                "group__group_alias": get_group_name,
            }
        else:
            pass

        return condition

    # @file_cache.cached(timeout=100)
    @cached(timeout=100)
    def get_default_groups(self):
        return (
            Group.objects.filter(is_enable=Group.YES, is_default=Group.YES)
            .order_by("?")
            .nocache()
        )

    def get_failover_groups(self):
        group_ivrinfo_obj = GroupIvrInfoRelationship.objects.filter(
            group__is_enable=Group.YES,
            number__is_fix_did=Number.YES,
            ivr_info__ivr_id=self.req_body["ivr_id"],
        )
        group_queryset = list(
            group_ivrinfo_obj.values_list("group_id", flat=True).distinct()
        )
        if group_ivrinfo_obj.exists():
            groups_obj = self.get_groups_by_ids(group_queryset)
            # groups_obj = Group.objects.filter(id__in=group_queryset)
            if groups_obj.exists():
                return groups_obj
        return []

    def get_non_failover_groups(self):
        # for purpose of giving another chance if any event comes not adding number__is_fix_did=Number.No,
        group_ivrinfo_obj = GroupIvrInfoRelationship.objects.filter(
            group__is_enable=Group.YES,
            ivr_info__ivr_id=self.req_body["ivr_id"],
        )
        group_queryset = list(
            group_ivrinfo_obj.values_list("group_id", flat=True).distinct()
        )
        if group_ivrinfo_obj.exists():
            # groups_obj = self.get_groups_by_ids(group_queryset)
            groups_obj = Group.objects.filter(id__in=group_queryset)
            if groups_obj.exists():
                return groups_obj
        return []

    # @file_cache.cached(timeout=900)
    @cached(timeout=900)
    def get_groups_by_ids(self, group_queryset):
        return (
            Group.objects.filter(id__in=group_queryset).order_by("?").nocache()
        )
