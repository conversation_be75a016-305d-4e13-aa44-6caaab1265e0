from typing import Dict, Iterable, Optional

from django.conf import settings
from django.db import transaction

from handlers.models import CentralRequestInfo, Group, Number
from utills.cache_manager.main import ObdInternalAPIStatusCacheHandler
from utills.group_queues_routing_manager.main import GroupQueuesRoutingManager


class GroupHandler_V2(GroupQueuesRoutingManager):
    def __init__(self, request: CentralRequestInfo):
        self.request: CentralRequestInfo = request
        self.obd_api_avail_cache = None

    def get_obd_api_avail_cache(
        self,
    ) -> ObdInternalAPIStatusCacheHandler:
        if not self.obd_api_avail_cache:
            self.obd_api_avail_cache = ObdInternalAPIStatusCacheHandler()

        return self.obd_api_avail_cache

    def assign_group(self) -> Optional[Dict]:
        return super().assign_group(self.request.body)

    def get_source_number(
        self, group: Group, only_fix_did: bool
    ) -> Optional[str]:
        if only_fix_did:
            condition = {
                "group_id": group.pk,
                "is_fix_did": Number.YES,
                "groupivrinforelationship__ivr_info__ivr_id": self.request.ivr_id,
            }
        else:
            condition = {
                "group_id": group.pk,
                "is_fix_did": Number.NO,
            }

        with transaction.atomic():
            source_number_obj = Number.objects.filter(**condition).order_by(
                "number_priority"
            )

            if not source_number_obj.exists():
                settings.API_LOGGER.error(f"number not found for:{condition}")
                return
            return source_number_obj.first().number

    def get_avail_group(
        self,
        groups: Iterable[Group],
        only_fixdid: bool = False,
        source_number: str = None,
    ) -> Optional[Dict]:
        for group in groups:
            # Either the group is AWS type and API is working, or group queues are available
            if (
                group.is_type_aws()
                and not self.get_obd_api_avail_cache().is_failed()
            ) or self.is_group_resource_avail(group.get_assigned_queues):
                source_number = source_number or self.get_source_number(
                    group, only_fix_did=only_fixdid
                )
                if not source_number:
                    continue

                return self.get_group_data(group, source_number)

    def get_group_data(self, group: Group, source_number: str) -> Dict:
        if not group:
            return {}

        if group.is_type_aws():
            return {
                "source_number": source_number,
                "is_aws": True,
                "ivr_type": self.ivr_info.ivr_type,
            }
        else:
            return {
                "url": group.get_assigned_queues[self.pq_key]["url"],
                "name": group.get_assigned_queues[self.pq_key]["name"],
                "ivr_settings": self.ivr_info.common_setting,
                "ivr_type": self.ivr_info.ivr_type,
                "source_number": source_number,
                "group_settings": group.settings,
                "is_aws": False,
            }
