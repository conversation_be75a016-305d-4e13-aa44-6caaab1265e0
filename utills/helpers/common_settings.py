import json
import logging
from django.conf import settings
from handlers.models import CommonSetting

logger = logging.getLogger(__name__)


class ObdInternalAPICommonSettings:
    entity = "obd-internal-api"
    CACHE_TTL_KEY = "OBD_API_AVAIL_CACHE_TTL"

    def get_obd_internal_api_common_settings(self) -> CommonSetting:
        obj, created = CommonSetting.objects.get_or_create(entity=self.entity)
        if created:
            obj.settings = json.dumps(
                {self.CACHE_TTL_KEY: settings.OBD_API_AVAIL_CACHE_TTL}
            )
            obj.save(update_fields=["updated_on", "settings"])
        return obj

    def get_cache_ttl(self) -> int:
        obj = self.get_obd_internal_api_common_settings()
        try:
            return int(
                obj.get_settings.get(
                    self.CACHE_TTL_KEY, settings.OBD_API_AVAIL_CACHE_TTL
                )
            )
        except ValueError:
            logger.error(
                "ValueError while fetching OBD_API_AVAIL_CACHE_TTL",
                exc_info=True,
            )
            return settings.OBD_API_AVAIL_CACHE_TTL


class TrueCallerCommonSettings:
    entity = "true_caller"
    IS_ENABLED = settings.TRUE_CALLER_ENABLED

    def get_true_caller_common_settings(self) -> CommonSetting:
        obj, created = CommonSetting.objects.get_or_create(entity=self.entity)
        if created:
            obj.settings = json.dumps(
                settings.TRUE_CALLER_DEFAULT_COMMON_SETTINGS
            )
            obj.save(update_fields=["updated_on", "settings"])

        return obj

    def get_company_id(self) -> list:
        obj = self.get_true_caller_common_settings()
        return obj.get_settings.get("company_ids", [])

    def is_enabled(self) -> bool:
        if not self.IS_ENABLED:
            return False
        obj = self.get_true_caller_common_settings()
        return obj.get_settings.get("enabled", False)

    def is_enabled_for_company(self, company_id: str) -> bool:
        if not self.is_enabled():
            return False
        company_ids = self.get_company_id()
        logger.debug(
            f"TrueCallerCommonSettings - is_enabled_for_company - company_ids: {company_ids}, company_id: {company_id}"
        )
        if not company_ids:
            return False
        return company_id in company_ids

    def remove_object(self):
        # this is to cleanup.
        CommonSetting.objects.filter(entity=self.entity).delete()
