import json
import logging
from typing import Dict, Union

from django.conf import settings
from rest_framework import status

from handlers.models import CentralRequestInfo
from utills.helpers.helper import Helper

logger = logging.getLogger(__name__)


class ProcessManagerHelper:
    def prepare_response(self, response: Union[dict, str]):
        return json.dumps(response) if isinstance(response, dict) else response

    def invoke_process_manager_with_api_response(
        self,
        cri_obj: CentralRequestInfo,
        response: Union[Dict, str],
        status_code: str = str(status.HTTP_200_OK),
        success=True,
    ):
        logger.info("Sending request to process_manager!!")
        message = self.prepare_response(response)
        if success:
            self._invoke_process_manager(
                cri_obj.request_id,
                cri_obj.c_id,
                cri_obj.ivr_id,
                status_code,
                service_name=settings.OBD_SERVICES["source_name"],
                message=message,
            )

        else:
            self._invoke_process_manager(
                cri_obj.request_id,
                cri_obj.c_id,
                cri_obj.ivr_id,
                status_code,
                service_name=settings.OBD_SERVICES["cancel_service"],
                failure_reason=message,
            )

    def _invoke_process_manager(
        self,
        request_id: str,
        company_id: str,
        ivr_id: str,
        status_code: str,
        service_name: str,
        message: str = "",
        failure_reason: str = "",
    ):
        pm_response = Helper.make_pm_response(
            settings.OBD_SERVICES["source_name"], failure_reason, message
        )
        logger.info(f"Sending event to pm: {pm_response}")
        Helper.invoke_process_manager(
            request_id,
            company_id,
            ivr_id,
            pm_response,
            status_code,
            service_name,
        )
