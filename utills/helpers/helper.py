import json
import random
import re
import string
import sys
import time
from datetime import datetime

from django.conf import settings
from django.db import connections, transaction

from central.settings.handlers import conf
from handlers.models import CommonSetting, IvrInfo, SQSQueueInfo
from utills.cache_manager.main import CacheManager
from utills.sqs_manager.main import SQSManager


class ApiInstanceMocker:
    """
    Used to create a mock of DB instance for APIs with given data as kwargs.
    Args:
        **kwargs
    """

    def __init__(self, **kwargs):
        id = Helper.random_string(20)
        self.__dict__ = dict(kwargs)
        self.__dict__.update({"id": id, "request_id": id})


class Helper:
    @staticmethod
    def get_threshold(entity, default, default_name="threshold"):
        """
        Checks if the threshold exists in DB for any given entity, if not exist then:
            adds the default in DB and returns it.
        Parameters:
            entity: 'central_1'
            default: {'self': 50, 'obd-central-pq2': 10, 'obd-central-pq3': 50}
        Returns:
            dict: {'self': 50, 'obd-central-pq2': 10, 'obd-central-pq3': 50} - in case of central_1
        """
        common_settings_obj = CommonSetting.objects.filter(entity=entity)
        if common_settings_obj.exists():
            common_settings = common_settings_obj.first()
            settings_dict = common_settings.get_settings
            return settings_dict[default_name]

        settings.API_LOGGER.info(
            "helper - CommonSettings not found in DB, adding default from config. Entity: {entity}".format(
                entity=entity
            )
        )
        with transaction.atomic():
            CommonSetting.objects.create(
                entity=entity, settings=json.dumps({default_name: default})
            )
        return default

    @staticmethod
    def get_queue_name_url_mapping(key, value, gateway=False):
        """
        It returns the dict of name URL mapping. based on the given key and value as a condition.
        If the queue entry not found in DB, in that case, it will create a new entry in DB - if we pass key as queue_name and also pass gateway for the new entry.
        If the queue_url not found in DB, then it gets it using SQSManager and also updates the DB.
        Parameters:
            key: name of condittion column
            value: value for condition column
            gateway: if an IVR queue_name is not found in db then this is used to create one.
            eg: key: gateway_prefix, value: obd-central-pq
        Returns:
            dict: {'obd-central-pq1': 'https://ap-south-1.queue.amazonaws.com/472952060482/obd-central-pq1', 'obd-central-pq2': 'https://ap-south-1.queue.amazonaws.com/472952060482/obd-central-pq2'}
        """

        queues_info = SQSQueueInfo.objects.filter(**{key: value}).values(
            "queue_name", "queue_url"
        )

        if not queues_info and gateway and key == "queue_name":
            settings.API_LOGGER.info(
                "{value} not found in SQSQueueInfo, creating new entry.".format(
                    value=value
                )
            )
            queue_url = SQSManager.get_queue_urls(value)

            if len(queue_url) <= 0:
                settings.API_LOGGER.error(
                    "Queue URL not found for key - {key}, value - {value}".format(
                        key=key, value=value
                    )
                )
                return {value: None}

            try:
                with transaction.atomic():
                    SQSQueueInfo.objects.create(
                        gateway_prefix=gateway,
                        queue_name=value,
                        queue_url=queue_url[0],
                    )
            except Exception as e:
                settings.API_LOGGER.info(
                    "SQSQueueInfo error occured - e: {e} ".format(e=e)
                )

            return {value: queue_url[0]}

        for queue in queues_info:
            if queue["queue_url"] is None or queue["queue_url"] == "":
                settings.API_LOGGER.info(
                    "QueueUrl not found in SQSQueueInfo for '{key}': '{value}'.".format(
                        key=key, value=value
                    )
                )
                queue_url = SQSManager.get_queue_urls(queue["queue_name"])
                SQSQueueInfo.objects.filter(
                    queue_name=queue["queue_name"]
                ).invalidated_update(
                    queue_url=queue_url[0], updated_on=datetime.now()
                )
                queue["queue_url"] = queue_url[0]

        return {
            queue["queue_name"]: queue["queue_url"] for queue in queues_info
        }

    @staticmethod
    def invoke_process_manager(
        req_id, c_id, ivr_id, response, response_status, service_name
    ):
        """
        Pushes data to *Process Manager* queue.
        Parameters:
            req_id: req_id
            c_id: c_id
            response: '{"emitter": "", "failure_reason": ""}'
            response_status: 200
            service_name: call or expiry_manager or response_manager
        Returns:
            None
        """

        body = {
            "request_id": req_id,
            "company_id": c_id,
            "ivr_id": ivr_id,
            "response": response,
            "response_status": response_status,
            "service_name": service_name,
            "context": "service_invoker",
        }
        pm_q_url = Helper.get_queue_name_url_mapping(
            "queue_name",
            conf.PROCESS_MANAGER_QUEUE,
            conf.PROCESS_MANAGER_GATEWAY_PREFIX,
        )

        SQSManager.sqs_fifo_pusher(
            pm_q_url[conf.PROCESS_MANAGER_QUEUE], json.dumps(body)
        )

    @staticmethod
    def make_pm_response(emitter, failure_reason, message):
        """
        Make a json string response for process manager
        Parameters:
            emitter: call
            failure_reason: process_flow not found or global cancellation
            message: additional message
        Returns:
            response: '{"emitter": "call", "failure_reason": "process_flow not found", "message": ""}'
        """
        response = {
            "emitter": emitter,
            "failure_reason": failure_reason,
            "message": message,
        }
        return response

    @staticmethod
    def random_string(stringLength=10):
        """
        Generates a Random String of given length.
        Parameters:
            stringLength: string length
        Returns:
            string
        """
        letters = string.ascii_lowercase
        return "".join(random.choice(letters) for i in range(stringLength))

    @staticmethod
    def retry_if_result_none(result):
        """
        Return True if we should retry (in this case when the result is None), False otherwise
        """
        return result is None

    @staticmethod
    def exit_handler(tts=10):
        time.sleep(tts)
        sys.exit()

    @staticmethod
    def is_ivr_rule_allowing_call(rule_api_data, ivr_id):
        """
        Parameters:
            rule_api_data: { is_call_allowed: 0, slots: { 2020-07-28: { } } }
        Returns:
            bool
        """
        is_call_allowed = rule_api_data.get("validated", None)

        if is_call_allowed:
            return True

        if is_call_allowed is None:
            settings.API_LOGGER.error(
                "rule-api unexpected response, is_call_allowed not found in response, response: {res}".format(
                    res=rule_api_data
                )
            )
            return False

        log_msg = "helper - ignoring IVR: {ivr_id} because of ivr_rule time: {ivr_rule}".format(
            ivr_id=ivr_id, ivr_rule=rule_api_data
        )
        Helper.sampling_logger_emitter(
            msg=log_msg, sampling_probability=0.002, is_enable=True
        )
        return False

    @staticmethod
    def get_group_assigned_queues(name):
        """
        Creates pq and npq for the given group name, and also add an entry to SQSQueueInfo.
        If the queue already exists then create_queue method returns the URL of the existing queue,
        and also cross-checking in SQSQueueInfo for entry.
        Parameters:
            name:{name}
        Returns:
            dict: {'p_q': {'name': '{gateway}_p_q_{name}',
                'url': 'https://ap-south-1.queue.amazonaws.com/472952060482/gateway_p_q-{name}',
                'threshold': 10},
                'np_q': {'name': 'gateway_np_q-{name}',
                'url': 'https://ap-south-1.queue.amazonaws.com/472952060482/gateway_np_q-{name}',
                'threshold': 30}}
        """
        threshold = Helper.get_threshold(
            conf.GROUPS_ENTITY, conf.GROUP_QUEUE_THRESHOLD
        )
        gateway = conf.GROUPS_GATEWAY
        p_q = conf.GROUPS_PQ_KEY
        np_q = conf.GROUPS_NPQ_KEY
        response = {}
        for q in [p_q, np_q]:
            queue_name = f"{gateway}_{q}_{name}"
            attributes = {
                "VisibilityTimeout": str(conf.GROUP_Q_VISIBILITY_TIMEOUT),
                "MessageRetentionPeriod": str(conf.GROUP_Q_RETENTION_PERIOD),
            }
            queue_url = SQSManager.create_queue(
                queue_name, attributes=attributes
            )
            sqs_info_obj = SQSQueueInfo.objects.filter(queue_url=queue_url)
            if not sqs_info_obj.exists():
                with transaction.atomic():
                    SQSQueueInfo.objects.create(
                        gateway_prefix=gateway,
                        queue_name=queue_name,
                        queue_url=queue_url,
                    )
            response[q] = {
                "name": queue_name,
                "url": queue_url,
                "threshold": threshold[q],
            }

        return response

    @staticmethod
    def get_max_workers(workers, handler):
        workers = int(workers)
        if workers < 1:
            workers = 1
        elif workers > conf.MAX_ACCEPTED_HANDLER_THREADS:
            settings.API_LOGGER.error(
                "{h} - threads_count: {tc} is more then max allowed threads count:{mtc}".format(
                    tc=workers,
                    mtc=conf.MAX_ACCEPTED_HANDLER_THREADS,
                    h=handler,
                )
            )
            workers = conf.MAX_ACCEPTED_HANDLER_THREADS
        return workers

    @staticmethod
    def get_actual_url(conf_raw_url, data_dict):
        data_dict = dict((re.escape(k), v) for k, v in data_dict.items())
        pattern = re.compile("|".join(data_dict.keys()))
        url = pattern.sub(
            lambda m: data_dict[re.escape(m.group(0))], conf_raw_url
        )
        return url

    @staticmethod
    def get_binary_choice(truthy_probability=0.5):
        sample_space = [0, 1]
        if truthy_probability > 1.0 or truthy_probability < 0.0:
            raise Exception("InvalidProbabilityProvidedError")
        falsy_probability = 1 - truthy_probability
        choice = random.choices(
            sample_space, [falsy_probability, truthy_probability]
        )
        return choice[0]

    @staticmethod
    def sampling_logger_emitter(
        msg,
        sampling_probability=conf.SAMPLING_PROBABILITY,
        extra=None,
        is_enable=False,
    ):
        if is_enable:
            choice = Helper.get_binary_choice(sampling_probability)
            if choice == 1:
                settings.API_LOGGER.info(msg, extra=extra)
            else:
                settings.API_LOGGER.tempinfo(msg, extra=extra)
        else:
            settings.API_LOGGER.tempinfo(msg, extra=extra)

    @staticmethod
    def get_client_ip(request):
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[-1].strip()
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip

    @staticmethod
    def get_ivrinfo_common_setting(ivr_id, key, default_value, max_value=None):
        ivr_info_obj = IvrInfo.objects.filter(ivr_id=ivr_id)
        if not ivr_info_obj.exists():
            settings.API_LOGGER.error(
                "helper: ivr_id: {ivr_id}, ivrinfo data not found!".format(
                    ivr_id=ivr_id
                )
            )
            return default_value

        ivr_info = ivr_info_obj.first()
        key_val = ivr_info.common_setting.get(key)
        key_val = "0" if key_val == 0 else key_val
        if key_val:
            try:
                if max_value:
                    if int(ivr_info.common_setting[key]) > int(max_value):
                        settings.API_LOGGER.error(
                            "helper: ivr_id: {ivr_id}, ivrinfo key: '{key}' value is greater then default value, updating ivr_info with defautl value, current_value: '{cv}', updated_value: '{uv}'".format(
                                ivr_id=ivr_id,
                                key=key,
                                cv=ivr_info.common_setting[key],
                                uv=default_value,
                            )
                        )
                        ivr_info.common_setting[key] = int(max_value)
                        ivr_info.save()
                return int(ivr_info.common_setting[key])
            except Exception:
                return ivr_info.common_setting[key]

        # changing msg to error will notify
        settings.API_LOGGER.info(
            "helper: ivr_id: {ivr_id}, ivrinfo key: '{key}' not found in common_setting, adding '{key}' with default data: '{default}'".format(
                ivr_id=ivr_id, key=key, default=default_value
            )
        )

        ivr_info.common_setting[key] = default_value
        ivr_info.save()

        return default_value

    def get_user_lock_duration(dial_reverse: int, ivr_id: str) -> int:
        if int(dial_reverse) == 1:
            user_lock_dur = Helper.get_ivrinfo_common_setting(
                ivr_id,
                conf.CACHE_UDC_LOCKED_USERS_AGENT_FIRST_TTL_SETTING_NAME,
                conf.CACHE_UDC_LOCKED_USERS_AGENT_FIRST_TTL,
            )
        else:
            user_lock_dur = Helper.get_ivrinfo_common_setting(
                ivr_id,
                conf.CACHE_UDC_LOCKED_USERS_DUR_SETTING_NAME,
                conf.CACHE_UDC_LOCKED_USERS_TTL,
            )
        if user_lock_dur == "0" or user_lock_dur <= 0:
            user_lock_dur = 1  # as ttl can not be less than 1 second.

        return user_lock_dur

    @staticmethod
    def update_ivrinfo_setting(key, value):
        ivr_info_qs = IvrInfo.objects.all()
        for ivr_info in ivr_info_qs:
            if not isinstance(ivr_info.common_setting, dict):
                ivr_info.common_setting = {}
            ivr_info.common_setting[key] = value
            ivr_info.save()

    @staticmethod
    def check_job_is_aborted(job_id):
        aborted_job_cache_key = (
            f"{conf.JP_JOB_ABORT_CENTRAL_CACHE_KEY}{job_id}"
        )
        value = CacheManager.get_value(aborted_job_cache_key)
        # will return true, if  value is not None, will return false, if value is None.
        return value is not None

    @staticmethod
    def close_db_connections():
        # Because each thread has a connection, so here you can call close_all() to close all connections under this thread name.
        connections.close_all()

    def get_toggle_view_only_value(ivr_id: str) -> int:
        ivr_info = IvrInfo.objects.filter(ivr_id=ivr_id).first()

        if ivr_info.ivr_type == IvrInfo.COC:
            return 1  # to avoid booking incase of COC, equals to book_user=0

        book_user = Helper.get_ivrinfo_common_setting(
            ivr_id,
            conf.BOOK_USER_TOGGLE_KEY,
            conf.DEFAULT_BOOK_USER_TOGGLE_VALUE,
        )

        match book_user:
            case 1:
                # book_user is enabled in req_data, then user will be booked at engine
                return 0
            case 0:
                # book_user is disabled in req_data, then user will not be booked at engine
                return 1
            case _:
                # book_user value is not between 0 and 1. then user will be booked at engine
                return 0
