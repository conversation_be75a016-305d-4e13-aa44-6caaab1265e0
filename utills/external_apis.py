import json
import traceback
from typing import Dict

from django.conf import settings
from rest_framework import status

import requests

from central.logging import get_config
from central.settings.handlers import conf
from handlers.models import IvrInfo
from utills.cache_manager.main import <PERSON>ache<PERSON>ana<PERSON>, TrueCallerTokenCacheHandler
from utills.exceptions import DestinationR<PERSON>ponseException
from utills.helpers.helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Helper
from utills.routing_manager.main import RoutingManger


class BaseAPI:
    @staticmethod
    def get_api(
        url,
        **kwargs,
    ):
        """
        Makes a GET request to the specified URL with parameters.

        Parameters:
            - url (str) : The URL to make the GET request to.
            - kwargs : Additional keyword arguments to customize the request.
                - headers (dict): A dictionary of headers to include in the request.
                - timeout (int): The timeout for the request in seconds (default: conf.EXTERNAL_API_TIMEOUT).
                - params (dict): A dictionary of URL parameters to include in the request.
                - json_data (dict): A JSON payload to include in the request.

        Returns:
            tuple: A tuple containing the JSON response and the status code as a string.
                - If the response is successful, the JSON response is returned along with the status code.
                - If an error occurs, an empty dictionary is returned along with the status code.
        """
        try:
            headers = kwargs.get("headers", {})

            config = get_config()
            trace_id = headers.get(settings.TRACE_ID_HEADER, config.uid)

            headers.update(
                {
                    settings.TRACE_ID_HEADER: trace_id,
                    "User-Agent": settings.USER_AGENT,
                }
            )
            settings.API_LOGGER.info(
                f"External API,request url- {url}, {kwargs}"
            )

            response = requests.get(
                url,
                timeout=kwargs.get("timeout", conf.EXTERNAL_API_TIMEOUT),
                verify=False,
                headers=headers,
                params=kwargs.get("params"),
                json=kwargs.get("json_data"),
            )
            try:
                json_res = response.json()
                settings.API_LOGGER.info(
                    f"external_apis: url: {url}, response={json_res}, status_code: {response.status_code}"
                )
                return json_res, str(response.status_code)
            except Exception as e:
                settings.API_LOGGER.error(
                    f"external_apis: api error, url: {url}, kwargs: {kwargs}, res:{response.text}, status_code:{response.status_code}, e: {e}",
                    extra={"status_code": response.status_code},
                )
                return {}, response.status_code
        except Exception as e:
            settings.API_LOGGER.error(
                f"external_apis: api error, kwargs: {kwargs} , url: {url}, e: {e}"
            )

        return {}, "500"

    @staticmethod
    def api(method, url, **kwargs):
        """
        Makes a  request to the specified URL with optional parameters.

        Parameters:
            - method (str) : Request Method, i.e : "get", "post"
            - url (str) : The URL to make the GET request to.
            - kwargs : Additional keyword arguments to customize the request.
                - headers (dict): A dictionary of headers to include in the request.
                - timeout (int): The timeout for the request in seconds (default: conf.EXTERNAL_API_TIMEOUT).
                - params (dict): A dictionary of URL parameters to include in the request.
                - json_data (str): A JSON payload(body) to include in the request.
                - set_default_headers (bool): Whether to set default headers like trace_id and User-Agent (default: True).


        Returns:
            tuple: A tuple containing the JSON response and the status code as a string.
                - If the response is successful, the JSON response is returned along with the status code.
                - If an error occurs, an empty dictionary is returned along with the status code.
        """

        try:
            headers = kwargs.get("headers", {})

            config = get_config()
            if kwargs.get("set_default_headers", True):
                trace_id = headers.get(settings.TRACE_ID_HEADER, config.uid)

                headers.update(
                    {
                        settings.TRACE_ID_HEADER: trace_id,
                        "User-Agent": settings.USER_AGENT,
                    }
                )
            settings.API_LOGGER.info(
                f"API Method: {method}, url- {url}, {kwargs}"
            )

            response = requests.request(
                method.lower(),
                url,
                headers=headers,
                params=kwargs.get("params"),
                json=kwargs.get("json_data"),
                verify=False,
                timeout=kwargs.get("timeout", conf.EXTERNAL_API_TIMEOUT),
            )

            try:
                parsed_response = response.json()
                settings.API_LOGGER.info(
                    f"API: response={parsed_response}, status_code: {response.status_code}"
                )
                return parsed_response, response.status_code

            except requests.JSONDecodeError as error:
                settings.API_LOGGER.error(
                    f"API: JSONDecodeError, url:{url} res:{response.text}, status_code:{response.status_code}, error: {error}",
                    exc_info=True,
                )
                return response.text, response.status_code

        except requests.RequestException as error:
            settings.API_LOGGER.error(
                f"RequestException: url: {url} error: {error} with response: {error.response}",
                exc_info=True,
            )
            return None, None

        except Exception as error:
            settings.API_LOGGER.error(
                f"Error while hitting url: {url}, error: {error}",
                exc_info=True,
            )

        return None, None

    @staticmethod
    def post_api(
        url,
        json_data,
        **kwargs,
    ):
        """
        Makes a POST request to the specified URL with parameters.

        Parameters:
            - url (str) : The URL to make the POST request to.
            - json_data (str):  A JSON payload(body) to include in the request
            - kwargs : Additional keyword arguments to customize the request.
                - headers (dict): A dictionary of headers to include in the request.
                - timeout (int): The timeout for the request in seconds (default: conf.EXTERNAL_API_TIMEOUT).
                - params (dict): A dictionary of URL parameters to include in the request.
                - set_default_headers (bool): Whether to set default headers like trace_id and User-Agent (default: True).


        Returns:
            tuple: A tuple containing the JSON response and the status code as a string.
                - If the response is successful, the JSON response is returned along with the status code.
                - If an error occurs, an empty dictionary is returned along with the status code.
        """
        return BaseAPI.api("post", url, json_data=json_data, **kwargs)


class ExternalAPIs(BaseAPI):
    @staticmethod
    def talk_to_ivr_rule_api(ivr_id, is_delete=True):
        """
        Check for ivr_rule in cache if not found then gets the same from IVR_RULE_API, and adds it to cache.
        Parameters:
                cache_key: ivr_rules_{ivr_id}
        Returns:
                dict:  { "start_time": "10:00", "end_time": "18:00", "action": "which service" } or None
        """
        cache_key = f"{conf.CACHE_IVR_RULE_CACHE_KEY}{ivr_id}"

        cache_rsp = CacheManager.get_value(cache_key)
        if cache_rsp:
            return Helper.is_ivr_rule_allowing_call(cache_rsp, ivr_id)

        ivr_info_obj = IvrInfo.objects.filter(ivr_id=ivr_id)
        if not ivr_info_obj.exists():
            settings.API_LOGGER.error(
                "external_apis: ivr_id: {ivr_id}, ivrinfo data not found!".format(
                    ivr_id=ivr_id
                )
            )
            return None

        company_id = ivr_info_obj.last().c_id

        try:
            url = conf.RULE_API_URL.format(
                company_id=company_id,
                ivr_id=ivr_id,
                rule_base=conf.RULE_API_BASE_URL,
            )
            response, status_code = ExternalAPIs.get_api(url)

            settings.API_LOGGER.tempinfo(
                "ivr_rule_api - response: {r}, status_code: {sc}".format(
                    r=response, sc=status_code
                )
            )

            if response and str(status_code) == "200":
                CacheManager.set_value(
                    cache_key, response["data"], conf.API_CACHE_TTL
                )
                return Helper.is_ivr_rule_allowing_call(
                    response["data"], ivr_id
                )

            if (
                response
                and str(status_code) == "404"
                and str(response.get("code")) == "404"
            ):
                if not is_delete:
                    return None
                # deleting cache key in case of central_1
                ivr_cache_key = "{prefix}{ivr_id}".format(
                    prefix=conf.CACHE_IVR_KEY_NAME, ivr_id=ivr_id
                )
                settings.API_LOGGER.info(
                    "ivr_rule_api - failed! deleting cache key: {cache_key}, ivr_id: {ivr_id}, response: {r}".format(
                        cache_key=ivr_cache_key, ivr_id=ivr_id, r=response
                    )
                )
                CacheManager.delete_key(ivr_cache_key)
                return None

        except Exception as e:
            settings.API_LOGGER.error(
                "ivr_rule_api - some error occured, ivr_rule_cache_key: {cache_key}, e: {e}".format(
                    cache_key=cache_key, e=e
                )
            )
        return None

    @staticmethod
    def talk_to_cancellation_api(ivr_id, request_id):
        """
        Checks for globally cancelled request.
        Parameters:
                ivr_id: abc
                request_id: asdfghjkl
        Returns:
                dict: {"detail": [{ "request_id": "asdfghjkl", "request_status": 2 }]} or {}
        """
        url = "{url}?request_id={request_id}".format(
            url=conf.CANCELLATION_API_URL, request_id=request_id
        )
        headers = {"Authorization": "Token " + conf.PROCESS_MANAGER_AUTH_TOKEN}
        response, status_code = ExternalAPIs.get_api(
            url, request_id=request_id, headers=headers
        )
        if status_code not in ("200", "404"):
            settings.API_LOGGER.error(
                "external_apis: request_id: {req_id}, ivr_id: {ivr_id}, cancellation api - url: {url}, res={res}".format(
                    req_id=request_id, ivr_id=ivr_id, url=url, res=response
                ),
                extra={"status_code": status_code},
            )

        if status_code == "200":
            return response

        return {}

    @staticmethod
    def talk_to_completed_requests_discovery_api(date, page):
        """
        Gets completed requesus and returns them
        Parameters:
                date: d/m/Y
                page: n
        Returns:
                list:  ['req_id1', 'req_id...n']
        """
        completed_requests = {"req_ids": [], "page_count": 1}
        instance = ApiInstanceMocker(date=date, page=page)
        settings.API_LOGGER.info(
            "completed_requests_discovery - processing page: {page}".format(
                page=page
            )
        )
        try:
            res, status_code = RoutingManger.event_api_trigger(
                conf.ROUTING_INFO_URL_NAMES[
                    "completed_requests_discovery_route_name"
                ],
                instance,
            )
            if status_code != "200" or (res and "detail" not in res):
                settings.API_LOGGER.error(
                    "completed_requests_discovery - failed!, response: {res}".format(
                        res=res
                    ),
                    extra={"status_code": status_code},
                )
                return completed_requests

            completed_requests["req_ids"] = [
                obj["request_id"] for obj in res["detail"]
            ]
            completed_requests["page_count"] = res["total_page_count"]
        except Exception as e:
            settings.API_LOGGER.error(
                "completed_requests_discovery - max attempt exception, e: {e}.".format(
                    e=e
                )
            )

        return completed_requests

    @staticmethod
    def talk_to_kamailio_api(cache_key, kam_api_name, **payload):
        """
        Check for kam_group_server api in cache if not found then gets the same from API, and adds it to cache.
        Parameters:
                cache_key: kam_group_server or kam_group_server-{kam_group_id}
                kam_api_name: {kam_api_name}
                payload: {token:token}
        Returns:
                dict:  {'1': {'name': 'TCS-TA-MP', 'c_id': 'TCS-TA-MP', 'd_id': 'MYOP', 'operator': 'TA_TCS', 'caller_id': '********', 'servers': ['d1.voicetree.info', 'd2.voicetree.info', 'd3.voicetree.info', 'd4.voicetree.info', 'd5.voicetree.info']}}
        """
        # cache_rsp = CacheManager.get_value(cache_key)
        # if cache_rsp:
        # 	return Helper.decode_data(cache_rsp)

        instance = ApiInstanceMocker(**payload)
        try:
            res, status_code = RoutingManger.event_api_trigger(
                kam_api_name, instance
            )
            if status_code != "200" or (res and "result" not in res):
                settings.API_LOGGER.error(
                    "{kam_api_name} - response not found!".format(
                        kam_api_name=kam_api_name
                    ),
                    extra={"status_code": status_code},
                )
                return []
        except Exception as e:
            settings.API_LOGGER.error(
                "{kam_api_name} - max attempt exception, e: {e}.".format(
                    kam_api_name=kam_api_name, e=e
                )
            )
            return []

        # CacheManager.set_value(cache_key, Helper.encode_data(res['result']), conf.KAM_API_CACHE_TTL)
        return res["result"]

    @staticmethod
    def get_display_number(ivr_id, request_id):
        display_number = ""
        ivr_info_obj = IvrInfo.objects.filter(ivr_id=ivr_id)
        if not ivr_info_obj.exists():
            settings.API_LOGGER.error(
                "external_apis: request_id: {req_id}, ivr_id: {ivr_id}, ivrinfo data not found!".format(
                    req_id=request_id, ivr_id=ivr_id
                )
            )
            return display_number

        display_number = ivr_info_obj.last().company_display_number
        return display_number

    @staticmethod
    def manage_udc_api_check(instance, lock_udc=True, user_view_only=1):
        response = {}
        raw_req_data = json.loads(instance.raw_data)
        company_id = instance.c_id
        ivr_id = instance.ivr_id
        request_id = instance.request_id
        req_type = str(raw_req_data.get("type", 0))
        user_id = raw_req_data.get("user_id", "")
        # display_number = ExternalAPIs.get_display_number(ivr_id, request_id)

        if req_type == "1":
            if "anon_uuid" in raw_req_data:
                # anon user case checking only channel availability
                res = ExternalAPIs.talk_to_channel_api(
                    company_id, ivr_id, request_id
                )
                if not res:
                    return res
                res.update(
                    {
                        "user_data": {
                            "contact_country": raw_req_data.get(
                                "number_2_cc", ""
                            ),
                            "contact": raw_req_data.get("number_2", ""),
                        }
                    }
                )
                return res

            is_user_avail_check_allowed = Helper.get_ivrinfo_common_setting(
                ivr_id,
                conf.UDC_API_TOGGLE_KEY,
                conf.DEFAULT_UDC_API_TOGGLE_VALUE,
            )
            if str(is_user_avail_check_allowed) == "1":
                is_locked_user = CacheManager.is_key_exist(
                    user_id, conf.CACHE_UDC_LOCKED_USERS_KEY_NAME
                )
                if not is_locked_user:
                    response = ExternalAPIs.talk_to_uc_api(
                        company_id,
                        user_id,
                        ivr_id,
                        request_id,
                        lock_udc,
                        user_view_only=user_view_only,
                        dial_reverse=raw_req_data.get("dial_reverse"),
                    )
                else:
                    settings.API_LOGGER.info("user is locked")
                    # user_id is locked process the same in the future execution, after visibility timeout
                    response = {
                        "status": "911",
                        "message": "user_id: {user_id} is locked, cant check for udc availability".format(
                            user_id=user_id
                        ),
                    }
            else:
                response = ExternalAPIs.talk_to_uc_api(
                    company_id,
                    user_id,
                    ivr_id,
                    request_id,
                    lock_udc=False,
                    user_is_avail=0,
                    user_view_only=user_view_only,
                )
        elif req_type == "2":
            is_department_check_allowed = Helper.get_ivrinfo_common_setting(
                ivr_id,
                conf.UDC_API_TOGGLE_KEY,
                conf.DEFAULT_UDC_API_TOGGLE_VALUE,
            )
            if str(is_department_check_allowed) == "1":
                # disabling ivr_lock_duration process
                is_locked_ivr = None  # CacheManager.is_key_exist(ivr_id, conf.CACHE_UDC_LOCKED_IVRS_KEY_NAME)
                if not is_locked_ivr:
                    response = ExternalAPIs.talk_to_dc_api(
                        company_id,
                        ivr_id,
                        request_id,
                        lock_udc,
                        user_view_only=user_view_only,
                    )
                else:
                    # ivr_id is locked process the same in the future execution, after visibility timeout
                    response = {
                        "status": "912",
                        "message": "ivr_id: {ivr_id} is locked, cant check for udc availability".format(
                            ivr_id=ivr_id
                        ),
                    }
            else:
                response = ExternalAPIs.talk_to_channel_api(
                    company_id, ivr_id, request_id
                )
        elif req_type == "3":
            response = ExternalAPIs.talk_to_channel_api(
                company_id, ivr_id, request_id
            )
        else:
            settings.API_LOGGER.error(
                "external_apis: request_id: {req_id}, ivr_id: {ivr_id} invalid request type, can't check for UDC availability".format(
                    req_id=request_id, ivr_id=ivr_id
                )
            )

        return response

    @staticmethod
    def talk_to_uc_api(
        company_id,
        user_id,
        ivr_id,
        request_id,
        lock_udc,
        user_is_avail=1,
        user_view_only=1,
        dial_reverse=0,
    ):
        ivr_info = IvrInfo.objects.filter(ivr_id=ivr_id).first()

        url = conf.UDC_USER_CHANNEL_API_URL.format(
            user_id=user_id,
            company_id=company_id,
            udc_base=conf.UDC_API_BASE_URL,
            user_is_avail=user_is_avail,
            user_view_only=user_view_only,
            ivr_type=ivr_info.ivr_type,
        )
        response, status_code = ExternalAPIs.get_api(
            url, request_id=request_id
        )

        settings.API_LOGGER.info(
            "external_apis: request_id: {req_id}, ivr_id: {ivr_id}, uc api - url: {url}, res={res}".format(
                req_id=request_id, ivr_id=ivr_id, url=url, res=response
            ),
            extra={"status_code": status_code},
        )

        try:
            if (
                response
                and status_code == "200"
                and str(response["data"]["is_user_avail"]) == "1"
                and str(response["data"]["is_channel_avail"]) == "1"
                and (
                    str(response["data"]["user_data"]["is_enabled"]) == "1"
                    or (
                        ivr_info.ivr_type == IvrInfo.COC
                        and str(response["data"]["user_data"]["is_enabled"])
                        in ["0", "2"]
                    )
                )
            ):  # noqa
                if lock_udc:
                    user_lock_dur = Helper.get_user_lock_duration(
                        int(dial_reverse), ivr_id
                    )
                    cache_key = "{prefix}{key}".format(
                        prefix=conf.CACHE_UDC_LOCKED_USERS_KEY_NAME,
                        key=user_id,
                    )
                    CacheManager.set_value(cache_key, 1, user_lock_dur)

                return response["data"]
        except Exception as e:
            settings.API_LOGGER.error(
                "external_apis: request_id: {req_id}, ivr_id: {ivr_id}, uc api - url: {url}, res={res}, Some error occured in UC API..., e: {e}".format(
                    req_id=request_id,
                    ivr_id=ivr_id,
                    url=url,
                    res=response,
                    e=e,
                ),
                extra={"status_code": status_code},
            )

        return {}

    @staticmethod
    def talk_to_dc_api(
        company_id, ivr_id, request_id, lock_udc, user_view_only=1
    ):
        lock_udc = False  # temporary fix, disabling ivr_lock mechanism.

        is_user_required = 0
        obdv2_call_direction = Helper.get_ivrinfo_common_setting(
            ivr_id, "obdv2_call_direction", "agent"
        )
        # default 'agent' if not already exist in IVR settings.

        if obdv2_call_direction == "agent":
            is_user_required = 1
            user_book_duration = Helper.get_ivrinfo_common_setting(
                ivr_id, "ivr_type_3_user_first_book_dur", 5
            )
        else:
            user_book_duration = Helper.get_ivrinfo_common_setting(
                ivr_id, "ivr_type_3_customer_first_book_dur", 30
            )

        url = conf.UDC_USER_DEPARTMENT_API_URL.format(
            ivr_id=ivr_id,
            company_id=company_id,
            udc_base=conf.UDC_API_BASE_URL,
            is_user_required=is_user_required,
            user_view_only=user_view_only,
            book_duration=user_book_duration,
        )

        response, status_code = ExternalAPIs.get_api(
            url, request_id=request_id
        )

        settings.API_LOGGER.info(
            "external_apis: request_id: {req_id}, ivr_id: {ivr_id}, dc api - url: {url}, res={res}".format(
                req_id=request_id, ivr_id=ivr_id, url=url, res=response
            ),
            extra={"status_code": status_code},
        )
        try:
            if (
                response
                and status_code == "200"
                and str(response["data"]["is_department_avail"]) == "1"
                and str(response["data"]["is_channel_avail"]) == "1"
            ):
                if is_user_required and (
                    not response["data"].get("user_data")
                    or str(
                        response["data"].get("user_data", {}).get("is_enabled")
                    )
                    != "1"
                ):
                    # incase of call_direction: user, user_data is must else call should go to on-hold
                    return {}

                if lock_udc:
                    ivr_lock_dur = Helper.get_ivrinfo_common_setting(
                        ivr_id,
                        conf.CACHE_UDC_LOCKED_IVRS_DUR_SETTING_NAME,
                        conf.CACHE_UDC_LOCKED_IVRS_TTL,
                    )
                    cache_key = "{prefix}{key}".format(
                        prefix=conf.CACHE_UDC_LOCKED_IVRS_KEY_NAME, key=ivr_id
                    )
                    CacheManager.set_value(cache_key, 1, ivr_lock_dur)

                return response["data"]
        except Exception as e:
            settings.API_LOGGER.error(
                "external_apis: request_id: {req_id}, ivr_id: {ivr_id}, dc api - url: {url}, res={res}, Some error occured in DC API..., e: {e}".format(
                    req_id=request_id,
                    ivr_id=ivr_id,
                    url=url,
                    res=response,
                    e=e,
                ),
                extra={"status_code": status_code},
            )

        return {}

    @staticmethod
    def talk_to_channel_api(company_id, ivr_id, request_id):
        url = conf.UDC_CHANNEL_API_URL.format(
            company_id=company_id, udc_base=conf.UDC_API_BASE_URL
        )

        response, status_code = ExternalAPIs.get_api(
            url, request_id=request_id
        )

        settings.API_LOGGER.info(
            "external_apis: request_id: {req_id}, ivr_id: {ivr_id}, channel api - url: {url}, res={res}".format(
                req_id=request_id, ivr_id=ivr_id, url=url, res=response
            ),
            extra={"status_code": status_code},
        )

        try:
            if (
                response
                and status_code == "200"
                and str(response["data"]["is_channel_avail"]) == "1"
            ):
                return response["data"]
        except Exception as e:
            settings.API_LOGGER.error(
                "external_apis: request_id: {req_id}, ivr_id: {ivr_id}, channel api - url: {url}, res={res}, Some error occured in Channel API..., e: {e}".format(
                    req_id=request_id,
                    ivr_id=ivr_id,
                    url=url,
                    res=response,
                    e=e,
                ),
                extra={"status_code": status_code},
            )

        return {}

    @staticmethod
    def talk_to_retrieve_did_api(company_id, fixdid):
        url = conf.FIXDID_API_URL.format(
            company_id=company_id,
            did=fixdid,
            fixdid_base=conf.FIXDID_API_BASE_URL,
        )

        response, status_code = ExternalAPIs.get_api(url)
        settings.API_LOGGER.info(
            "external_apis: company_id: {company_id}, fixdid: {fixdid}, retrieve_did api, url: {url}, res={res}, response_code={sc}".format(
                company_id=company_id,
                fixdid=fixdid,
                url=url,
                res=response,
                sc=status_code,
            )
        )

        if response and status_code == "200":
            return response["data"], status_code

        if status_code not in ("404"):
            settings.API_LOGGER.error(
                "external_apis: company_id: {company_id}, fixdid: {fixdid}, retrieve_did api failed, url: {url}, res={res}".format(
                    company_id=company_id, fixdid=fixdid, url=url, res=response
                ),
                extra={"status_code": status_code},
            )
        else:
            settings.API_LOGGER.info(
                "external_apis: company_id: {company_id}, fixdid: {fixdid}, retrieve_did api failed, url: {url}, res={res}".format(
                    company_id=company_id, fixdid=fixdid, url=url, res=response
                ),
                extra={"status_code": status_code},
            )

        return {}, status_code

    @staticmethod
    def manage_udc_api_check_mock(instance):
        return {"number_2_cc": "91", "number_2": "9971019537"}

    @staticmethod
    def talk_to_jp_requests_list_api(url: str, query_params: Dict = None):
        try:
            json_body, status_code = {}, None

            response = requests.get(
                url=url,
                params=query_params,
                verify=False,
                timeout=conf.EXTERNAL_API_TIMEOUT,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Token {conf.JP_REQUESTS_LIST_API_TOKEN}",
                },
            )
            try:
                json_body, status_code = response.json(), str(
                    response.status_code
                )

                settings.API_LOGGER.info(
                    f"external_apis : Response from jp_requests_list_api: {json_body} , Status - {status_code} , Url - {url} , query_params - {query_params} "
                )

                return json_body, status_code

            except Exception as e:
                settings.API_LOGGER.error(
                    f"external_apis: talk_to_jp_requests_api error, url: {url}, json_response:{json_body}, \
                        status_code:{status_code}, e: {e}, traceback - {traceback.format_exc()}"
                )

                return json_body, status_code

        except Exception as e:
            settings.API_LOGGER.error(
                f"external_apis: talk_to_jp_requests_api error,  url: {url}, e: {e},  traceback - {traceback.format_exc()}"
            )

        return json_body, "500"

    def talk_to_pilot_number_list_api(pilot_number: str = None):
        url = f"{settings.FIXDID_API_BASE_URL}/pri"

        params = {"include": "servers"}
        if pilot_number:
            params.update({"pilot_number": pilot_number})

        response, status_code = ExternalAPIs.get_api(
            url,
            params=params,
        )
        status_code = int(status_code)
        if status.is_success(status_code):
            return response

        else:
            error_message = f"External API, error pilot_number_list_api response: {response} status_code: {status_code}"
            if status.is_client_error(status_code):
                settings.API_LOGGER.info(error_message)
            elif status.is_server_error(status_code):
                settings.API_LOGGER.error(error_message)
            return None

    @staticmethod
    def talk_to_obd_internal_api(payload: Dict):
        final_response = None

        response, status_code = ExternalAPIs.post_api(
            settings.OBD_INTERNAL_API_URL, payload
        )
        if not status_code:
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR  # id

        if status.is_success(status_code):
            final_response = response

        elif status_code in settings.OBD_INTERNAL_API_RETRY_STATUS_CODES:
            raise DestinationResponseException(
                "request will be retried!!",
                response=response,
                status_code=status_code,
                retry=True,
            )

        else:
            error_message = f"talk_to_obd_internal_api: api error, response: {response} status_code: {status_code}"
            if status.is_client_error(status_code):
                settings.API_LOGGER.info(error_message)
                raise DestinationResponseException(
                    "Bad Request",
                    response=response,
                    status_code=status_code,
                    retry=False,
                )
            elif status.is_server_error(status_code):
                settings.API_LOGGER.error(error_message)
                raise DestinationResponseException(
                    "Server Error!!",
                    response=response,
                    status_code=status_code,
                    retry=True,
                )

        return final_response


class TrueCallerApi(BaseAPI):
    def __init__(self):
        self.token_cache = TrueCallerTokenCacheHandler()

    @classmethod
    def is_enabled(cls) -> bool:
        return settings.TRUE_CALLER_ENABLED

    def _get_url(self, path) -> str:
        base_url = settings.TRUE_CALLER_API_URL
        if base_url.endswith("/"):
            base_url = base_url[:-1]

        if path.startswith("/"):
            path = path[1:]

        return f"{base_url}/{path}"

    def _get_api_key(self) -> str:
        return settings.TRUE_CALLER_API_KEY

    def _get_client_id(self) -> str:
        return settings.TRUE_CALLER_CLIENT_ID

    def _get_key_id(self) -> str:
        return settings.TRUE_CALLER_KEY_ID

    def _get_token_ttl(self) -> int:
        return (
            60 * 60
        ) - 30  # 1 hour - 30 seconds, as the token is valid for 1 hour.

    def get_token(self) -> str:
        cached_token = self.token_cache.get()
        if not cached_token:
            settings.API_LOGGER.info(
                "TrueCallerApi: fetching new token from API!"
            )
            token = self.talk_to_token_api()
            if not token:
                return None
            self.token_cache.set(token, ttl=self._get_token_ttl())
            return token
        return cached_token

    def talk_to_token_api(self) -> str:
        payload: Dict = {
            "api_key": self._get_api_key(),
            "key_id": self._get_key_id(),
        }

        headers: Dict = {
            "Content-Type": "application/json",
            "X-Public-Access": "allow",
        }
        url: str = self._get_url(f"clients/{self._get_client_id()}/token")
        response, status_code = self.post_api(
            url, payload, headers=headers, set_default_headers=False
        )
        if status.is_success(status_code):
            try:
                return response["token"]
            except KeyError:
                settings.API_LOGGER.error(
                    f"TrueCallerApi: token not found in response: {response}"
                )
        else:
            return None

    def talk_to_dial_assist_api(
        self, caller_number: str, receiver_number: str
    ):
        payload: Dict = {
            "receiver_number": receiver_number,
            "caller_number": caller_number,
        }

        url: str = self._get_url(
            f"/v1/clients/{self._get_client_id()}/dial-assist"
        )

        token: str = self.get_token()
        if not token:
            return

        headers: Dict = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
        }
        return self.post_api(
            url, payload, headers=headers, set_default_headers=False
        )
