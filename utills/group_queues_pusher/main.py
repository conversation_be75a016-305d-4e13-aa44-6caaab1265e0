import json
from typing import Dict

from django.conf import settings

from central.settings.handlers import conf
from utills.cache_manager.main import <PERSON><PERSON><PERSON>anager
from utills.helpers.helper import Helper
from utills.shared_cache_manager.main import SharedCacheManager
from utills.sqs_manager.main import SQSManager
from utills.udc_helper import UDCHelper


class GroupQueuesPusher:
    def push_to_group_queue(
        self,
        source: str,
        body: Dict,
        group: Dict,
        udc_helper: UDCHelper,
        q_key: str = None,
        pq_url: str = None,
        ReceiptHandle: str = None,
    ):
        udc_response = udc_helper.udc_response

        body["source_number"] = group["source_number"]
        body["group_settings"] = group["group_settings"]
        body["ivr_settings"] = group["ivr_settings"]
        body["ivr_type"] = group["ivr_type"]

        if str(body["type"]) == "1":
            user_data = udc_response.get("user_data")
            body["number_2_cc"] = user_data.get("contact_country", "")
            body["number_2"] = user_data.get("contact", "")

        if str(body["type"]) == "2" and udc_response.get("user_data"):
            user_data = udc_response.get("user_data")
            body["number_2_cc"] = user_data.get("contact_country", "")
            body["number_2"] = user_data.get("contact", "")
            body["type"] = "1"

        settings.API_LOGGER.info(
            "{source} - request_id: {req_id}, sending req to group_queue: {gq}, req_body: {rb}".format(
                source=source,
                gq=group["url"],
                req_id=body["request_id"],
                rb=json.dumps(body),
            )
        )

        udc_helper.set_request_in_cache()  # Adding request in shared_cache

        SQSManager.send_message_to_sqs(group["url"], json.dumps(body))

        shared_cache_key = ":{prefix}:{value}".format(
            prefix=conf.REDIS_VERSION_KEY,
            value=conf.GROUP_MESSGAE_COUNT_KEY.format(q_url=group["url"]),
        )

        SharedCacheManager.incr_value(shared_cache_key)

        if source == "central_2":
            settings.API_LOGGER.info(
                "{source} - request_id: {req_id}, deleting from priority_queue: {pq}".format(
                    source=source, pq=pq_url, req_id=body["request_id"]
                )
            )

            SQSManager.sqs_delete_message(pq_url, ReceiptHandle)

            CacheManager.decr_value(q_key, True)

        pm_response = Helper.make_pm_response("call", "", "success")
        Helper.invoke_process_manager(
            body["request_id"],
            body["company_id"],
            body["ivr_id"],
            pm_response,
            "200",
            "call",
        )
