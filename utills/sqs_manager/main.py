import json
import random
import string
import uuid

from django.conf import settings

from central.settings.handlers import conf as helper_conf
from .import conf

class SQSManager:
	@staticmethod
	def create_queue(queue_name, visibility_timeout=helper_conf.GROUP_Q_VISIBILITY_TIMEOUT, attributes=None):
		"""
		creates a queue with given name and visibility_timeout.
		Parameters:
			queue_name: obd_central_company_usage_check
			visibility_timeout: 10, default = 600
		Returns:
			str: "https://sqs.ap-south-1.amazonaws.com/************/obd_central_company_usage_check"
		"""
		try:
			if not attributes:
				attributes = {"VisibilityTimeout": str(visibility_timeout)}

			queue_url = conf.sqs_client.create_queue(
				QueueName = queue_name,
				Attributes = attributes,
				tags = { helper_conf.OBD_QUEUES_TAG['key']: helper_conf.OBD_QUEUES_TAG['value'] }
			)['QueueUrl']
		except Exception as err:
			settings.API_LOGGER.info("sqs_manager - Queue  Already exists!, err: {err}".format(err=err))
			queue_urls = SQSManager.get_queue_urls(queue_name)
			queue_url = queue_urls[0]

		return queue_url

	@staticmethod
	def get_queue_url(queue_name):
		"""
		returns the queue_url
		Parameters:
			queue_name: queue_name
		Returns:
			str: queue_url or None
		"""
		try:
			return conf.sqs_client.get_queue_url(QueueName=queue_name)['QueueUrl']
		except Exception as e:
			return {}

	@staticmethod
	def get_queue_urls(queue_prefix):
		"""
		returns the list of queue_urls of matching prefix.
		Parameters:
			queue_prefix: obd_central_company_usage
		Returns:
			list: ['https://ap-south-1.queue.amazonaws.com/************/obd_central_company_usage_check']
		"""
		try:
			return conf.sqs_client.list_queues(
				QueueNamePrefix=queue_prefix
			)['QueueUrls']
		except Exception as e:
			return []

	@classmethod
	def sqs_pusher(cls, data_to_send, sqs_queue, is_name=True):
		"""
		push data to sqs queues, if queue_name is given then sends in all queues of matching queue_name prefix.
		Parameters:
			data_to_send: 'dcndfnbkcvdnklgbn', json string
			sqs_queue: queue_name or queue_url
			is_name: True if queue_name is given, False if queue_url is given
		Returns:
			list: list of sqs responses
		"""
		enqueue_response = []
		if is_name:
			for q_url in cls.get_queue_urls(sqs_queue):
				enqueue_response.append(
					SQSManager.send_message_to_sqs(q_url, data_to_send)
				)
		else:
			enqueue_response.append(
				SQSManager.send_message_to_sqs(sqs_queue, data_to_send)
			)
		return enqueue_response

	@staticmethod
	def sqs_fifo_pusher(q_url, data_to_send):
		"""
		push data to fifo queues
		Parameters:
			q_url: queue_url
			data_to_send: 'dcndfnbkcvdnklgbn', json string
		Returns:
			False
		"""
		return conf.sqs_client.send_message(
			QueueUrl=q_url,
			MessageBody=data_to_send,
			MessageDeduplicationId=str(uuid.uuid4()),
			MessageGroupId='processmanager'
		)

	@staticmethod
	def send_message_to_sqs(q_url, data_to_send):
		"""
		sends a msg to sqs.
		Parameters:
			q_url: queue_url
			data_to_send: 'dcndfnbkcvdnklgbn', json string
		Returns:
			False
		"""
		return conf.sqs_client.send_message(
			QueueUrl=q_url,
			MessageBody=data_to_send
		)

	@classmethod
	def send_message_in_batch(cls, messages, sqs_queue, is_name=True):
		"""
		sends multiple messages in batch, if queue_name is given then sends in all queues of matching queue_name prefix.
		Parameters:
			messages: ['dnvksndb']
			sqs_queue: queue_name or queue_url
			is_name: True if queue_name is given, False if queue_url is given
		Returns:
			False
		"""
		entities = [
			{ 'Id': SQSManager.randomString(), 'MessageBody': messages[i], 'DelaySeconds': 0} \
			for i in range(0, len(messages)) \
		]
		if is_name:
			for q_url in cls.get_queue_urls(sqs_queue):
				cls.send_message_batch(q_url, entities)
		else:
			cls.send_message_batch(sqs_queue, entities)

	@staticmethod
	def send_message_batch(queue_url, entities):
		"""
		sends multiple messages in batch.
		Parameters:
			sqs_queue: queue_url
			entities: [{'Id': 'dnnsdfbdfgb', 'MessageBody': 'dnvksndb', 'DelaySeconds': 0}]
		Returns:
			sqs response
		"""
		return conf.sqs_client.send_message_batch(
				QueueUrl= queue_url,
				Entries= entities
			)

	@classmethod
	def get_attributes(cls, sqs_queue, is_name=True):
		"""
		returns list of dict of queue attributes, returing list because if sqs_queue have multiple queues with same prefix.
		Parameters:
			sqs_queue: queue_name or queue_url
			is_name: True if queue_name is given, False if queue_url is given
		Returns:
			dict: [{'ApproximateNumberOfMessages': '1'}]
		"""
		attributes = []
		if is_name:
			for q_url in cls.get_queue_urls(sqs_queue):
				attributes.append(
					cls.get_queue_attributes(q_url)
				)
		else:
			attributes.append(
				cls.get_queue_attributes(sqs_queue)
			)
		return attributes

	@staticmethod
	def get_queue_attributes(queue_url, attributes=None):
		"""
		returns queue attributes
		Parameters:
			queue_url: "https://sqs.ap-south-1.amazonaws.com/************/obd_central_company_usage_check"
			attributes = list of attributes
		Returns:
			dict: {'ApproximateNumberOfMessages': '1'}
		"""
		if not attributes:
			attributes = ['ApproximateNumberOfMessages']

		return conf.sqs_client.get_queue_attributes(
			QueueUrl=queue_url,
			AttributeNames=attributes
		)['Attributes']

	@staticmethod
	def set_queue_attributes(queue_url, attributes=None):
		"""
		returns queue attributes
		Parameters:
			queue_url: "https://sqs.ap-south-1.amazonaws.com/************/obd_central_company_usage_check"
			attributes = dict of attributes
		Returns:
			None
		"""
		if not attributes:
			raise Exception("Dict of Queue Artibute(s) is required")

		return conf.sqs_client.set_queue_attributes(QueueUrl=queue_url, Attributes=attributes)

	@classmethod
	def fetch_message_lp(cls, sqs_queue, limit, is_name=True):
		"""
		returns list of sqs messages using long polling, waiting time = picking from conf.
		Parameters:
			queue_url: "https://sqs.ap-south-1.amazonaws.com/************/obd_central_company_usage_check"
			limit: 10
			is_name = False if queue_url is given, True if queue_name is given
		Returns:
			list of messages
		"""
		if is_name:
			for q_url in cls.get_queue_urls(sqs_queue):
				messages = cls.receive_message(q_url, limit, helper_conf.SQS_WAIT_TIME_LONG_POLLING)
		else:
			messages = cls.receive_message(sqs_queue, limit, helper_conf.SQS_WAIT_TIME_LONG_POLLING)
		return messages

	@classmethod
	def fetch_message_sp(cls, sqs_queue, limit, is_name=True):
		"""
		returns list of sqs messages using short polling, waiting time = 0
		Parameters:
			queue_url: "https://sqs.ap-south-1.amazonaws.com/************/obd_central_company_usage_check"
			limit: 10
			is_name = False if queue_url is given, True if queue_name is given
		Returns:
			list of messages
		"""
		if is_name:
			for q_url in cls.get_queue_urls(sqs_queue):
				messages = cls.receive_message(q_url, limit, 0)
		else:
			messages = cls.receive_message(sqs_queue, limit, 0)
		return messages

	@staticmethod
	def receive_message(q_url, limit, wait_time):
		"""
		returns list of sqs messages
		Parameters:
			queue_url: "https://sqs.ap-south-1.amazonaws.com/************/obd_central_company_usage_check"
			limit: 10
			wait_time: 10, for short polling or long polling
		Returns:
			list of messages
		"""
		res = []
		messages = conf.sqs_client.receive_message(
			QueueUrl= q_url,
			WaitTimeSeconds= wait_time,
			AttributeNames=['All'],
			MaxNumberOfMessages=limit
		)
		try:
			# return SQSManager.body_cleaner(messages['Messages'])
			return messages['Messages']
		except Exception:
			return res

	@staticmethod
	def sqs_delete_message(queue_url,message_handle):
		"""
		deletes a message from given queue.
		Parameters:
			queue_url: "https://sqs.ap-south-1.amazonaws.com/************/obd_central_company_usage_check"
			message_handle: 'AQEBxahwmtgpZDJGaVzzKYk97451G8VSH1P'
		Returns:
			sqs response of deleted msg
		"""
		return conf.sqs_client.delete_message(
			QueueUrl = queue_url, ReceiptHandle = message_handle
		)

	@staticmethod
	def sqs_delete_queue(queue_url):
		"""
		deletes given queue
		Parameters:
			queue_url: "https://sqs.ap-south-1.amazonaws.com/************/obd-central-ivrid-5efaf2226ce72271"
		Returns:
			sqs response
		"""
		try:
			return conf.sqs_client.delete_queue(
				QueueUrl = queue_url
			)
		except Exception as e:
			settings.API_LOGGER.error("sqs_manager - error occured!, err: {err}, url: {url}".format(err=e, url=queue_url))
			return {}

	@staticmethod
	def body_cleaner(messages):
		"""
		clean the 'Body' of given list of SQS messages, for valid json.
		Parameters:
			messages: list of sqs messages
		Returns:
			list: list of same sqs messages but with valid json Body.
		"""
		for message in messages:
			try:
				data = message['Body']
				data = data.replace("'",'"')
				json.loads(data)
			except Exception:
				data = json.dumps(data)
			message['Body'] = data
		return messages

	@staticmethod
	def randomString(stringLength=10):
		"""
		Generate a random string of fixed length.
		Parameters:
			stringLength: 20, default = 10
		Returns:
			str: "qpvmdayatzdigiqwhbvi"
		"""
		letters = string.ascii_lowercase
		return ''.join(random.choice(letters) for i in range(stringLength))