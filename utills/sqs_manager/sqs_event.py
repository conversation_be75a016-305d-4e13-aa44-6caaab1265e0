import json
from typing import List, Optional, Dict

from django.conf import settings


class SqsEvent:
    def __init__(self, event):
        self.event = event
        self.receipt_handle = self.event["ReceiptHandle"]
        self.body = json.loads(event["Body"])


class FixDidSqsEvent(SqsEvent):
    def __init__(self, event):
        super().__init__(event)
        self.data = dict()
        if self.body:
            self.data = self.body["data"]


    def __str__(self) -> str:
        return f"company_id: {self.company_id}, fix_dids: {self.fix_dids}"

    @property
    def company_id(self) -> Optional[str]:
        if self.data:
            return self.data[
                settings.GROUP_IVR_RELATION_SQS_DATA_FIELDS["company_id"]
            ]
        return None

    @property
    def fix_dids(self) -> List:
        if self.data:
            fix_dids = self.data[
                settings.GROUP_IVR_RELATION_SQS_DATA_FIELDS["fixed_did"]
            ]
            if not isinstance(fix_dids, (list, tuple)):
                fix_dids = [
                    fix_dids,
                ]
            return fix_dids
        return list()

    def is_map_event(self) -> bool:
        return (
            self.body.get("event", {}).get("action")
            == settings.GROUP_IVR_RELATION_SQS_EVENT_ACTIONS["map_action"]
        )

    def is_unmap_event(self) -> bool:
        return (
            self.body.get("event", {}).get("action")
            == settings.GROUP_IVR_RELATION_SQS_EVENT_ACTIONS["unmap_action"]
        )


class CallRequestSqsEvent(SqsEvent):
    def __init__(self, event: Dict, queue_url: str, queue_cache_key: str):
        super().__init__(event)
        self.queue_url = queue_url
        self.queue_cache_key = queue_cache_key

    @property
    def request_id(self) -> str:
        return self.body["request_id"]
