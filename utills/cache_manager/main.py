import logging

from django.conf import settings
from django.core.cache import cache

from utills.helpers.common_settings import ObdInternalAPICommonSettings

logger = logging.getLogger(__name__)


class CacheManager:
    @staticmethod
    def get_value(key, version=settings.REDIS_VERSION_KEY):
        try:
            return cache.get(key, version=version)
        except Exception as e:
            settings.API_LOGGER.error(
                "local_cache_manager - Unable to get from cache e:{e}  key:{key}".format(
                    e=e, key=key
                )
            )
            return None

    @staticmethod
    def set_value(
        key, value, expiry=False, version=settings.REDIS_VERSION_KEY
    ):
        try:
            if not expiry:
                cache.set(key, value, version=version)
            else:
                cache.set(key, value, expiry, version=version)
        except Exception as e:
            settings.API_LOGGER.info(
                "local_cache_manager - Unable to set to cache e:{e}  key:{key}, expiry: {expiry}".format(
                    e=e, key=key, expiry=expiry
                )
            )
            pass

    @staticmethod
    def delete_key(key, version=settings.REDIS_VERSION_KEY):
        try:
            return cache.delete(key, version=version)
        except Exception as e:
            settings.API_LOGGER.info(
                "local_cache_manager - Unable to delete key: {key} from cache e:{e}".format(
                    e=e, key=key
                )
            )
            pass

    @staticmethod
    def incr_value(key, version=settings.REDIS_VERSION_KEY):
        try:
            cache.incr(key, version=version)
        except ValueError:
            cache.set(key, 1, version=version)
            settings.API_LOGGER.info(
                "local_cache_manager - {key} not found in cache, creating a new entry.".format(
                    key=key
                )
            )
        except Exception as e:
            settings.API_LOGGER.error(
                "local_cache_manager - Unable to incr key: {key} in cache e:{e}".format(
                    e=e, key=key
                )
            )
            pass

    @staticmethod
    def decr_value(key, delete=False, version=settings.REDIS_VERSION_KEY):
        try:
            cache.decr(key, version=version)
            if (
                delete
                and (CacheManager.get_value(key, version) is not None)
                and int(CacheManager.get_value(key, version) or -1) <= 0
            ):
                CacheManager.delete_key(key, version)
            return 1
        except Exception as e:
            settings.API_LOGGER.info(
                "local_cache_manager - Unable to decr key: {key} in cache e:{e}".format(
                    e=e, key=key
                )
            )

    @staticmethod
    def get_keys(key_prefix, version=settings.REDIS_VERSION_KEY):
        """keys method will only work if the cache type is Redis"""
        keys = []
        try:
            keys = cache.keys(f"{key_prefix}*", version=version)
        except Exception as e:
            settings.API_LOGGER.error(
                "local_cache_manager - unable to connect to cache - e: {e}, key_prefix: {kp}".format(
                    e=e, kp=key_prefix
                )
            )

        return keys

    @staticmethod
    def get_many(key_prefix, version=settings.REDIS_VERSION_KEY):
        """keys and get_many methods will only work if the cache type is Redis"""
        key_values = {}
        try:
            key_values = cache.get_many(
                cache.keys(f"{key_prefix}*", version=version), version=version
            )
        except Exception as e:
            settings.API_LOGGER.error(
                "local_cache_manager - unable to connect to cache - e: {e}, key_prefix: {kp}".format(
                    e=e, kp=key_prefix
                )
            )

        return key_values

    @staticmethod
    def is_key_exist(key, key_prefix, version=settings.REDIS_VERSION_KEY):
        """checks if the given key exists in the cache against the given prefix"""

        if not key:
            return False

        cache_key = "{prefix}{key}".format(prefix=key_prefix, key=key)

        is_exist = CacheManager.get_value(cache_key, version)

        if is_exist:
            return True

        return False


class ObdInternalAPIStatusCacheHandler(CacheManager):
    key = "obd_internal_api_failed"

    def get_expiry(self):
        return ObdInternalAPICommonSettings().get_cache_ttl()

    def set(self, ttl: int = None) -> None:
        if not ttl:
            ttl = self.get_expiry()
        logger.info(f"setting cache_key: {self.key} with expiry: {ttl}")
        self.set_value(self.key, 1, expiry=ttl)

    def is_failed(self) -> bool:
        value = self.get_value(self.key)
        logger.info(f"fetched value: {value} from cache for: {self.key}")
        return True if value else False

    def delete(self):
        logger.info(f"Deleting cache key: {self.key}")

        self.delete_key(self.key)


class TrueCallerTokenCacheHandler(CacheManager):
    key = "true_caller_token"

    def set(self, value: str, ttl: int = 3600) -> None:
        logger.info(f"setting cache_key: {self.key} with expiry: {ttl}")
        self.set_value(self.key, value, expiry=ttl)

    def get(self) -> str:
        value = self.get_value(self.key)
        logger.info(f"fetched true_caller token from cache for: {self.key}")
        logger.debug(f"fetched true_caller token:{value}")
        return value

    def delete(self):
        logger.info(f"Deleting cache key: {self.key}")
        self.delete_key(self.key)
