import redis

from django.conf import settings
from fakeredis import FakeRedis

if getattr(settings, "IN_TESTING"):
    SHARED_REDIS_CON = FakeRedis(decode_responses=True)
else:
    SHARED_REDIS_CON = redis.Redis(
        host=settings.SHARED_REDIS_HOST,
        port=settings.SHARED_REDIS_PORT,
        db=settings.SHARED_REDIS_DB,
        decode_responses=True,
        socket_timeout=10,
    )

try:
    SHARED_REDIS_CON.ping()
except redis.exceptions.ConnectionError as r_con_error:
    settings.API_LOGGER.info("error----Redis connection error")
