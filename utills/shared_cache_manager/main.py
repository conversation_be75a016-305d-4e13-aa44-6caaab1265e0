from django.conf import settings

from utills.shared_cache_manager import conf


class SharedCacheManager:
    @staticmethod
    def get_value(key):
        try:
            return conf.SHARED_REDIS_CON.get(key)
        except Exception as e:
            settings.API_LOGGER.error(
                "shared_cache_manager - Unable to get from cache e:{e}  key:{key}".format(
                    e=e, key=key
                )
            )
            return None

    @staticmethod
    def get_keys(pattern):
        try:
            return conf.SHARED_REDIS_CON.keys(pattern)
        except Exception as e:
            settings.API_LOGGER.error(
                "shared_cache_manager - Unable to get from cache e:{e}  pattern:{key}".format(
                    e=e, key=pattern
                )
            )
            return None

    @staticmethod
    def set_value(key, value, expiry):
        try:
            conf.SHARED_REDIS_CON.set(key, value, ex=expiry)
        except Exception as e:
            settings.API_LOGGER.info(
                "shared_cache_manager - Unable to set to cache e:{e}  key:{key}, expiry: {expiry}".format(
                    e=e, key=key, expiry=expiry
                )
            )
            pass

    @staticmethod
    def delete_key(key):
        try:
            return conf.SHARED_REDIS_CON.delete(key)
        except Exception as e:
            settings.API_LOGGER.info(
                "shared_cache_manager - Unable to delete key: {key} from cache e:{e}".format(
                    e=e, key=key
                )
            )
            pass

    @staticmethod
    def delete_many_keys(keys):
        try:
            if not keys or not isinstance(keys, list):
                return None

            return conf.SHARED_REDIS_CON.delete(*keys)
        except Exception as e:
            settings.API_LOGGER.info(
                "shared_cache_manager - Unable to delete many_keys: {keys} from cache e:{e}".format(
                    e=e, keys=keys
                )
            )
            pass

    @staticmethod
    def incr_value(key):
        try:
            return conf.SHARED_REDIS_CON.incr(key)
        except Exception as e:
            settings.API_LOGGER.info(
                "shared_cache_manager - Unable to incr key: {key} in cache e:{e}".format(
                    e=e, key=key
                )
            )
            pass

    @staticmethod
    def decr_value(key, delete=False):
        try:
            conf.SHARED_REDIS_CON.decr(key)
            if delete and int(SharedCacheManager.get_value(key)) <= 0:
                SharedCacheManager.delete_key(key)
            return 1
        except Exception as e:
            settings.API_LOGGER.info(
                "shared_cache_manager - Unable to decr key: {key} in cache e:{e}".format(
                    e=e, key=key
                )
            )
            pass


class UDCCacheHandler(SharedCacheManager):
    def __init__(
        self,
        company_id: str,
        request_id: str,
    ) -> None:
        self.prefix_key = f":{settings.REDIS_VERSION_KEY}:channel_{company_id}"
        self.key = f"{self.prefix_key}_{request_id}"
        self.request_id = request_id
        settings.API_LOGGER.info(
            f"UDCCacheHandler,  key - {self.key} for request_id - {request_id}"
        )

    def set(
        self, value: bool = True, ttl: int = settings.REQUEST_CHANNEL_CACHE_TTL
    ):
        settings.API_LOGGER.info(
            f"UDCCacheHandler setting value - {value} for key - {self.key} for ttl - {ttl} with request_id - {self.request_id} "
        )
        self.set_value(self.key, value, ttl)

    def get_count_of_keys(self) -> int:
        keys = self.get_keys(f"{self.prefix_key}*")
        return len(keys)
