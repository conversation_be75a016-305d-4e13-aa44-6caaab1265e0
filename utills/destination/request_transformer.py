import copy
import logging
from typing import Dict

logger = logging.getLogger(__name__)


class RequestDataTransformer:
    BACKEND_OBD_API_DEFAULT_DATA = {
        "1": {
            "customer_cc": "91",
            "agent_cc": "91",
            "max_retries": "3",
            "retry_time": "1800",
            "dial_reverse": 0,
        },
        "2": {
            "customer_cc": "91",
            "agent_cc": "91",
            "max_retries": "3",
            "retry_time": "1800",
            "dial_reverse": 0,
        },
        "3": {
            "customer_cc": "91",
            "agent_cc": "91",
            "max_retries": "3",
            "retry_time": "1800",
        },
    }

    BACKEND_OBD_API_REMOVE_DATA = {
        "1": ["ivr_id", "obd_ivr", "type"],
        "2": ["agent_cc", "agent_number", "dial_reverse", "type"],
        "3": ["agent_cc", "agent_number", "dial_reverse", "type"],
    }
    ####################### OBD API ###########################
    BACKEND_OBD_API_PRE_INSTANCE_MAPPING = {"client_ref_id": "reference_id"}
    BACKEND_OBD_API_INSTANCE_MAPPING = {
        "reference_id": "request_id",
        "obd_ivr": "ivr_id",
        "display_number": "company_display_number",
        "did": "source_number",
        "group_did": "d_id",
        "group_clid": "c_id",
        "customer_no": "number",
        "customer_cc": "number_cc",
        "agent_number": "number_2",
        "agent_cc": "number_2_cc",
        "anon_uuid": "anon_uuid",
        "context": "context",
        "dial_reverse": "dial_reverse",
        "public_ivr_id": "public_ivr_id",
        "job_id": "job_id",
        "type": "type",
        "ivr_type": "ivr_type",
        "max_call_duration": "max_call_duration",
    }

    INT_TYPE_CAST = ["dial_reverse", "ivr_type", "max_call_duration"]

    def __init__(self):
        pass

    # @staticmethod
    def flat_dict(self, request_body: Dict):
        """
        it adds all keys of nested dict to parent dict.
        Parameters:
                request_body(dict): {"key1": "value1", "parent_key1": {"child_key1": "child_value1", "child_key2": "child_value2"}, "parent_key2": {"child_key3": "child_value3"}}
        Returns:
                flatten_dict: {"key1": "value1", "child_key1": "child_value1", "child_key2": "child_value2", "child_key3": "child_value3"}
        """
        instance_copy = copy.deepcopy(request_body)
        for key in request_body:
            if isinstance(request_body[key], dict):
                instance_copy.update(request_body[key])
                del instance_copy[key]
        return instance_copy

    # @staticmethod
    def fill_default_data(self, request_body: Dict):
        """
        fills the default data to given dict if default data not found in given dict, based on type, in given dict.
        default_data: {"1": {"key1": "value1", "key2": "value2"}, "2": {"key1": "value1"}, "3": {"key1": "value1"}}
        Parameters:
                request_body(dict): {"type": "1", key1": "given_value", "key3": "value3"}
        Returns:
                default_data_filled_dict: {"type": "1", "key1": "given_value", "key2": "value2", "key3": "value3"}
        """
        # default_data = conf.BACKEND_OBD_API_DEFAULT_DATA
        default_data = copy.deepcopy(self.BACKEND_OBD_API_DEFAULT_DATA)
        try:
            data = default_data[str(request_body["type"])]
            data.update(request_body)
            return data
        except Exception as e:
            logger.error(
                " manipulation layer - type field not found, data: {data}, default_data: {default_data}, e: {e}".format(
                    data=request_body, default_data=default_data, e=e
                )
            )
        return request_body

    # @staticmethod
    def remove_extra_data(self, request_body: Dict):
        """
        removed extra data from given dict, based on type, in given dict.
        extra_data: {"1": ["key1", "key2"], "2": ["key1"], "3": ["key1"]}
        Parameters:
                request_body(dict): {"type": "1", "key1": "given_value", "key3": "value3"}
        Returns:
                new_dict: {"type": "1", "key3": "value3"}
        """
        # extra_data = conf.BACKEND_OBD_API_REMOVE_DATA
        extra_data = copy.deepcopy(self.BACKEND_OBD_API_REMOVE_DATA)
        try:
            data = extra_data[str(request_body["type"])]
            for d in data:
                if d in request_body:
                    del request_body[d]
        except Exception as e:
            logger.error(
                " manipulation layer - unable to remove extra data, data: {data}, extra_data: {extra_data}, e:{e}".format(
                    data=request_body, extra_data=extra_data, e=e
                )
            )
        return request_body

    # @staticmethod
    def dynamically_unpack_values(self, request_body: Dict):
        """
        It dynamically fills the required data to a new dict and also change its name if required.
        default_mapping: {"key1": "key1", "key2": "key2_new", "key3": "key3_new", "key4": "key4"}
        Parameters:
                request_body(dict): {"key1": "1", "key2_new": "2", "key3": "3", "key5": "4"}
        Returns:
                new_dict: {"key1": "1", "key2": "2", "key3": "3"}
        """
        new_dict = {}
        # pre_instance_mapping = conf.BACKEND_OBD_API_PRE_INSTANCE_MAPPING
        # instance_mapping = conf.BACKEND_OBD_API_INSTANCE_MAPPING

        pre_instance_mapping = copy.deepcopy(
            self.BACKEND_OBD_API_PRE_INSTANCE_MAPPING
        )
        instance_mapping = copy.deepcopy(self.BACKEND_OBD_API_INSTANCE_MAPPING)

        for item in pre_instance_mapping:
            value_corresponding_to_pre_instance_mapping = request_body.get(
                pre_instance_mapping[item], request_body.get(item, None)
            )
            if value_corresponding_to_pre_instance_mapping is not None:
                new_dict[item] = str(
                    value_corresponding_to_pre_instance_mapping
                )

        for item1 in instance_mapping:
            value_corresponding_to_instance_mapping = request_body.get(
                instance_mapping[item1], request_body.get(item1, None)
            )
            if value_corresponding_to_instance_mapping is not None:
                new_dict[item1] = str(value_corresponding_to_instance_mapping)

        return new_dict

    # @staticmethod
    def type_cast_values(self, request_body: Dict):
        """It change types of given fiields to int"""
        for field in self.INT_TYPE_CAST:
            if request_body.get(field):
                try:
                    request_body[field] = int(request_body[field])
                except Exception as e:
                    logger.error(
                        " - manipulation layer - error while typecasting data: {data}, e: {e}".format(
                            data=request_body[field], e=e
                        )
                    )
        return request_body

    def transform(self, request_body: Dict):
        logger.info("Starting request transformation.")
        try:
            request_body = self.flat_dict(request_body)
            request_body = self.fill_default_data(request_body)
            request_body = self.dynamically_unpack_values(request_body)
            request_body = self.remove_extra_data(request_body)
            request_body = self.type_cast_values(request_body)
        except Exception as e:
            logger.error(
                " manipulation layer - error while manipulating data: {data}, e: {e}".format(
                    data=request_body, e=e
                )
            )
        logger.info(f"Request successfully transformed to {request_body} ")
        return request_body
