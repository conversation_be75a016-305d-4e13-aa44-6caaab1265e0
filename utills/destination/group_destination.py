import json
import logging
from copy import deepcopy
from datetime import datetime, timezone
from typing import Callable, Dict, Union

from django.conf import settings
from rest_framework import status

from botocore.exceptions import ClientError

from handlers.models import CentralRequestInfo
from handlers.tasks import notify_true_caller
from utills.cache_manager.main import ObdInternalAPIStatusCacheHandler
from utills.destination.request_transformer import RequestDataTransformer
from utills.exceptions import DestinationResponseException
from utills.external_apis import ExternalAPIs, TrueCallerApi
from utills.group_queues_pusher.main import GroupQueuesPusher
from utills.helpers.common_settings import TrueCallerCommonSettings
from utills.shared_cache_manager.main import SharedCacheManager
from utills.sqs_manager.main import SQSManager

logger = logging.getLogger(__name__)


class GroupDestination(GroupQueuesPusher):
    def __init__(self):
        self.obd_api_avail_cache = None

    def get_obd_api_avail_cache(
        self,
    ) -> ObdInternalAPIStatusCacheHandler:
        if not self.obd_api_avail_cache:
            self.obd_api_avail_cache = ObdInternalAPIStatusCacheHandler()

        return self.obd_api_avail_cache

    def prepare_payload(
        self, request_body: Dict, group_data: Dict, udc_response: Dict
    ) -> Dict:
        logger.info("Preparing request payload")

        request_body = deepcopy(request_body)
        request_body["group_settings"] = group_data.get("group_settings", {})
        request_body["ivr_settings"] = group_data.get("ivr_settings", {})

        request_body["source_number"] = group_data["source_number"]
        request_body["ivr_type"] = group_data["ivr_type"]

        if str(request_body["type"]) == "1":
            user_data = udc_response.get("user_data")
            request_body["number_2_cc"] = user_data.get("contact_country", "")
            request_body["number_2"] = user_data.get("contact", "")

        if str(request_body["type"]) == "2" and udc_response.get("user_data"):
            user_data = udc_response.get("user_data")
            request_body["number_2_cc"] = user_data.get("contact_country", "")
            request_body["number_2"] = user_data.get("contact", "")
            request_body["type"] = "1"

        logger.info(f"Request Payload: {request_body}")

        return request_body

    def increment_shared_cache(self, queue_url: str):
        shared_cache_key = ":{prefix}:{value}".format(
            prefix=settings.REDIS_VERSION_KEY,
            value=settings.GROUP_MESSGAE_COUNT_KEY.format(q_url=queue_url),
        )
        logger.info(f"Incrementing cache key: {shared_cache_key}")
        SharedCacheManager.incr_value(shared_cache_key)

    def get_total_time_taken(self, request_time: str) -> float:
        request_time = datetime.strptime(request_time, "%Y-%m-%d %H:%M:%S")
        current_time = datetime.strptime(
            str(datetime.now(tz=timezone.utc).strftime("%Y-%m-%d %H:%M:%S")),
            "%Y-%m-%d %H:%M:%S",
        )
        return abs((request_time - current_time).total_seconds())

    def push_to_api(self, request_payload: Dict) -> Dict:
        payload = RequestDataTransformer().transform(request_payload)
        try:
            response = ExternalAPIs.talk_to_obd_internal_api(payload)
            logger.info(
                "Request send to obd internal api",
                extra={
                    "total_time_taken": self.get_total_time_taken(
                        request_payload["request_time"]
                    )
                },
            )
            return response
        except DestinationResponseException as error:
            if status.is_server_error(int(error.status_code)):
                self.get_obd_api_avail_cache().set()  # setting cache to avoid triggering the api for some time.
            raise

    def push_to_group_queue(
        self, group_queue_url: str, request_payload: Dict
    ) -> str:
        logger.info(f"Sending request to group queue: {group_queue_url}")
        try:
            SQSManager.send_message_to_sqs(
                group_queue_url, json.dumps(request_payload)
            )
            return "success"

        except ClientError as error:
            logger.error(f"ClientError with msg: {str(error)}")
            raise DestinationResponseException(
                f"Push to SQS: {group_queue_url} failed!!", retry=True
            ) from error

    def _push(
        self, group_data: Dict, request_payload: Dict
    ) -> Union[Dict, str]:
        if group_data.get("is_aws"):
            return self.push_to_api(request_payload)

        else:
            response = self.push_to_group_queue(
                group_data["url"], request_payload
            )
            self.increment_shared_cache(group_data["url"])
            return response

    def push(
        self,
        cri_obj: CentralRequestInfo,
        group_data: Dict,
        udc_response: Dict,
        on_success: Callable[
            [CentralRequestInfo, Union[Dict, str]],
            None,
        ],
        on_failure: Callable[
            [CentralRequestInfo, Union[Dict, str], str],
            None,
        ],
        on_retry: Callable[[CentralRequestInfo], None] = None,
    ):
        request_payload = self.prepare_payload(
            cri_obj.body, group_data, udc_response
        )
        try:
            response = self._push(group_data, request_payload)
            on_success(
                cri_obj,
                response,
            )
            if TrueCallerCommonSettings().is_enabled_for_company(cri_obj.c_id):
                notify_true_caller.delay(request_payload)

        except DestinationResponseException as error:
            if error.retry:
                if on_retry:
                    logger.info(
                        f"Destination Failed with response:{error.response}, status_code:{error.status_code}, Retrying request!!"
                    )
                    on_retry(cri_obj)
                return

            on_failure(cri_obj, error.response, error.status_code)
