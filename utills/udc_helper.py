from django.conf import settings

from handlers.models import CentralRequestInfo
from utills.exceptions import UDCLockedException, UDCUnavailable
from utills.external_apis import ExternalAPIs
from utills.shared_cache_manager.main import UDCCacheHandler


class UDCHelper:
    def __init__(self, instance: CentralRequestInfo) -> None:
        self.instance = instance
        self.company_id = instance.c_id
        self.request_id = instance.request_id
        self.udc_response = None
        self.cache = UDCCacheHandler(self.company_id, self.request_id)

    def udc_api_checks(self, user_view_only: int):
        response = ExternalAPIs.manage_udc_api_check(
            self.instance, user_view_only=user_view_only
        )
        if not response:
            raise UDCUnavailable(
                f"UDC Unavailable for request_id - {self.request_id}, ivr_id - {self.instance.ivr_id}"
            )

        if (
            response.get("status", None) == "911"
            or response.get("status", None) == "912"
        ):
            settings.API_LOGGER.info(
                f"User: {self.instance.user_id} is locked!!"
            )
            raise UDCLockedException(
                f"UDC is locked for request_id - {self.request_id}!!",
                response,
            )

        self.udc_response = response

    def get_avail_channels_from_response(self):
        try:
            return self.udc_response["channel_data"]
        except (KeyError, TypeError) as error:
            settings.API_LOGGER.error(
                f"UDCHelper- Error occurred while checking UDC for request_id- {self.request_id}, ivr_id - {self.instance.ivr_id}, error -{error}"
            )
            return 0

    def check_keys_in_cache(self):
        keys_count = self.cache.get_count_of_keys()
        settings.API_LOGGER.info(
            f"UDCHelper - Fetched keys count- {keys_count} for request_id - {self.request_id}, ivr_id - {self.instance.ivr_id}, company_id- {self.company_id}"
        )
        avail_channels = self.get_avail_channels_from_response()

        if avail_channels <= keys_count:
            settings.API_LOGGER.info(
                f"UDCHelper: keys count- {keys_count} is more than or equals to available_channels- {avail_channels}, request_id- {self.request_id}, company_id- {self.company_id}, ivr_id- {self.instance.ivr_id}"
            )
            raise UDCUnavailable("keys count is more then available channels.")
        return True

    def udc_checks(self, user_view_only: int):
        self.udc_api_checks(user_view_only)
        return self.check_keys_in_cache()

    def set_request_in_cache(self):
        self.cache.set(1)
