import copy
import json

from django.conf import settings

import requests
from retrying import retry

from central.settings.handlers import conf
from handlers.models import ApiResponseFailure
from utills.helpers.helper import Helper
from utills.sqs_manager.main import SQSManager


class RoutingManger:
    @classmethod
    @retry(
        retry_on_result=Helper.retry_if_result_none,
        wait_fixed=conf.INTRA_API_RETRY_DELAY,
        stop_max_attempt_number=conf.INTRA_API_MAX_RETRIES,
    )
    def event_api_trigger(cls, api_name, instance):
        routing_info = cls.get_routing_info(instance, api_name)

        if routing_info:
            response, _ = cls.dynamic_api_call(
                instance.request_id, routing_info
            )
        else:
            settings.API_LOGGER.error(
                "routing_manager - Routing Info API Failed, Name: {api_name}, Response: {routing_info}".format(
                    api_name=api_name, routing_info=routing_info
                )
            )
            return None

        if routing_info["is_external"] and response is None:
            return True

        return response, _

    @classmethod
    @retry(
        retry_on_result=Helper.retry_if_result_none,
        wait_fixed=conf.INTRA_API_RETRY_DELAY,
        stop_max_attempt_number=conf.INTRA_API_MAX_RETRIES,
    )
    def main_service_queue_router(cls, instance):
        service_name = cls.get_route_process_flow(instance)

        # PF not found
        if service_name in (
            conf.OBD_SERVICES["cancel_service"],
            conf.OBD_SERVICES["complete_service"],
        ):
            return service_name

        if service_name is not None:
            cls.dynamic_target_service_queue_push(service_name, instance)

            return True

        return None

    @staticmethod
    def dynamically_unpack_values(
        instance, instance_mapping, name, url_with_placeholder_args=None
    ):
        native_service_field_override = (
            conf.NATIVE_SERVICE_FIELD_OVERRIDE.get(name) or {}
        )

        instance_dict = instance.__dict__

        for s_name in conf.SERVICE_NAMES_VARS_LIST:
            if s_name in instance_mapping:
                instance_mapping[s_name] = conf.OBD_SERVICES["source_name"]

        new_dict = copy.deepcopy(instance_mapping)
        for item in instance_mapping:
            value_corresponding_to_instance_mapping = instance_dict.get(
                instance_mapping[item],
                instance_dict.get(
                    item,
                    instance_dict.get(
                        native_service_field_override.get(
                            instance_mapping[item], None
                        )
                    ),
                ),
            )
            if value_corresponding_to_instance_mapping is not None:
                new_dict[item] = str(value_corresponding_to_instance_mapping)

        if url_with_placeholder_args:
            url = Helper.get_actual_url(url_with_placeholder_args, new_dict)
            return url

        return new_dict

    @staticmethod
    def dynamic_target_service_queue_push(service_name, instance, data=None):
        if not data:
            data = {}

        data.update(json.loads(instance.raw_data))

        data.update({conf.SERVICE_ROUTER_FLAG: service_name})

        service_route_q_url = Helper.get_queue_name_url_mapping(
            "queue_name",
            conf.SERVICE_ROUTER_QUEUE,
            conf.SERVICE_ROUTER_GATEWAY_PREFIX,
        )

        SQSManager.sqs_pusher(
            json.dumps(data),
            service_route_q_url[conf.SERVICE_ROUTER_QUEUE],
            False,
        )

    @staticmethod
    def dynamic_api_call(req_id, api_data, ignore_404=False):
        method = api_data["method"]
        url = api_data["url"]
        headers = api_data["headers"]
        api_timeout = conf.INTRA_API_TIMEOUT
        is_external = 0

        if "is_external" in api_data and api_data["is_external"]:
            is_external = 1
            api_timeout = conf.EXTERNAL_API_TIMEOUT

        if method == "GET":
            params = api_data["data"]
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                timeout=api_timeout,
                verify=False,
            )
        else:
            payload = api_data["data"]
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                data=payload,
                timeout=api_timeout,
                verify=False,
            )

        res_status = str(response.status_code)
        if res_status.startswith("2"):
            resp = json.loads(response.text)

            # settings.API_LOGGER.info('routing_manager - request api_data: {req_data}, response: {response}'.format(req_data=api_data, response=resp))
        else:
            if res_status.startswith("404"):
                settings.API_LOGGER.tempinfo(
                    "routing_manager - request_id: {req_id}, Error api failed, req_data = {req_data}, res_data={res_data}".format(
                        req_data=api_data,
                        res_data=response.text,
                        req_id=req_id,
                    ),
                    extra={
                        "status_code": res_status,
                        "external_api": is_external,
                    },
                )
            else:
                settings.API_LOGGER.error(
                    "routing_manager - request_id: {req_id}, Error api failed, req_data = {req_data}, res_data={res_data}".format(
                        req_data=api_data,
                        res_data=response.text,
                        req_id=req_id,
                    ),
                    extra={
                        "status_code": res_status,
                        "external_api": is_external,
                    },
                )

            # RoutingManger.set_api_response_failure(req_id, url, res_status, api_data, response)

            for item in conf.RETRY_STATUS_CODES:
                if res_status.startswith(item):
                    resp = None
                    break
            else:  # forelse
                resp = True

        return resp, res_status

    @staticmethod
    def set_api_response_failure(req_id, url, res_status, api_data, response):
        dict_to_save_in_db = {
            "req_id": req_id,
            "url": url,
            "status": res_status,
            "failure_api": api_data,
            "context": "Api failed",
        }
        try:
            dict_to_save_in_db.update(response=json.loads(response.text))
        except Exception:
            dict_to_save_in_db.update(response=str(response.text))

        data = json.dumps(dict_to_save_in_db)
        ApiResponseFailure.objects.create(
            request_id=req_id, status_code=res_status, response=data
        )

    @classmethod
    def call_routing_info(cls, req_id, name):
        routing_info_data_pack = copy.deepcopy(conf.ROUTING_INFO_API_DATA)
        routing_info_data_pack["data"]["params"]["name"] = name

        routing_info_data_pack["headers"] = routing_info_data_pack["data"][
            "headers"
        ]
        routing_info_data_pack["data"] = routing_info_data_pack["data"][
            "params"
        ]

        response, _ = cls.dynamic_api_call(req_id, routing_info_data_pack)

        if response:
            return response["detail"][0]

        return response

    @classmethod
    def get_route_process_flow(cls, instance):
        api_data = cls.get_routing_info(
            instance, conf.ROUTING_INFO_URL_NAMES["process_flow_router_name"]
        )
        response, status_code = cls.dynamic_api_call(
            instance.request_id, api_data, ignore_404=conf.ignore_error_404
        )
        if response:
            try:
                service_name_route = response["detail"].get("destination_name")
            except Exception as e:
                if status_code.startswith("404"):
                    settings.API_LOGGER.info(
                        "routing_manager - request_id: {req_id}, Process Flow service not found, returning complete_service {}".format(
                            api_data, req_id=instance.request_id
                        )
                    )
                    service_name_route = conf.OBD_SERVICES["complete_service"]
                else:
                    settings.API_LOGGER.info(
                        "routing_manager - request_id: {req_id}, Process Flow service not found, returning cancel_service {}".format(
                            api_data, req_id=instance.request_id
                        )
                    )
                    service_name_route = conf.OBD_SERVICES["cancel_service"]

            return service_name_route

        settings.API_LOGGER.info(
            "routing_manager - request_id: {req_id}, Error Process FLow route failure with data {api_data}".format(
                api_data=api_data, req_id=instance.request_id
            )
        )
        return response

    @classmethod
    def get_routing_info(cls, instance, name):
        routing_data = cls.call_routing_info(instance.request_id, name)

        # settings.API_LOGGER.info('routing_manager - routing_data {}'.format(routing_data))

        if not routing_data:
            settings.API_LOGGER.info(
                "routing_manager - Error Routing Info API failure, name: {name}, request_id: {req_id}".format(
                    name=name, req_id=instance.request_id
                )
            )
            return None

        routing_data["data"] = json.loads(
            routing_data["data"].replace("'", '"')
        )
        routing_data["headers"] = routing_data["data"]["headers"]

        if routing_data["data"].get("dynamic_args"):
            routing_data["url"] = cls.dynamically_unpack_values(
                instance,
                routing_data["data"]["dynamic_args"],
                name,
                routing_data["url"],
            )

        if routing_data["method"] == "GET":
            routing_data["data"] = cls.dynamically_unpack_values(
                instance, routing_data["data"]["params"], name
            )
        else:
            routing_data["data"] = cls.dynamically_unpack_values(
                instance, routing_data["data"]["body"], name
            )

        return routing_data
