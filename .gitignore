*.log

*.py[codt]

# C extensions
*.so

# Packages
*.egg
*.egg-info
dist
build
eggs
parts
bin
var
sdist
develop-eggs
.installed.cfg
lib
lib64

# Installer logs
pip-log.txt

# Unit test / coverage reports
.coverage
.tox
nosetests.xml

# Translations
*.mo

# Mr Developer
.mr.developer.cfg
.project
.pydevproject

# Complexity
output/*.html
output/*/index.html

# Sphinx
docs/_build

.webassets-cache

process/
# Virtualenvs
.env
venv

# intellij
*.ipr
*.iml
*.iws
*.pem

# Mysql
*.sql
*.sql.gz

#Test Files
test_*.txt

central/settings/prelocal.py

# collect static output directory
central/assets/
central/static/

.DS_Store
.idea/
static/
media/

# node
node_modules/

# bower packages
central/assets-src/vendor/
.sass-cache/
todo.txt

.vscode
.env
my-log.json
myapp.log
utills/sqs_manager/seeds.py

deploy-config/stage_envs/

Pipfile

db_cleaner/grt_puller/*.*
!db_cleaner/grt_puller/.gitignore

db_cleaner/s3_pusher/*.*
!db_cleaner/s3_pusher/.gitignore

test_reports
coverage.xml
deploy/terraform/.terraform/*
deploy/terraform/.terraform*
deploy/terraform/builds/*
deploy/terraform/*.tfvars
