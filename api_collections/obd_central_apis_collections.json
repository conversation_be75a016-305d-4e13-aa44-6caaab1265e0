{"info": {"_postman_id": "a7bde82d-3cf3-4fda-b9ce-434962f56ab4", "name": "OBD Central", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "Central Internal Requests", "item": [{"name": "User <PERSON>", "item": [{"name": "get-user-token", "_postman_id": "d360ac68-2aca-4425-a19e-77e868baaf71", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n\t\"username\": \"gaura<PERSON><PERSON><PERSON><PERSON>\",\n\t\"password\": \"abrakadabra\"\t\n}", "options": {"raw": {"language": "json"}}}, "url": "{{obd_central_host}}/api/1/get-user-token"}, "response": []}], "_postman_id": "505f64d3-6f59-457e-805c-9c322740d190", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "Server Groups", "item": [{"name": "server_groups", "_postman_id": "fabd6b80-b2d9-4879-990e-2048787cc058", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{central_token}}", "type": "text"}], "url": {"raw": "{{obd_central_host}}/api/1/server-groups?group=S4-TEST", "host": ["{{obd_central_host}}"], "path": ["api", "1", "server-groups"], "query": [{"key": "group", "value": "S4-TEST"}, {"key": "kam_group_id", "value": "1", "disabled": true}, {"key": "server", "value": "s4.voicetree.info", "disabled": true}]}, "description": "Params: (anyone)\n- server,\n- group,\n- kam_group_id"}, "response": []}], "_postman_id": "19abff60-32c4-4d85-a50b-6f685ec324f6", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "Group Ivrs", "item": [{"name": "group-ivr", "_postman_id": "f5d5e837-5b4d-48b8-be8a-396118273612", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{central_token}}", "type": "text"}], "url": {"raw": "{{obd_central_host}}/api/1/group-ivr?group=INT-US-DEV", "host": ["{{obd_central_host}}"], "path": ["api", "1", "group-ivr"], "query": [{"key": "ivr_id", "value": "5e3297cda390c178_19", "disabled": true}, {"key": "group", "value": "INT-US-DEV"}]}}, "response": []}, {"name": "notif-group-ivr", "_postman_id": "97c14cde-5d43-466b-ba41-c18476101e47", "request": {"method": "GET", "header": [], "url": "{{obd_central_host}}/api/1/notif-group-ivr"}, "response": []}], "_postman_id": "ee86786c-03f5-452d-bf38-b6699938441a", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "Company Display Numbers", "item": [{"name": "company_display_numbers", "_postman_id": "19ac7ba4-37b3-464d-b40f-eb9550d9b620", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{central_token}}", "type": "text"}], "url": "{{obd_central_host}}/api/1/company_display_numbers"}, "response": []}], "_postman_id": "5742691c-27ba-4e96-92a9-10fc97bd8a69", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "Company Usage Check", "item": [{"name": "companyusage-check", "_postman_id": "61c0085b-e948-4a8c-9fe5-cefe521132df", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{central_token}}", "type": "text"}], "url": {"raw": "{{obd_central_host}}/api/1/companyusage-check?c_id=1", "host": ["{{obd_central_host}}"], "path": ["api", "1", "companyusage-check"], "query": [{"key": "c_id", "value": "1"}]}}, "response": []}, {"name": "notif-companyusage-check", "_postman_id": "7eaea882-e971-408a-a77f-816e7210c71f", "request": {"method": "GET", "header": [], "url": "{{obd_central_host}}/api/1/notif-companyusage-check"}, "response": []}], "_postman_id": "d0fda098-c97b-479e-b7bd-e910874af9c6", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "IvrInfo", "item": [{"name": "iv<PERSON><PERSON>", "_postman_id": "bb6af5e6-5110-47bf-8697-242f423d4c43", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{central_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{obd_central_host}}/api/1/ivrinfo?ivr_id=5e3297cda390c178_19", "host": ["{{obd_central_host}}"], "path": ["api", "1", "iv<PERSON><PERSON>"], "query": [{"key": "company_id", "value": "1", "disabled": true}, {"key": "company_display_number", "value": "kjsn", "disabled": true}, {"key": "id", "value": "8", "disabled": true}, {"key": "page", "value": "1", "disabled": true}, {"key": "ivr_id", "value": "5e3297cda390c178_19"}]}}, "response": []}, {"name": "iv<PERSON><PERSON>", "_postman_id": "feb89f4d-6319-4a84-84a4-a08a05370d56", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Token {{central_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"c_id\": \"2\",\n    \"ivr_id\": \"5e3297cda390c178_19\",\n    \"common_setting\": {\n        \"shared_backup\": 1,\n        \"failover\": 1\n    },\n    \"company_priority\": 1,\n    \"ivr_priority\": 1,\n    \"company_display_number\": \"911204022601\",\n    \"country_code\": \"91\"\n}"}, "url": "{{obd_central_host}}/api/1/ivrinfo"}, "response": []}, {"name": "iv<PERSON><PERSON>", "_postman_id": "f5ef6177-22c7-479e-8b18-2a41f90851a7", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Token {{central_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"c_id\": \"2\",\n    \"ivr_id\": \"5e3297cda390c178_19\",\n    \"common_setting\": {\n        \"shared_backup\": 1,\n        \"failover\": 0\n    },\n    \"company_priority\": 1,\n    \"ivr_priority\": 1,\n    \"company_display_number\": \"911204022601\",\n    \"country_code\": \"91\"\n}"}, "url": "{{obd_central_host}}/api/1/ivrinfo"}, "response": []}, {"name": "iv<PERSON><PERSON>", "_postman_id": "8fe1e8f9-189d-4a27-918a-e5ba827bf604", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Token {{central_token}}", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{obd_central_host}}/api/1/ivrinfo/?ivr_id=5e3297cda390c178_19&common_setting=shared_backup, failover", "host": ["{{obd_central_host}}"], "path": ["api", "1", "iv<PERSON><PERSON>", ""], "query": [{"key": "ivr_id", "value": "5e3297cda390c178_19"}, {"key": "common_setting", "value": "shared_backup, failover"}]}}, "response": []}], "_postman_id": "d8621925-c23c-4ca4-b35d-853d511674de", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}], "_postman_id": "989da314-36e8-43b5-8b5c-37fb643abc13", "protocolProfileBehavior": {}}, {"name": "Process Manager", "item": [{"name": "Routing Info", "item": [{"name": "Routing Info [GET]", "_postman_id": "427e48ea-3cf5-4307-a40a-e5801457300d", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "{{pm_token}}", "type": "text"}], "url": {"raw": "{{process_manager_host}}/api/1/routinginfo", "host": ["{{process_manager_host}}"], "path": ["api", "1", "routinginfo"], "query": [{"key": "name", "value": "process_flow_get", "disabled": true}, {"key": "public_ip", "value": "1", "disabled": true}]}, "description": "URI: /api/1/routinginfo?name=misscall_expiry_enable_call\n\nMethod: GET \nBody: raw json\nRequest:\n        {\n\t\t \"name\":\"misscall_expiry_enable_call\"\n        }\n\n\nResponse:\n{\n    \"detail\": [\n        {\n            \"name\": \"misscall_expiry_enable_call\",\n            \"method\": \"POST\",\n            \"url\": \"http://127.0.0.1:8002/api/1/expiry_request\",\n            \"data\": \"{'body': {'req_id': 'req_id', 'service_unique_id': 'id', 'service_name': 'misscall', 'ttl': 'expiry'}, 'headers': {}, 'params': {}}\"\n        }\n    ]\n}"}, "response": []}, {"name": "Routing Info [POST]", "_postman_id": "99aed3db-4f8d-4bef-87b0-210c00edac76", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "{{pm_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"completed_requests_discovery\",\n    \"method\": \"GET\",\n    \"data\": \"{'body': {}, 'headers': {'Authorization': 'Token 521b849c6fa3dd1d18ae2d819c60cf546138d2a2'}, 'params': {'date': 'date'}}\",\n    \"is_external\": 0,\n    \"ip_url\": \"http://*************\",\n    \"relative_url\": \"/api/1/completed_requests_discovery/\",\n    \"url\": \"http://*************/api/1/completed_requests_discovery/\"\n}", "options": {"raw": {}}}, "url": "{{process_manager_host}}/api/1/routinginfo", "description": "URI: /api/1/routinginfo\n\nMethod: POST \nBody: raw json\nRequest:\n{\n    \"name\": \"misscall_response_set\",\n    \"method\": \"POST\",\n    \"url\": \"http://127.0.0.1:8000/api/1/responseset\",\n    \"data\": {\n        \"body\": {},\n        \"headers\": {},\n        \"params\": {\n            \"user_number\": \"from_num\",\n            \"misscall_number\": \"to_num\",\n            \"event\": \"event\"\n        }\n    }\n}\n\nResponse:\n{\n    \"name\": \"misscall_response_set\",\n    \"method\": \"POST\",\n    \"url\": \"http://127.0.0.1:8000/api/1/responseset\",\n    \"data\": {\n        \"body\": {},\n        \"headers\": {},\n        \"params\": {\n            \"user_number\": \"from_num\",\n            \"misscall_number\": \"to_num\",\n            \"event\": \"event\"\n        }\n    }\n}"}, "response": []}, {"name": "Routing Info [PUT]", "_postman_id": "6bf2c915-66e4-4f7e-97db-5bdf5fb26911", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Token 521b849c6fa3dd1d18ae2d819c60cf546138d2a2", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"name\": \"completed_requests_discovery_mock\",\n    \"method\": \"GET\",\n    \"data\": \"{'body': {}, 'headers': {'Authorization': 'Token 521b849c6fa3dd1d18ae2d819c60cf546138d2a2'}, 'params': {'date': 'date'}}\",\n    \"is_external\": 1,\n    \"ip_url\": null,\n    \"relative_url\": \"http://localhost/completed_requests_discovery.php\",\n    \"url\": \"http://localhost/completed_requests_discovery.php\"\n}", "options": {"raw": {}}}, "url": "{{process_manager_host}}/api/1/routinginfo", "description": "URI: /api/1/routinginfo\n\nMethod: PUT \nBody: raw json\nRequest:\n{\n    \"name\": \"ivr_rule\",\n    \"method\": \"POST\",\n    \"relative_url\": \"/api/1/responseset\",\n    \"data\": {\n        \"body1\": {\n            \"user_number\": \"from_num\",\n            \"misscall_number\": \"to_num\",\n            \"event\": \"event\"\n        },\n        \"headers\": {},\n        \"params\": {}\n    },\n    \"ip_url\": \"http://127.0.0.1:8003\"\n}\n\n\nResponse:\n{\n    \"name\": \"misscall_response_set\",\n    \"method\": \"POST\",\n    \"url\": \"http://127.0.0.1:8000/api/1/responseset\",\n    \"data\": {\n        \"body\": {},\n        \"headers\": {},\n        \"params\": {\n            \"user_number\": \"from_num\",\n            \"misscall_number\": \"to_num\",\n            \"event\": \"event\"\n        }\n    }\n}"}, "response": []}, {"name": "Routing Info [DELETE]", "_postman_id": "2e406415-e161-4a13-b1d8-0a62647f2a6d", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Token 521b849c6fa3dd1d18ae2d819c60cf546138d2a2", "type": "text"}], "body": {"mode": "raw", "raw": "{\n   \n   \"name\": \"completed_requests_discovery_mock\"\n}", "options": {"raw": {}}}, "url": "{{process_manager_host}}/api/1/routinginfo", "description": "URI: /api/1/routinginfo\n\nMethod: PUT \nBody: raw json\nRequest:\n{\n   \n   \"name\": \"misscall_response_set\"\n}\n\n\nResponse:\n{\n    'detail': \"OK\"\n}"}, "response": []}], "_postman_id": "954c8fed-9c66-49d9-b0ae-08649febfca2", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "ProcessFLow", "item": [{"name": "processflow [GET]", "_postman_id": "8ce69c3b-35aa-41d2-884c-f681b149eeb8", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Token daa66f6e7367caa02e0b4b10f9f1a9e657b7f97e", "type": "text"}], "url": {"raw": "{{process_manager_host}}/api/1/process-flow?ivr_id=99&company_id=99", "host": ["{{process_manager_host}}"], "path": ["api", "1", "process-flow"], "query": [{"key": "iteration", "value": "1", "disabled": true}, {"key": "ivr_id", "value": "99"}, {"key": "source_name", "value": "call", "disabled": true}, {"key": "event", "value": "1", "disabled": true}, {"key": "company_id", "value": "99"}, {"key": "page", "value": "1", "disabled": true}]}, "description": "URI: /api/1/process-flow\n\nMethod: GET \nBody: raw json\nRequest Params:\nRequest:\n{\n    \"company_id\": \"1\",\n    \"ivr_id\": \"1\",\n    \"source\": \"2\",\n    \"iteration\": \"1\",\n    \"event\": \"1\"\n},\nResponse:\n{\n    \"detail\": [\n        {\n        \"id\": 1,\n        \"company_id\": \"1\",\n        \"company_display_number\": null,\n        \"ivr_id\": \"1\",\n        \"source_name\": \"misscall\",\n        \"event\": 2,\n        \"iteration\": 1,\n        \"destination_name\": \"misscall\"\n    }\n    ]\n}"}, "response": []}, {"name": "companies [GET]", "_postman_id": "8520cdf7-865c-4cc4-81a5-418e05550ce3", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Token daa66f6e7367caa02e0b4b10f9f1a9e657b7f97e", "type": "text"}], "url": {"raw": "{{process_manager_host}}/api/1/companies", "host": ["{{process_manager_host}}"], "path": ["api", "1", "companies"], "query": [{"key": "iteration", "value": "1", "disabled": true}, {"key": "ivr_id", "value": "1", "disabled": true}, {"key": "source_name", "value": "misscall", "disabled": true}, {"key": "event", "value": "2", "disabled": true}, {"key": "company_id", "value": "1", "disabled": true}]}, "description": "URI: /api/1/process-flow\n\nMethod: GET \nBody: raw json\n\nResponse:\n{\n    \"detail\": [\n        \"986757\"\n    ]\n}"}, "response": []}, {"name": "processflow [POST]", "_postman_id": "f42104bc-ae94-4c58-b9a1-61f45b2a86ec", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Token daa66f6e7367caa02e0b4b10f9f1a9e657b7f97e", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"company_id\": \"99\",\n    \"iteration\": 1,\n    \"ivr_id\": \"99\",\n    \"source_name\": \"call\",\n    \"event\": 1,\n    \"destination_name\": \"call\"\n}\n", "options": {"raw": {}}}, "url": "{{process_manager_host}}/api/1/process-flow", "description": "URI: /api/1/process-flow\n\nMethod: POST \nBody: raw json\nRequest:\n{\n    \"detail\": {\n        \"id\": 19,\n        \"company_id\": \"233\",\n        \"company_display_number\": null,\n        \"ivr_id\": \"13433\",\n        \"source_name\": \"misscall\",\n        \"event\": 1,\n        \"iteration\": 1,\n        \"destination_name\": \"call\"\n    }\n}\nResponse:\n{\n    \"detail\": {\n        \"id\": 19,\n        \"company_id\": \"233\",\n        \"company_display_number\": null,\n        \"ivr_id\": \"13433\",\n        \"source_name\": \"misscall\",\n        \"event\": 1,\n        \"iteration\": 1,\n        \"destination_name\": \"call\"\n    }\n}"}, "response": []}, {"name": "processflow [PUT]", "_postman_id": "d1b21e5d-1790-495a-901c-ccce49529eb5", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Token daa66f6e7367caa02e0b4b10f9f1a9e657b7f97e", "type": "text"}], "body": {"mode": "raw", "raw": "{\n \n    \"company_id\": \"233\",\n    \"iteration\": 1,\n    \"ivr_id\": \"13433\",\n    \"source_name\": \"misscall\",\n    \"event\": 1,\n    \"destination_name\": \"call\",\n    \"company_display_number\":\"98226757\"\n}", "options": {"raw": {}}}, "url": "{{process_manager_host}}/api/1/process-flow/32", "description": "URI: /api/1/process-flow\n\nMethod: PUT \nBody: raw json\nRequest:\n{\n    \"detail\": {\n        \"id\": 19,\n        \"company_id\": \"233\",\n        \"company_display_number\": null,\n        \"ivr_id\": \"13433\",\n        \"source_name\": \"misscall\",\n        \"event\": 1,\n        \"iteration\": 1,\n        \"destination_name\": \"call\"\n    }\n}\nResponse:\n{\n    \"detail\": {\n        \"id\": 19,\n        \"company_id\": \"233\",\n        \"company_display_number\": null,\n        \"ivr_id\": \"13433\",\n        \"source_name\": \"misscall\",\n        \"event\": 1,\n        \"iteration\": 1,\n        \"destination_name\": \"call\"\n    }\n}"}, "response": []}, {"name": "processflow [DELETE]", "_postman_id": "cb6d874a-e065-4bf2-8bb5-d1545ef7b858", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"company_id\": \"1\",\n    \"service_id\": 1,\n    \"iteration\": 1,\n    \"ivr_id\": \"1\",\n    \"source_name\": \"call\",\n    \"event\": 1\n}"}, "url": "{{process_manager_host}}/api/1/process-flow/3", "description": "URI: /api/1/process-flow\n\nMethod: PUT \nBody: raw json\nRequest:\n{\n    \"detail\": {\n        \"id\": 19,\n        \"company_id\": \"233\",\n        \"company_display_number\": null,\n        \"ivr_id\": \"13433\",\n        \"source_name\": \"misscall\",\n        \"event\": 1,\n        \"iteration\": 1,\n        \"destination_name\": \"call\"\n    }\n}\nResponse:\n{\n    \"detail\": \"OK\"\n}"}, "response": []}], "_postman_id": "9f004137-dff2-4fd6-992c-dea185806b0d", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "Global Cancellation", "item": [{"name": "cancellation [GET]", "_postman_id": "83a08c4c-fb91-4222-b225-e77dce6927ac", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "{{pm_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {}}}, "url": "{{process_manager_host}}/api/1/cancellation", "description": "URI: /api/1/cancellation?request_status=2\n\nMethod: GET \nBody: raw json\nRequest:\n        {\n            \"request_id\": \"test-6\",\n            \"request_status\": \"1\"\n        }\n\n\nResponse:\n{\n    \"detail\": [\n        {\n            \"request_id\": \"test-2\",\n            \"request_status\": 2\n        },\n        {\n            \"request_id\": \"test-3\",\n            \"request_status\": 2\n        },\n        {\n            \"request_id\": \"test-4\",\n            \"request_status\": 2\n        },\n        {\n            \"request_id\": \"test-5\",\n            \"request_status\": 2\n        },\n        {\n            \"request_id\": \"test-9\",\n            \"request_status\": 2\n        }\n    ]\n}"}, "response": []}, {"name": "cancellation [POST]", "_postman_id": "8103eea9-d3ef-49cd-9fc9-cedf6a38ecd0", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Token daa66f6e7367caa02e0b4b10f9f1a9e657b7f97e", "type": "text"}], "body": {"mode": "raw", "raw": "        {\n            \"request_id\": \"0bdd5650-570a-11ea-951c-9abd3af2f6bb\",\n            \"request_status\": \"1\"\n        }", "options": {"raw": {}}}, "url": "{{process_manager_host}}/api/1/cancellation", "description": "URI: /api/1/cancellation?request_status=2\n\nMethod: POST \nBody: raw json\nRequest:\n        {\n            \"request_id\": \"test-6\",\n            \"request_status\": \"1\"\n        }\n\n\nResponse:\n{\n    \"request_id\": \"test-14\",\n    \"request_status\": 2\n}"}, "response": []}, {"name": "cancellation [PUT]", "_postman_id": "398db936-74b4-4a70-ac66-727772202e9b", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Token daa66f6e7367caa02e0b4b10f9f1a9e657b7f97e", "type": "text"}], "body": {"mode": "raw", "raw": "        {\n            \"request_id\": \"22332\",\n            \"request_status\": \"1\"\n        }", "options": {"raw": {}}}, "url": "{{process_manager_host}}/api/1/cancellation", "description": "URI: /api/1/cancellation?request_status=2\n\nMethod: PUT \nBody: raw json\nRequest:\n        {\n            \"request_id\": \"test-1\",\n            \"request_status\": \"2\"\n        }\n\n\nResponse:\n        {\n            \"request_id\": \"test-1\",\n            \"request_status\": \"2\"\n        }"}, "response": []}, {"name": "cancellation [DELETE]", "_postman_id": "351049ea-0061-41ec-9326-cc7b37e67427", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Token daa66f6e7367caa02e0b4b10f9f1a9e657b7f97e", "type": "text"}], "body": {"mode": "raw", "raw": "        {\n            \"request_id\": \"22332\",\n            \"request_status\": \"2\"\n        }", "options": {"raw": {}}}, "url": "{{process_manager_host}}/api/1/cancellation", "description": "URI: /api/1/cancellation?request_status=2\n\nMethod: PUT \nBody: raw json\nRequest:\n        {\n            \"request_id\": \"test-6\",\n        }\n\n\nResponse:\n{\n    'detail': \"OK\"\n}"}, "response": []}], "_postman_id": "97a0957b-5edc-4b16-a9b7-7a935cfd752a", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "CompletedRequests", "item": [{"name": "completed-requests [GET]", "_postman_id": "6d079951-51cd-4469-b172-095673ac9eb4", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "{{pm_token}}", "type": "text"}], "url": {"raw": "{{process_manager_host}}/api/1/completed_requests_discovery?date=20/03/2020&page=1", "host": ["{{process_manager_host}}"], "path": ["api", "1", "completed_requests_discovery"], "query": [{"key": "date", "value": "20/03/2020"}, {"key": "page", "value": "1"}]}, "description": "\n\nMethod: GET \nBody: raw json\n\nResponse:\n{\n    \"detail\": 0\n}"}, "response": []}], "_postman_id": "9a33657c-5c2d-4c01-9ec4-f8d1214edeb2", "description": "Mapping refrence id with request id ", "event": [{"listen": "prerequest", "script": {"id": "a63655f2-18c8-45f0-b909-92046eb9f818", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "fe577f1e-9650-4c1f-8b36-9dedf03b6062", "type": "text/javascript", "exec": [""]}}], "protocolProfileBehavior": {}, "_postman_isSubFolder": true}], "_postman_id": "32a4ff0a-ab36-4d3e-935f-a57d4df72161", "protocolProfileBehavior": {}}, {"name": "IVR Rules", "item": [{"name": "IVR Rules", "_postman_id": "8aa9d04f-0f2d-4639-81fb-8c0c2a87b0ef", "request": {"method": "GET", "header": [], "url": {"raw": "{{OBD_API_HOST}}/obd/ivr/1/5e902486c816b921/rules", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "ivr", "1", "5e902486c816b921", "rules"], "query": [{"key": "day", "value": "sat", "disabled": true}]}}, "response": [{"id": "4f4698b0-abfd-40c6-a7df-ae315d7be553", "name": "Validation Error", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{OBD_API_HOST}}/obd/ivr-rules?company_id={{COMPANY_ID}}", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "ivr-rules"], "query": [{"key": "company_id", "value": "{{COMPANY_ID}}"}, {"key": "ivr_id", "value": "5e3297cda390c178_17", "disabled": true}, {"key": "day", "value": "sat", "disabled": true}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.17.8"}, {"key": "Date", "value": "Thu, 26 Mar 2020 08:47:14 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "66"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"code\": \"400\",\n    \"message\": \"ivr_id is required\"\n}"}, {"id": "8caa4eed-e26d-4cfa-a9d7-1858bc7a0466", "name": "Sucess - All Days", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{OBD_API_HOST}}/obd/ivr-rules?company_id={{COMPANY_ID}}&ivr_id=5e3297cda390c178_17", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "ivr-rules"], "query": [{"key": "company_id", "value": "{{COMPANY_ID}}"}, {"key": "ivr_id", "value": "5e3297cda390c178_17"}, {"key": "day", "value": "sat", "disabled": true}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.17.8"}, {"key": "Date", "value": "Thu, 26 Mar 2020 08:44:40 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "442"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"code\": \"200\",\n    \"message\": \"\",\n    \"data\": {\n        \"mon\": {\n            \"call\": {\n                \"start_time\": \"18:30:00\",\n                \"end_time\": \"18:30:00\"\n            }\n        },\n        \"tue\": {\n            \"call\": {\n                \"start_time\": \"18:30:00\",\n                \"end_time\": \"18:30:00\"\n            }\n        },\n        \"wed\": {\n            \"call\": {\n                \"start_time\": \"18:30:00\",\n                \"end_time\": \"18:30:00\"\n            }\n        },\n        \"thu\": {\n            \"call\": {\n                \"start_time\": \"18:30:00\",\n                \"end_time\": \"18:30:00\"\n            }\n        },\n        \"fri\": {\n            \"call\": {\n                \"start_time\": \"18:30:00\",\n                \"end_time\": \"18:30:00\"\n            }\n        },\n        \"sat\": {\n            \"call\": {\n                \"start_time\": \"18:30:00\",\n                \"end_time\": \"18:30:00\"\n            }\n        },\n        \"sun\": {}\n    }\n}"}, {"id": "955f47b0-76f9-4a9c-98d6-2085bcc7ca10", "name": "Success - Single Day", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{OBD_API_HOST}}/obd/ivr-rules?company_id={{COMPANY_ID}}&ivr_id=5e3297cda390c178_17&day=sat", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "ivr-rules"], "query": [{"key": "company_id", "value": "{{COMPANY_ID}}"}, {"key": "ivr_id", "value": "5e3297cda390c178_17"}, {"key": "day", "value": "sat"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.17.8"}, {"key": "Date", "value": "Thu, 26 Mar 2020 08:46:44 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "118"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"code\": \"200\",\n    \"message\": \"\",\n    \"data\": {\n        \"sat\": {\n            \"call\": {\n                \"start_time\": \"18:30:00\",\n                \"end_time\": \"18:30:00\"\n            }\n        }\n    }\n}"}]}], "_postman_id": "c1c6067c-87ea-41f8-aa76-5e940bc8b23a", "protocolProfileBehavior": {}}, {"name": "UDC", "item": [{"name": "User and Channel Availibilty Check", "_postman_id": "38be2a7e-b745-459f-966c-1042c9d07e99", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "oLNQZfO7PtoLNQZfO7PtWXF496IGoZWXF496IGoZ", "type": "text", "disabled": true}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "", "value": "", "type": "text"}], "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{OBD_API_HOST}}/obd/availability/1/5784e83b7f4fe866/user-channel", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "availability", "1", "5784e83b7f4fe866", "user-channel"], "query": [{"key": "display_number", "value": "911204022601", "disabled": true}, {"key": "user_id", "value": "562dbe0e3843b525", "disabled": true}]}}, "response": [{"id": "60f63503-e7ff-4572-97fb-8ffced17763f", "name": "User and Channel Availibilty Check Missing Display Number", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "oLNQZfO7PtoLNQZfO7PtWXF496IGoZWXF496IGoZ", "type": "text"}], "url": {"raw": "http://localhost:8000/obd/uc-check?user_id={{COMPANY_USER}}", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["obd", "uc-check"], "query": [{"key": "display_number", "value": "{{DISPLAY_NUMBER}}", "disabled": true}, {"key": "user_id", "value": "{{COMPANY_USER}}"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 26 Mar 2020 10:38:47 GMT"}, {"key": "Server", "value": "WSGIServer/0.2 CPython/3.7.4"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Length", "value": "82"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"code\": \"400\",\n    \"message\": \"display_number is required\",\n    \"errors\": {}\n}"}, {"id": "8955f1ae-aa00-42e9-b9be-7a003bd4a30d", "name": "User and Channel Availibilty Check Success", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "oLNQZfO7PtoLNQZfO7PtWXF496IGoZWXF496IGoZ", "type": "text"}], "url": {"raw": "http://localhost:8000/obd/uc-check?display_number={{DISPLAY_NUMBER}}&user_id={{COMPANY_USER}}", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["obd", "uc-check"], "query": [{"key": "display_number", "value": "{{DISPLAY_NUMBER}}"}, {"key": "user_id", "value": "{{COMPANY_USER}}"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 26 Mar 2020 10:38:09 GMT"}, {"key": "Server", "value": "WSGIServer/0.2 CPython/3.7.4"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Length", "value": "313"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"code\": \"200\",\n    \"message\": \"\",\n    \"data\": {\n        \"is_channel_avail\": 1,\n        \"channel_data\": 100,\n        \"is_user_avail\": 1,\n        \"user_data\": {\n            \"uuid\": \"562dbe0e3843b525\",\n            \"name\": \"<PERSON><PERSON>\",\n            \"contact\": \"8527384897\",\n            \"contact_country\": \"+91\",\n            \"contact_2\": \"\",\n            \"contact_2_country\": \"\",\n            \"is_enabled\": 1,\n            \"contact_type\": \"mobile\",\n            \"is_active\": 1\n        }\n    }\n}"}, {"id": "a045219d-acdb-4a7a-9e08-474583ec06d4", "name": "User and Channel Availibilty Check user not found", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "oLNQZfO7PtoLNQZfO7PtWXF496IGoZWXF496IGoZ", "type": "text"}], "url": {"raw": "http://localhost:8000/obd/uc-check?display_number={{DISPLAY_NUMBER}}&user_id=asd", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["obd", "uc-check"], "query": [{"key": "display_number", "value": "{{DISPLAY_NUMBER}}"}, {"key": "user_id", "value": "asd"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 26 Mar 2020 10:40:32 GMT"}, {"key": "Server", "value": "WSGIServer/0.2 CPython/3.7.4"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Length", "value": "128"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"code\": \"200\",\n    \"message\": \"\",\n    \"data\": {\n        \"is_channel_avail\": 1,\n        \"channel_data\": 100,\n        \"is_user_avail\": 0,\n        \"user_data\": {}\n    }\n}"}, {"id": "ac0d05fd-9ef3-4fac-8f80-0059f78e0a7d", "name": "User and Channel Availibilty Check user id missing", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "oLNQZfO7PtoLNQZfO7PtWXF496IGoZWXF496IGoZ", "type": "text"}], "url": {"raw": "http://localhost:8000/obd/uc-check?display_number={{DISPLAY_NUMBER}}", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["obd", "uc-check"], "query": [{"key": "display_number", "value": "{{DISPLAY_NUMBER}}"}, {"key": "user_id", "value": "{{COMPANY_USER}}", "disabled": true}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 26 Mar 2020 10:39:21 GMT"}, {"key": "Server", "value": "WSGIServer/0.2 CPython/3.7.4"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Length", "value": "75"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"code\": \"400\",\n    \"message\": \"user_id is required\",\n    \"errors\": {}\n}"}, {"id": "e9f26a0c-32be-4e84-9cfa-3bd6fbc73979", "name": "User and Channel Availibilty Check display number not found", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "oLNQZfO7PtoLNQZfO7PtWXF496IGoZWXF496IGoZ", "type": "text"}], "url": {"raw": "http://localhost:8000/obd/uc-check?display_number=1223&user_id={{COMPANY_USER}}", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["obd", "uc-check"], "query": [{"key": "display_number", "value": "1223"}, {"key": "user_id", "value": "{{COMPANY_USER}}"}]}}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 26 Mar 2020 10:39:48 GMT"}, {"key": "Server", "value": "WSGIServer/0.2 CPython/3.7.4"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Length", "value": "78"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"code\": \"403\",\n    \"message\": \"Invalid Display Number\",\n    \"errors\": {}\n}"}]}, {"name": "Dept and Channel Availibilty Check", "_postman_id": "92e70a15-3df5-45eb-a5e1-021ec01f7546", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "type": "text", "value": "oLNQZfO7PtoLNQZfO7PtWXF496IGoZWXF496IGoZ", "disabled": true}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "", "value": "", "type": "text"}], "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{OBD_API_HOST}}/obd/availability/1/5e902486c816b921/dept-channel?max_users=2", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "availability", "1", "5e902486c816b921", "dept-channel"], "query": [{"key": "display_number", "value": "911204022601", "disabled": true}, {"key": "ivr_id", "value": "5e902486c816b921", "disabled": true}, {"key": "max_users", "value": "2"}]}}, "response": [{"id": "2273bf81-3734-4874-b3e1-39426074c740", "name": "Dept and Channel Availibilty Check ivr/dept not exists", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "type": "text", "value": "oLNQZfO7PtoLNQZfO7PtWXF496IGoZWXF496IGoZ"}], "url": {"raw": "{{OBD_API_HOST}}/obd/dc-check?display_number={{DISPLAY_NUMBER}}&ivr_id=12&max_users=1", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "dc-check"], "query": [{"key": "display_number", "value": "{{DISPLAY_NUMBER}}"}, {"key": "ivr_id", "value": "12"}, {"key": "max_users", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 26 Mar 2020 10:45:19 GMT"}, {"key": "Server", "value": "WSGIServer/0.2 CPython/3.7.4"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Length", "value": "155"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"code\": \"200\",\n    \"message\": \"\",\n    \"data\": {\n        \"is_channel_avail\": 1,\n        \"is_department_avail\": 0,\n        \"channel_data\": 100,\n        \"multi_department\": 0,\n        \"user_data\": []\n    }\n}"}, {"id": "4e52170e-9d27-44a5-94e3-d08bfc6a4f05", "name": "Dept and Channel Availibilty Check success", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "type": "text", "value": "oLNQZfO7PtoLNQZfO7PtWXF496IGoZWXF496IGoZ"}], "url": {"raw": "{{OBD_API_HOST}}/obd/dc-check?display_number={{DISPLAY_NUMBER}}&ivr_id={{IVR_ID}}&max_users=1", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "dc-check"], "query": [{"key": "display_number", "value": "{{DISPLAY_NUMBER}}"}, {"key": "ivr_id", "value": "{{IVR_ID}}"}, {"key": "max_users", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 26 Mar 2020 10:43:22 GMT"}, {"key": "Server", "value": "WSGIServer/0.2 CPython/3.7.4"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Length", "value": "347"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"code\": \"200\",\n    \"message\": \"\",\n    \"data\": {\n        \"is_channel_avail\": 1,\n        \"is_department_avail\": 1,\n        \"channel_data\": 100,\n        \"multi_department\": 1,\n        \"user_data\": [\n            {\n                \"uuid\": \"55486ec4942ce240\",\n                \"name\": \"<PERSON><PERSON><PERSON>\",\n                \"contact\": \"7042720160\",\n                \"contact_country\": \"+91\",\n                \"contact_2\": \"\",\n                \"contact_2_country\": \"\",\n                \"is_enabled\": 1,\n                \"contact_type\": \"mobile\",\n                \"is_active\": 1\n            }\n        ]\n    }\n}"}, {"id": "792c8f82-d4b8-4102-9e93-7c0681f7a35d", "name": "Dept and Channel Availibilty Check invalid request 2", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "type": "text", "value": "oLNQZfO7PtoLNQZfO7PtWXF496IGoZWXF496IGoZ"}], "url": {"raw": "{{OBD_API_HOST}}/obd/dc-check?display_number={{DISPLAY_NUMBER}}&max_users=1", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "dc-check"], "query": [{"key": "display_number", "value": "{{DISPLAY_NUMBER}}"}, {"key": "ivr_id", "value": "{{IVR_ID}}", "disabled": true}, {"key": "max_users", "value": "1"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 26 Mar 2020 10:44:17 GMT"}, {"key": "Server", "value": "WSGIServer/0.2 CPython/3.7.4"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Length", "value": "74"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"code\": \"400\",\n    \"message\": \"ivr_id is required\",\n    \"errors\": {}\n}"}, {"id": "7e46f075-ee1f-4289-8de0-76a17ab44450", "name": "Dept and Channel Availibilty Check display number not valid", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "type": "text", "value": "oLNQZfO7PtoLNQZfO7PtWXF496IGoZWXF496IGoZ"}], "url": {"raw": "{{OBD_API_HOST}}/obd/dc-check?display_number=asd&ivr_id={{IVR_ID}}&max_users=1", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "dc-check"], "query": [{"key": "display_number", "value": "asd"}, {"key": "ivr_id", "value": "{{IVR_ID}}"}, {"key": "max_users", "value": "1"}]}}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 26 Mar 2020 10:44:42 GMT"}, {"key": "Server", "value": "WSGIServer/0.2 CPython/3.7.4"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Length", "value": "78"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"code\": \"403\",\n    \"message\": \"Invalid Display number\",\n    \"errors\": {}\n}"}, {"id": "abe0db4c-a86c-4480-ab9b-7aad7ce21557", "name": "Dept and Channel Availibilty Check imvalid request", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "type": "text", "value": "oLNQZfO7PtoLNQZfO7PtWXF496IGoZWXF496IGoZ"}], "url": {"raw": "{{OBD_API_HOST}}/obd/dc-check?ivr_id={{IVR_ID}}&max_users=1", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "dc-check"], "query": [{"key": "display_number", "value": "{{DISPLAY_NUMBER}}", "disabled": true}, {"key": "ivr_id", "value": "{{IVR_ID}}"}, {"key": "max_users", "value": "1"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 26 Mar 2020 10:43:39 GMT"}, {"key": "Server", "value": "WSGIServer/0.2 CPython/3.7.4"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Length", "value": "82"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"code\": \"400\",\n    \"message\": \"display_number is required\",\n    \"errors\": {}\n}"}]}, {"name": "Channel Availbilty Check", "_postman_id": "40918ea9-1043-4ca7-912c-1a1ef8a9b9ab", "request": {"method": "GET", "header": [], "url": "{{OBD_API_HOST}}/obd/availability/1/channel"}, "response": [{"id": "6adad820-6475-4116-bd65-b09931552e8c", "name": "Invalid Display Number", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{OBD_API_HOST}}/obd/channel-check?display_number=9112040226011", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "channel-check"], "query": [{"key": "display_number", "value": "9112040226011"}]}}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.17.8"}, {"key": "Date", "value": "Thu, 26 Mar 2020 09:33:46 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "66"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"code\": \"403\",\n    \"message\": \"Invalid Display Number\"\n}"}, {"id": "904b2ae6-93d7-4a01-bd13-f00da6143c2b", "name": "Success", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{OBD_API_HOST}}/obd/channel-check?display_number=911204022601", "host": ["{{OBD_API_HOST}}"], "path": ["obd", "channel-check"], "query": [{"key": "display_number", "value": "911204022601"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.17.8"}, {"key": "Date", "value": "Thu, 26 Mar 2020 09:32:44 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "95"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"code\": \"200\",\n    \"message\": \"\",\n    \"data\": {\n        \"is_channel_avail\": 1,\n        \"channel_data\": 100\n    }\n}"}, {"id": "914be5ef-81b4-47c2-b4f2-5dbcc3c844a9", "name": "Validation Error", "originalRequest": {"method": "GET", "header": [], "url": "{{OBD_API_HOST}}/obd/channel-check"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.17.8"}, {"key": "Date", "value": "Thu, 26 Mar 2020 09:33:25 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "70"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Allow", "value": "GET, HEAD, OPTIONS"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"code\": \"400\",\n    \"message\": \"display_number is required\"\n}"}]}], "_postman_id": "1282597c-b57a-4cb3-97b1-04408e8aa1ec", "protocolProfileBehavior": {}}, {"name": "<PERSON>x <PERSON><PERSON>", "item": [{"name": "Mapping", "item": [{"name": "Mapping", "_postman_id": "12765fa3-f9ae-477d-b990-dd1f1e7b1370", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"mapping\": [\n\t\t{\n\t\t\t\"did\": \"11447712\",\n\t\t\t\"ivr_id\": \"5e84959c6a3ca504\"\n\t\t}\n\t]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{FIX_DID_HOST}}/fix-did/mapping/{{COMPANY_ID}}"}, "response": [{"id": "b37c774e-968f-451b-8f11-a0455109ad8a", "name": "Success", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"mapping\": [\n\t\t{\n\t\t\t\"did\": \"1231231\",\n\t\t\t\"ivr_id\": \"123\"\n\t\t}\n\t]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{FIX_DID_HOST}}/fix-did/mapping/{{COMPANY_ID}}"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"status\": \"success\",\n\t\"message\": \"IVR/DID mapped successfully\"\n}"}, {"id": "fd94269a-f9ff-4b69-8ff6-6ef47a768d7e", "name": "Invalid DID", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"mapping\": [\n\t\t{\n\t\t\t\"did\": \"1231231\",\n\t\t\t\"ivr_id\": \"123\"\n\t\t}\n\t]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{FIX_DID_HOST}}/fix-did/mapping/{{COMPANY_ID}}"}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"code\": \"FIX_DID_0008\",\n    \"message\": \"Invalid DID, DID is not booked\"\n}"}]}, {"name": "List Mapping", "_postman_id": "3b472dfa-ae35-4808-898f-ebdb7df27f17", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{FIX_DID_HOST}}/fix-did/mapping/{{COMPANY_ID}}", "host": ["{{FIX_DID_HOST}}"], "path": ["fix-did", "mapping", "{{COMPANY_ID}}"], "query": [{"key": "did", "value": "33254176", "disabled": true}, {"key": "ivr_id", "value": "5e3297cda390c178_17", "disabled": true}]}}, "response": [{"id": "59df5c67-240b-429a-97f5-63a8c2ce32c1", "name": "Success", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{FIX_DID_HOST}}/fix-did/mapping/{{COMPANY_ID}}?ivr_id=123&did=1231321", "host": ["{{FIX_DID_HOST}}"], "path": ["fix-did", "mapping", "{{COMPANY_ID}}"], "query": [{"key": "ivr_id", "value": "123"}, {"key": "did", "value": "1231321"}]}}, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"status\": \"success\",\n\t\"data\": [\n\t\t{\n\t\t\t\"ivr_id\": \"123\",\n\t\t\t\"did\": \"1231231\"\n\t\t},\n\t\t{\n\t\t\t\"ivr_id\": \"123\",\n\t\t\t\"did\": \"1231232\"\n\t\t}\n\t]\n}"}]}, {"name": "Un-Mapping", "_postman_id": "5c4bf6dc-cff8-4d36-909f-34b28e089ddb", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{FIX_DID_HOST}}/fix-did/mapping/{{COMPANY_ID}}?ivr_id=5e84959c6a3ca504&did=11447702", "host": ["{{FIX_DID_HOST}}"], "path": ["fix-did", "mapping", "{{COMPANY_ID}}"], "query": [{"key": "ivr_id", "value": "5e84959c6a3ca504"}, {"key": "did", "value": "11447702"}]}}, "response": [{"id": "08d4d279-f614-423f-9c1c-47391d48cdaa", "name": "Un-Mapping | Unmapp all IVRs for this DID", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{FIX_DID_HOST}}/fix-did/mapping/{{COMPANY_ID}}?did=123456", "host": ["{{FIX_DID_HOST}}"], "path": ["fix-did", "mapping", "{{COMPANY_ID}}"], "query": [{"key": "did", "value": "123456"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"message\": \"DID unmapped Successfully\"\n}"}, {"id": "99a35c38-a3e7-4f66-add2-985b82cd5a94", "name": "Un-Mapping | Unmap all DIDs for a IVR", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{FIX_DID_HOST}}/fix-did/mapping/{{COMPANY_ID}}?ivr_id=123", "host": ["{{FIX_DID_HOST}}"], "path": ["fix-did", "mapping", "{{COMPANY_ID}}"], "query": [{"key": "ivr_id", "value": "123"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"status\": \"success\",\n\t\"message\": \"DID unmapped Successfully\"\n}"}]}], "_postman_id": "a85294da-b142-4273-a0ad-bc3328b67166", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "DID", "item": [{"name": "List Booked DIDs", "_postman_id": "1c6f018b-ce25-4018-a0e0-2d7d863b7fde", "request": {"method": "GET", "header": [], "url": "{{FIX_DID_HOST}}/fix-did/did/{{COMPANY_ID}}"}, "response": [{"id": "4f353365-1992-40a1-9899-8481a175c870", "name": "Success", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{FIX_DID_HOST}}/fix-did/did/{{COMPANY_ID}}?fields=did,region,region_group,ivrs", "host": ["{{FIX_DID_HOST}}"], "path": ["fix-did", "did", "{{COMPANY_ID}}"], "query": [{"key": "fields", "value": "did,region,region_group,ivrs", "description": "Optional"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"status\": \"success\",\n\t\"message\": \"did list\",\n\t\"data\": [\n\t\t{\n\t\t\t\"did\": \"1\",\n\t\t\t\"region\": \"DL\",\n\t\t\t\"region_group\": {\n\t\t\t\t\"id\": \"34\",\n\t\t\t\t\"caller_id\": \"12040226\",\n\t\t\t\t\"gp_alias\": \"s4-test\"\n\t\t\t},\n\t\t\t\"ivrs\": [\n\t\t\t\t{\n\t\t\t\t\t\"id\": \"1231231\",\n\t\t\t\t\t\"name\": \"IVR-1\"\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t]\n}"}]}, {"name": "Reterive DID", "_postman_id": "c587a0ed-9f87-49c2-adcf-06ec00f394b7", "request": {"method": "GET", "header": [], "url": "{{FIX_DID_HOST}}/fix-did/did/1/11447712"}, "response": [{"id": "4c68c7aa-84f0-4b43-8562-78f769183a58", "name": "Success", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{FIX_DID_HOST}}/fix-did/did/{{COMPANY_ID}}/{{DID}}?fields=did,region,region_group,ivrs", "host": ["{{FIX_DID_HOST}}"], "path": ["fix-did", "did", "{{COMPANY_ID}}", "{{DID}}"], "query": [{"key": "fields", "value": "did,region,region_group,ivrs", "description": "Optional"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"status\": \"success\",\n\t\"message\": \"did data\",\n\t\"data\": {\n\t\t\"did\": \"1\",\n\t\t\"region\": \"DL\",\n\t\t\"region_group\": {\n\t\t\t\"id\": \"34\",\n\t\t\t\"caller_id\": \"12040226\",\n\t\t\t\"gp_alias\": \"s4-test\"\n\t\t},\n\t\t\"ivrs\": [\n\t\t\t{\n\t\t\t\t\"id\": \"1231231\",\n\t\t\t\t\"name\": \"IVR-1\"\n\t\t\t}\n\t\t]\n\t}\n}"}]}, {"name": "Usage", "_postman_id": "4a8c2321-492d-4494-a3fc-5bb841a49e1b", "request": {"method": "GET", "header": [], "url": "{{FIX_DID_HOST}}/fix-did/usage/{{COMPANY_ID}}"}, "response": []}], "_postman_id": "a4f43438-804d-4417-8b61-aab4b54ea5a0", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}], "_postman_id": "54b6f859-bfc5-461c-b0c5-6f04248fbc58", "protocolProfileBehavior": {}}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Group Servers", "item": [{"name": "group_servers", "_postman_id": "664977b2-d3e5-4ad4-8ed1-0c60b4f0c454", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text", "disabled": true}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "token", "value": "****************************************", "type": "text"}, {"key": "kam_group_id", "value": "201", "type": "text"}, {"key": "is_obd", "value": "1", "type": "text"}], "options": {"raw": {"language": "json"}}}, "url": "{{kam_host}}/group_servers"}, "response": []}, {"name": "group_server_list", "_postman_id": "a60f814d-a989-4ce5-b199-9073653b540d", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text", "disabled": true}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "token", "value": "****************************************", "type": "text"}, {"key": "is_obd", "value": "1", "type": "text"}], "options": {"raw": {"language": "json"}}}, "url": "{{kam_host}}/group_server_list"}, "response": []}], "_postman_id": "e16a9b9a-9bab-4af6-b739-f81508b6fb8a", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}], "_postman_id": "fccd02af-89aa-41cd-94b2-735d2ed43ee0", "protocolProfileBehavior": {}}], "protocolProfileBehavior": {}}