import os

from .base import *

# --------------------------------- Central_1 ---------------------------------
CENTRAL_1_ENTITY = "central_1"  # Entity name for common_settings table
CENTRAL_1 = "self"  # Threshold name for central_1
CENTRAL_PQ_GATEWAY_PREFIX = "obd-central-pq"  # gateway name for SQSInfo
CENTRAL_IVRQ_GATEWAY_PREFIX = "obd-central-ivrs"

CENTRAL_1_MAX_WORKERS = "threads_count"  # Threads count

# Central Queues Names & DB Threshold mapping.
CENTRAL_PQ1 = "obd-central-pq1"
CENTRAL_PQ2 = "obd-central-pq2"
CENTRAL_PQ3 = "obd-central-pq3"

CENTRAL_HIGH_PRIORITIES = [
    1,
    2,
]  # High priorities, will go in Priorities Queues.

CENTRAL_1_SLEEP_DUR = 1

CACHE_IVR_KEY_NAME = "obd-central-ivrid-"
CACHE_PAUSED_IVR_KEY_NAME = "obd-central-paused-ivrid-"
CACHE_EXCLUDE_IVR_KEY_NAME = "obd-central-exclude-ivrs-"
CACHE_IVR_RULE_CACHE_KEY = "obd_central_ivr_rules_up_"
CACHE_PAUSED_IVR_TTL = 120
API_CACHE_TTL = int(os.getenv("API_CACHE_TTL", 80))

DEFAULT_CALL_EXPIRY = 86400

SINGLE_IVR_THRESHOLD_KEY = "single_ivr_threshold"
CENTRAL_1_LOOP_COUNT_KEY = "central_1_loop_count"

# Default threshold in case of threhold not found in DB.
CENTRAL_1_THRESHOLD = {
    CENTRAL_1: 20,
    CENTRAL_PQ1: 10,
    CENTRAL_PQ2: 10,
    CENTRAL_PQ3: 50,
    CENTRAL_1_MAX_WORKERS: 1,
    SINGLE_IVR_THRESHOLD_KEY: 20,
    CENTRAL_1_LOOP_COUNT_KEY: 20,
}

# To check glocal request cancellation.
CANCELLATION_API_URL = os.getenv("CANCELLATION_API_URL", "")
CANCELLATION_SERVICE_CHECK_ENABLED = int(os.getenv("CANCELLATION_ENABLED", 0))
CANCELLED_REQUEST_STATUS = int(os.getenv("CANCELLED_REQUEST_STATUS", 2))


# --------------------------------- Central_2 ---------------------------------
CENTRAL_2_ENTITY = "central_2"  # Entity name for common_settings table
CENTRAL_2 = "self"  # Threshold name for central_2
CENTRAL_2_MAX_WORKERS = "threads_count"  # Threads count

CENTRAL_2_SLEEP_DUR = 1

CENTRAL_PQ1_COUNT_KEY = "central_pq1_count"
CENTRAL_PQ2_COUNT_KEY = "central_pq2_count"
CENTRAL_PQ3_COUNT_KEY = "central_pq3_count"

CENTRAL_QUEUES_COUNT_KEYS = {
    CENTRAL_PQ1: CENTRAL_PQ1_COUNT_KEY,
    CENTRAL_PQ2: CENTRAL_PQ2_COUNT_KEY,
    CENTRAL_PQ3: CENTRAL_PQ3_COUNT_KEY,
}

GROUP_MESSGAE_COUNT_KEY = "{q_url}"

# Default threshold in case of threhold not found in DB.
CENTRAL_2_THRESHOLD = {CENTRAL_2: 20, CENTRAL_2_MAX_WORKERS: 1}

CACHE_UDC_LOCKED_IVRS_KEY_NAME = "udc_locked_ivr_"
CACHE_UDC_LOCKED_IVRS_DUR_SETTING_NAME = "ivr_lock_duration"
CACHE_UDC_LOCKED_IVRS_TTL = 3  # default if not found in ivr settings

CACHE_UDC_LOCKED_USERS_KEY_NAME = "udc_locked_user_"
CACHE_UDC_LOCKED_USERS_DUR_SETTING_NAME = "user_lock_duration"
CACHE_UDC_LOCKED_USERS_TTL = 30  # default if not found in ivr settings
CACHE_UDC_LOCKED_USERS_AGENT_FIRST_TTL_SETTING_NAME = (
    "agent_first_user_lock_duration"
)
CACHE_UDC_LOCKED_USERS_AGENT_FIRST_TTL = 15

# --------------------------------- ON_HOLD ---------------------------------
ON_HOLD_ENTITY = "on_hold"
ON_HOLD_Q_GATEWAY_PREFIX = "obd-central-on-hold"
ON_HOLD_Q_NAME = "obd-central-on-hold"

# common_settings - threshold names
ON_HOLD_CIRCULATION_REQ_PROCESS_COUNT = "on_hold_circulation_req_process_count"
ON_HOLD_DUPLICATE_DELAY = "on_hold_duplicate_delay"
ON_HOLD_MAX_ATTEMPTS = "on_hold_max_attempts"
ON_HOLD_MAX_USER_CHECK_COUNT = "on_hold_max_user_check_count"
ON_HOLD_MAX_IVR_THRESHOLD = "on_hold_max_ivr_threshold"
ON_HOLD_MAX_WORKERS = "threads_count"

ON_HOLD_COUNT_KEY = "on_hold_count"

ON_HOLD_SLEEP_DUR = 5
ON_HOLD_CIRCULATOR_SLEEP_DUR = 1

LOCKED_IDS_PREFIX = "on-hold-"
LOCKED_IDS_TTL = 600  # onhold multi handler

# Default threshold in case of threhold not found in DB.
ON_HOLD_THRESHOLD = {
    ON_HOLD_CIRCULATION_REQ_PROCESS_COUNT: 30,
    ON_HOLD_DUPLICATE_DELAY: 10,
    ON_HOLD_MAX_ATTEMPTS: 5,
    ON_HOLD_MAX_USER_CHECK_COUNT: 5,
    ON_HOLD_MAX_IVR_THRESHOLD: 10,  # for paused IVRs
    ON_HOLD_MAX_WORKERS: 1,
}


# ----------------------------- Routing Manager -------------------------
OBD_SERVICES = {
    "source_name": os.getenv("SOURCE_NAME", "call"),
    "cancel_service": os.getenv("CANCEL_SERVICE", "cancel_service"),
    "response_manager": os.getenv("RESPONSE_MANAGER", "response_manager"),
    "expiry_manager": os.getenv("EXPIRY_MANAGER", "expiry_manager"),
    "complete_service": os.getenv("COMPLETE_SERVICE", "complete_service"),
}

ROUTING_INFO_URL = os.getenv(
    "ROUTING_INFO_URL", "http://15.206.200.30/api/1/routinginfo"
)
PROCESS_MANAGER_AUTH_TOKEN = os.getenv("PM_AUTH_TOKEN", "****************")

SERVICE_ROUTER_GATEWAY_PREFIX = "service_router"

ROUTING_INFO_API_DATA = {
    "method": "GET",
    "url": ROUTING_INFO_URL,
    "data": {
        "body": {},
        "headers": {"Authorization": "Token " + PROCESS_MANAGER_AUTH_TOKEN},
        "params": {"name": ""},
    },
}

SERVICE_ROUTER_FLAG = "service_flow_router"

SERVICE_ROUTER_QUEUE = os.getenv(
    "SERVER_ROUTE_QUEUE_NAME", "obd_service_route_queue"
)

ROUTING_INFO_URL_NAMES = {
    "ivr_rule_route_name": os.getenv("IVR_RULE_ROUTE_NAME", "ivr_rule"),
    "process_flow_router_name": os.getenv(
        "PROCESS_FLOW_ROUTER_NAME", "process_flow_get"
    ),
    "kam_group_servers_route_name": os.getenv(
        "KAM_GROUP_SERVERS_ROUTE_NAME", "kam_group_servers"
    ),
    "kam_group_servers_list_route_name": os.getenv(
        "KAM_GROUP_SERVERS_LIST_ROUTE_NAME", "kam_group_server_list"
    ),
    "global_cancellation_route_name": os.getenv(
        "GLOBAL_CANCELLATION_ROUTE_NAME", "global_cancellation"
    ),
    "completed_requests_discovery_route_name": os.getenv(
        "COMPLETED_REQUESTS_ROUTE_NAME", "completed_requests_discovery"
    ),
}

SECRET_TOKENS = {
    "kam_group_servers_token": os.getenv(
        "KAM_GROUP_SERVERS_TOKEN", "16c32ccf89a8f9639eecc42c2365a6b516dc8e8a"
    )
}

# routing_info_name_data_override
NATIVE_SERVICE_FIELD_OVERRIDE = {"call": {"req_id": "request_id"}}

INTRA_API_RETRY_DELAY = 2
INTRA_API_MAX_RETRIES = 3
INTRA_API_TIMEOUT = 10
EXTERNAL_API_TIMEOUT = 10
RETRY_STATUS_CODES = ["400", "5"]
SERVICE_NAMES_VARS_LIST = ["source_name"]

ignore_error_404 = True

# --------------------------------- UDC API ---------------------------------
UDC_API_BASE_URL = os.getenv(
    "UDC_API_BASE_URL", "https://microapi.myoperator.dev/obd/availability"
)
UDC_API_BASE_URL = UDC_API_BASE_URL.strip("/")
UDC_CHANNEL_API_URL = "{udc_base}/{company_id}/channel"
UDC_USER_CHANNEL_API_URL = "{udc_base}/{company_id}/{user_id}/user-channel?user_is_avail={user_is_avail}&user_view_only={user_view_only}&ivr_type={ivr_type}"
UDC_USER_DEPARTMENT_API_URL = "{udc_base}/{company_id}/{ivr_id}/dept-channel?is_user_required={is_user_required}&user_view_only={user_view_only}&dept_book_duration={book_duration}"

RULE_API_BASE_URL = os.getenv(
    "RULE_API_BASE_URL", "https://microapi.myoperator.dev/obd/ivr/"
)
RULE_API_BASE_URL = RULE_API_BASE_URL.strip("/")
RULE_API_URL = "{rule_base}/{company_id}/{ivr_id}/calendar/validate"

UDC_API_TOGGLE_KEY = "availability_check"
DEFAULT_UDC_API_TOGGLE_VALUE = 1

BOOK_USER_TOGGLE_KEY = "book_user"
DEFAULT_BOOK_USER_TOGGLE_VALUE = 1

# ----------------------------- KAMAILIO ---------------------------------
KAM_API_CACHE_TTL = int(os.getenv("KAM_API_CACHE_TTL", 900))
KAM_GROUP_SERVER_CACHE_KEY = "kam_group_server"
KAM_GROUP_SERVER_LIST_CACHE_KEY = "kam_group_server_list"


# ----------------------------- Process Manager -----------------------------
PROCESS_MANAGER_GATEWAY_PREFIX = "process_manager"
PROCESS_MANAGER_QUEUE = "obd_pm_invoker"


# ----------------------------- Response Manager -----------------------------
RESPONSE_MANAGER_ENTITY = "response_manager"
RESPONSE_MANAGER_GATEWAY_PREFIX = "response_manager"
RESPONSE_MANAGER_QUEUE = "obd_central_rm"
RM_SLEEP_DUR = 5
RESPONSE_MANANGER_COUNT_KEY = "central_response_msg_count"

# for threading
RESPONSE_MANAGER_MAX_WORKERS = "threads_count"
RESPONSE_MANAGER_THRESHOLD = {RESPONSE_MANAGER_MAX_WORKERS: 1}


# ----------------------------- Central Delay ---------------------------
CENTRAL_DELAY_QUEUE_GATEWAY_PREFIX = "central_delay"
CENTRAL_DELAY_QUEUE = "obd_central_delay_queue"
DELAY_QUEUE_SLEEP_DUR = 5
CENTRAL_DELAY_COUNT_KEY = "central_delay_count"


# --------------------------------- Seeds ---------------------------------
TESTINGS_IVRS = ["1", "2", "3", "4", "5"]
REQUESTS_COUNT = 100


# ---------------------------- Expiry Manager ----------------------------
EXPIRY_MANAGER_SLEEP_DUR = 5


# -------------------------- COMPANY USAGE CHECK -------------------
COMAPNY_USAGE_CHECK_QUEUE_NAME = "obd_central_company_usage_check"
COMAPNY_USAGE_CHECK_GATEWAY_PREFIX = "company_usage_check"
COMAPNY_USAGE_CHECK_SLEEP_DUR = 10

COMAPNY_USAGE_CHECK_COUNT_KEY = (
    "obd_central_company_usage_check_msg_count"  # incremented by notif lambda
)


# --------------------------- SQS Manager -----------------------------
# used while creating group queues
OBD_QUEUES_TAG = {
    "key": os.getenv("OBD_QUEUE_TAG_KEY", "obd"),
    "value": os.getenv("OBD_QUEUE_TAG_VALUE", "obd"),
}
SQS_WAIT_TIME_LONG_POLLING = 10
GROUP_Q_VISIBILITY_TIMEOUT = 300
IVR_Q_VISIBILITY_TIMEOUT = 120
GROUP_Q_RETENTION_PERIOD = 86400

# ----------------------- Group IVRINFO RELATION MANAGER --------------------
GROUP_IVRINFO_RELETION_QUEUE = "obd-central-group-ivr-relation"
GROUP_IVRINFO_RELETION_QUEUE_GATEWAY_PREFIX = "group-ivr-relation"
GROUP_IVRINFO_SLEEP_DUR = 10
GROUP_IVRINFO_LOOP_COUNT = 100

GROUP_IVRINFO_RELETION_COUNT_KEY = (
    "obd_central_group_ivrinfo_reletion_msg_count"  # noqa E501
)

GROUP_IVR_RELATION_SQS_EVENT_ACTIONS = {
    "map_action": "map_did_ivr",
    "unmap_action": "unmap_did_ivr",
    "discard_actions": ["book_did", "release_did"],
}

GROUP_IVR_RELATION_SQS_DATA_FIELDS = {
    "fixed_did": "did",
    "company_id": "company_id",
}

FIXDID_API_BASE_URL = os.getenv(
    "FIXDID_API_BASE_URL", "https://microapi.myoperator.dev/fix-did/"
)
FIXDID_API_BASE_URL = FIXDID_API_BASE_URL.strip("/")
FIXDID_API_URL = "{fixdid_base}/did/{company_id}/{did}"

FIX_DID_VERSION = "v1"

FIX_DID_OUTGOING_TYPE = 1
# --------------------------- Cache Manager ---------------------------
REDIS_VERSION_KEY = os.getenv("SHARED_REDIS_VERSION", "cs")

CANCELED_REQUEST_KEY = os.getenv(
    "CANCELED_REQUEST_KEY", "obd-central-canceled-request_ids-"
)
CANCELED_REQUESTS_KEY_TTL = os.environ.get("CANCELED_REQUEST_KEY_TTL", 3630)


# ---------------------------- Notif APIs ------------------------
NOTIF_HANDLER_RUN_COUNT = 6  # for celery to run the notif handler


# --------------------------- Helper Conf for  Group Queues ---------------
GROUPS_ENTITY = "groups"
GROUPS_GATEWAY = "obd-central-group-queues"

GROUPS_PQ_KEY = "p_q"
GROUPS_NPQ_KEY = "np_q"

GROUP_QUEUE_THRESHOLD = {GROUPS_PQ_KEY: 10, GROUPS_NPQ_KEY: 30}

MAX_ACCEPTED_HANDLER_THREADS = 20
SAMPLING_PROBABILITY = 0.1


# --------------------------------- DB Cleaner -----------------------
GRT_PULLER_FILE_PATH = root("../db_cleaner/grt_puller")
S3_PUSHER_FILE_PATH = root("../db_cleaner/s3_pusher")

DB_CLEANER_ENTITY = "db_cleaner"
DB_CLEANER_SETTINGS = {
    "grt_puller": {"previous_day": 1},
    "s3_pusher": {
        "backup_days": 7,
        "s3_bucket": "obd_central",
    },
}
DB_CLEANER_SETTINGS_NAME = "settings"

# -------------------------- Trash Group Q Availability ----------------------
GQA_SLEEP_DUR = 3

# Free resources mapping key for redis
CACHE_PQ_FREE_RESOURCES = "total_priority_free_resources"
CACHE_NPQ_FREE_RESOURCES = "total_non_priority_free_resources"
CACHE_GQA_TTL = 3600

# ------------------------- Cache Consistency Checker --------------------
CHECK_LIST = {
    # In TYPE1 there are multiple queues, where queue_name and cache_key_name of each queue is same and their prefix is fixed.
    "TYPE1": [CACHE_IVR_KEY_NAME],
    # In TYPE2 there are multiple queues, where queue_url and cache_key_name of each queue is same for shared cache.
    "TYPE2": [GROUPS_GATEWAY],
    # In TYPE3 there is only one queue and cache_key and queue_name both are given
    "TYPE3": {
        CENTRAL_PQ1_COUNT_KEY: CENTRAL_PQ1,
        CENTRAL_PQ2_COUNT_KEY: CENTRAL_PQ2,
        CENTRAL_PQ3_COUNT_KEY: CENTRAL_PQ3,
        ON_HOLD_COUNT_KEY: ON_HOLD_Q_NAME,
        CENTRAL_DELAY_COUNT_KEY: CENTRAL_DELAY_QUEUE,
        RESPONSE_MANANGER_COUNT_KEY: RESPONSE_MANAGER_QUEUE,
        COMAPNY_USAGE_CHECK_COUNT_KEY: COMAPNY_USAGE_CHECK_QUEUE_NAME,
        GROUP_IVRINFO_RELETION_COUNT_KEY: GROUP_IVRINFO_RELETION_QUEUE,
    },
}


# ----------------------------- IVR Queue Remover ------------------------
IVR_QUEUE_INACTIVITY_CHECK_DUR = 60


# ------------------------ Data Deletion ------------------------
DATA_DELETION_ENTITY = "data_deletion"  # Entity name for common_settings table

DATA_KEEP_DAYS = "data_keep_days"
DATA_DELETE_CHUNK_SIZE = "data_delete_chunk_size"
DATA_DELETE_SLEEP_DUR = "data_delete_sleep_dur"
DATA_DELETE_MODELS_LIST = "data_delete_models_list"

# Default threshold in case of threshold not found in DB.
DATA_DELETION_THRESHOLD = {
    DATA_KEEP_DAYS: 40,
    DATA_DELETE_CHUNK_SIZE: 200,
    DATA_DELETE_SLEEP_DUR: 10,
    DATA_DELETE_MODELS_LIST: ["CentralRequestInfo"],
}


# ---------------------- SQS Attributes Changer ------------------

QUEUES_PREFIX_LIST = [
    "obd-central-group-queues_",
    "obd-central-group-ivr-relation",
    "obd-central-ivrid-",
    "obd-central-on-hold",
    "obd_central_company_usage_check",
    "obd_central_delay_queue",
    "obd_central_rm",
    "obd_pm_invoker",
    "obd_service_route_queue",
    "obd-central-pq",
]


JP_JOB_ABORT_CACHE_KEY = "obd_jp_job_abort_"
JP_JOB_ABORT_CACHE_KEY_TTL = 432000  # 5 days

JP_JOB_ABORT_CENTRAL_CACHE_KEY = "obd_central_jp_job_abort_"
JP_JOB_ABORT_CENTRAL_CACHE_KEY_TTL = 432000  # 5 days


JP_REQUESTS_LIST_API_BASE_URL = os.getenv(
    "JP_REQUESTS_LIST_API_BASE_URL", "http://localhost:8002/api/1/"
)

JP_REQUESTS_LIST_API = (
    JP_REQUESTS_LIST_API_BASE_URL + "jobs/{job_id}/requests/"
)

JP_REQUESTS_LIST_API_TOKEN = os.getenv(
    "JP_REQUESTS_LIST_API_TOKEN", "22a6e9742ae13c28deaff91fe62aa6deaae5e9ed"
)


# Job Processor task request info request status
JP_TRI_REQUEST_STATUS = {
    "paused": 2,
    "running": 1,
    "abort": 0,
    "completed": 4,
    "scheduled": 3,
}


JOB_STATUS_SYNC_HANDLER_ENTITY = "job_status_sync_handler"

JP_SYNC_STATUS_PER_REQUEST_LIMIT = "jp_sync_status_per_request_limit"
JOB_STATUS_SYNC_HANDLER_THRESHOLD = {JP_SYNC_STATUS_PER_REQUEST_LIMIT: 50}


REQUEST_CHANNEL_CACHE_TTL: int = int(
    os.getenv("REQUEST_CHANNEL_CACHE_TTL", 60 * 60)
)  # 1 hour


TRUE_CALLER_DEFAULT_COMMON_SETTINGS = {
    "enabled": TRUE_CALLER_ENABLED,
    "company_ids": [],
}
