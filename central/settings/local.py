from .constant import *

# https://docs.djangoproject.com/en/dev/ref/settings/#debug
DEBUG = True


DATABASES["default"]["OPTIONS"] = {"charset": "utf8mb4"}
ALLOWED_HOSTS = ["127.0.0.1", "*"]


CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://" + REDIS_HOST + ":" + REDIS_PORT,
        "TIMEOUT": None,
        "OPTIONS": {
            "DB": REDIS_DB,
        },
    }
}

INSTALLED_APPS += ["debug_toolbar"]

MIDDLEWARE += ["debug_toolbar.middleware.DebugToolbarMiddleware"]

# Override any variable here from constants.py to run on local
