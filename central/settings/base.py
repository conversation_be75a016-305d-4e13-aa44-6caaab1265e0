# -*- coding: utf-8 -*-
"""
Django settings for central project.

"""
import logging
import logging.config
import os

from dotenv import load_dotenv

load_dotenv()

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
root = lambda *x: os.path.join(BASE_DIR, *x)  # noqa E731


REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = os.getenv("REDIS_PORT", "6379")
REDIS_DB = int(os.getenv("REDIS_DB", "0"))


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/1.11/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv(
    "DJANGO_SECRET", "i7*po9$q$puqp7=s=&+idxu592qy^94xnf76fs2(y-1ve$ws!-"
)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

LOG_FILENAME = os.getenv("LOGFILE_PATH", "common.log")

ALLOWED_HOSTS = ["127.0.0.1", "*"]


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_extensions",
    "rest_framework",
    "cacheops",
    "storages",
    "rest_framework.authtoken",
    "corsheaders",
    "drf_yasg",
]

PROJECT_APPS = [
    "handlers",
    "utills",
    "api",
    "test_suite",
]

INSTALLED_APPS += PROJECT_APPS

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "central.urls"
CORS_ORIGIN_ALLOW_ALL = True


TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "central.wsgi.application"


# Database
# https://docs.djangoproject.com/en/1.11/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": os.getenv("DB_ENGINE", default="django.db.backends.mysql"),
        "NAME": os.getenv("DB_NAME"),
        "USER": os.getenv("DB_USER"),
        "PASSWORD": os.getenv("DB_PASSWORD"),
        "HOST": os.getenv("DB_HOST"),
        "PORT": os.getenv("DB_PORT"),
    }
}

if os.getenv("READ_DB_NAME"):
    DATABASES["onhold_circulation"] = {
        "ENGINE": os.getenv(
            "READ_DB_ENGINE", default="django.db.backends.mysql"
        ),
        "NAME": os.getenv("READ_DB_NAME"),
        "USER": os.getenv("READ_DB_USER"),
        "PASSWORD": os.getenv("READ_DB_PASSWORD"),
        "HOST": os.getenv("READ_DB_HOST"),
        "PORT": os.getenv("READ_DB_PORT"),
    }

# Password validation
# https://docs.djangoproject.com/en/1.11/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/1.11/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_L10N = True

USE_TZ = False


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/1.11/howto/static-files/

STATIC_URL = "/static/"
STATIC_ROOT = root("static")

STATICFILES_DIRS = (root("assets"),)

SITE_ID = 1

ROOT_URLCONF = "central.urls"


STATICFILES_FINDERS = (
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
)


AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
REGION_NAME = os.getenv("AWS_REGION_NAME")


CACHEOPS_REDIS = "redis://" + REDIS_HOST + ":" + REDIS_PORT

CACHEOPS = {
    # 'CompanyMisCallInfo.*': {'ops': 'all', 'timeout': 60*15},
    "handlers.*": {"ops": "all", "timeout": 60 * 60},
}

CACHEOPS_PREFIX = lambda _: "ccops_"  # noqa E731
CACHEOPS_DEGRADE_ON_FAILURE = True
CACHEOPS_LRU = True


LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {
        "require_debug_false": {
            "()": "django.utils.log.CallbackFilter",
            "callback": lambda r: not DEBUG,
        },
    },
    "handlers": {
        # Include the default Django email handler for errors
        # This is what you'd get without configuring logging at all.
        "mail_admins": {
            "class": "django.utils.log.AdminEmailHandler",
            "level": "ERROR",
            # But the emails are plain text by default - HTML is nicer
            "include_html": True,
        },
        # Log to a text file that can be rotated by logrotate
        "logfile": {
            "class": "logging.handlers.WatchedFileHandler",
            "filename": LOG_FILENAME,
            "formatter": "json",
        },
        "json": {
            "class": "logging.FileHandler",
            "filename": LOG_FILENAME,
            "formatter": "json",
        },
        "console": {
            "level": "NOTSET",
            "class": "logging.StreamHandler",
            "formatter": "json",
        },
    },
    "formatters": {
        "json": {
            "format": "%(pathname)s %(asctime)s %(name)s %(process)s %(levelname)s %(lineno)d  %(message)s",
            "class": "central.logging.CustomJsonFormatter",
        }
    },
    "root": {"level": "INFO", "handlers": ["console"]},
    "loggers": {
        # Again, default Django configuration to email unhandled exceptions
        "django.request": {
            "handlers": [
                "console",
            ],
            "level": "INFO",
            "propagate": False,
        },
        # Might as well log any errors anywhere else in Django
        "django": {
            "handlers": [
                "console",
            ],
            "level": "INFO",
            "propagate": False,
        },
        # Your own app
        "central": {
            "handlers": [
                "console",
            ],
            "level": "INFO",
            "propagate": False,
        },
    },
}

API_LOGGER = logging.getLogger("central")
API_LOGGER.propagate = False

logging.TEMPINFO = 21
logging.addLevelName(logging.TEMPINFO, "TEMPINFO")
API_LOGGER.tempinfo = lambda msg, *args, **kwargs: API_LOGGER._log(
    logging.TEMPINFO, msg, args, kwargs
)


CACHED_VIEWS = {
    "home": 86400,
}

REST_FRAMEWORK = {
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 10,
    "DEFAULT_THROTTLE_CLASSES": [
        "rest_framework.throttling.ScopedRateThrottle"
    ],
    "DEFAULT_THROTTLE_RATES": {"notif": "1/minute"},
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework.authentication.TokenAuthentication",
    ),
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
}

###################### Shared Redis #########################
SHARED_REDIS_HOST = os.getenv("SHARED_REDIS_HOST")
SHARED_REDIS_PORT = os.getenv("SHARED_REDIS_PORT")
SHARED_REDIS_DB = os.getenv("SHARED_REDIS_DB")

IN_TESTING = False

DEFAULT_AUTO_FIELD = "django.db.models.AutoField"

TRACE_ID_HEADER: str = "X-MYOP-TRACE-ID"
USER_AGENT = "central"


OBD_INTERNAL_API_URL = os.getenv("OBD_INTERNAL_API_URL")


OBD_INTERNAL_API_RETRY_DEFAULT_STATUS_CODES = [408, 429, 422, 412]
OBD_INTERNAL_API_AMI_ISSUE_STATUS_CODE = [
    424,
]
OBD_INTERNAL_API_RETRY_STATUS_CODES = (
    OBD_INTERNAL_API_RETRY_DEFAULT_STATUS_CODES
    + OBD_INTERNAL_API_AMI_ISSUE_STATUS_CODE
)


OBD_API_AVAIL_CACHE_TTL = os.getenv(
    "OBD_API_AVAIL_CACHE_TTL", 60 * 2
)  # defines default cache ttl to stop picking aws groups as the api is not working!!


TRUE_CALLER_ENABLED = (
    os.getenv("TRUE_CALLER_ENABLED", "False").lower() == "true"
)

TRUE_CALLER_API_URL = os.getenv(
    "TRUE_CALLER_API_URL"
)  # https://enterprise-portal-noneu.truecaller.com

TRUE_CALLER_KEY_ID = os.getenv("TRUE_CALLER_KEY_ID")
TRUE_CALLER_CLIENT_ID = os.getenv("TRUE_CALLER_CLIENT_ID")
TRUE_CALLER_API_KEY = os.getenv("TRUE_CALLER_API_KEY")


CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/1")
CELERY_RESULT_BACKEND = os.getenv(
    "CELERY_RESULT_BACKEND_URL", "redis://localhost:6379/1"
)
CELERY_ENABLE_UTC = True
CELERY_TIMEZONE = "UTC"
