import os

DJANGO_SETTINGS_MODULE = os.getenv("DJANGO_SETTINGS_MODULE")

if DJANGO_SETTINGS_MODULE == "central.settings.production":
    from ..production import *

elif DJANGO_SETTINGS_MODULE == "central.settings.local":
    from ..local import *

elif DJANGO_SETTINGS_MODULE == "central.settings.testing":
    from ..testing import *

else:
    raise Exception(
        "Unrecognized settings module: %s" % DJANGO_SETTINGS_MODULE
    )
