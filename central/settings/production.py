import os

from .constant import *

# GENERAL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#debug
DEBUG = False

ALLOWED_HOSTS = (
    os.getenv("DJANGO_ALLOWED_HOSTS", ",".join(i for i in ALLOWED_HOSTS))
    .strip()
    .split(",")
)

DATABASES["default"]["OPTIONS"] = {
    "charset": "utf8mb4",
    "init_command": "SET sql_mode='STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO'",
}


CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://" + REDIS_HOST + ":" + REDIS_PORT,
        "TIMEOUT": None,
        "OPTIONS": {
            "DB": REDIS_DB,
        },
    },
    "fallback_cache": {
        "BACKEND": "django.core.cache.backends.dummy.DummyCache",
    },
}


LOGGING["handlers"]["slack_admins"] = {
    "level": "ERROR",
    "filters": ["require_debug_false"],
    "class": "utills.logger_handler.SlackExceptionHandler",
}
LOGGING["root"] = {"level": LOG_LEVEL, "handlers": ["logfile", "slack_admins"]}

LOGGING["loggers"]["django.request"] = {
    "handlers": ["logfile", "slack_admins"],
    "level": "ERROR",
    "propagate": False,
}
LOGGING["loggers"]["central"] = {
    "handlers": ["logfile", "slack_admins"],
    "level": LOG_LEVEL,
    "propagate": False,
}


COMAPNY_USAGE_CHECK_QUEUE_NAME = ""
# CELERY_RESULT_BACKEND = 'redis://' + REDIS_HOST + ':6380/0'

LOGGER_SLACK_URL = os.getenv("LOGGER_SLACK_URL")
