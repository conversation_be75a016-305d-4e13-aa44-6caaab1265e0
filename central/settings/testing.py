import os

from fakeredis import FakeConnection

from .constant import *

logging.getLogger("faker").setLevel(logging.INFO)
DEBUG = True
IN_TESTING = True

LOG_FILENAME = "common.log"
ALLOWED_HOSTS = ["127.0.0.1", "*"]

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
    }
}

# remove `main_cache` from settings.CACHES
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://test",
        "KEY_PREFIX": "chat",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"connection_class": FakeConnection},
        },
    },
}
LOGGING["handlers"]["console"]["level"] = "DEBUG"
LOGGING["loggers"]["central"]["level"] = "DEBUG"
LOGGING["loggers"]["responses"] = {"level": "WARNING"}


TEMPLATES[0]["OPTIONS"]["debug"] = DEBUG

LOGGER_SLACK_URL = ""
REGION_NAME = os.getenv("AWS_REGION_NAME", "ap-southeast-1")

CELERY_TASK_EAGER_PROPAGATES = True
CELERY_TASK_ALWAYS_EAGER = True
