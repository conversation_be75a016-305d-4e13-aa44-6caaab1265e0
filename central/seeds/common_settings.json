[{"model": "handlers.CommonSetting", "fields": {"entity": "central_1", "settings": "{ \"threshold\": {\"self\": 20, \"obd-central-pq2\": 10, \"obd-central-pq3\": 50 }}", "added_on": "2020-01-13 00:00:00", "updated_on": "2020-01-13 00:00:00"}}, {"model": "handlers.CommonSetting", "fields": {"entity": "central_2", "settings": "{ \"threshold\": { \"self\": 20 }}", "added_on": "2020-01-13 00:00:00", "updated_on": "2020-01-13 00:00:00"}}, {"model": "handlers.CommonSetting", "fields": {"entity": "gp_queue_availability", "settings": "{ \"threshold\": { \"self\": 30, \"group-pq\": 10, \"group-npq\": 20 }}", "added_on": "2020-01-13 00:00:00", "updated_on": "2020-01-13 00:00:00"}}, {"model": "handlers.CommonSetting", "fields": {"entity": "on_hold", "settings": "{ \"threshold\": { \"on_hold_circulation_req_process_count\": 30, \"on_hold_duplicate_delay\": 1, \"on_hold_max_attempts\": 5, \"on_hold_max_user_check_count\": 5, \"on_hold_max_ivr_threshold\": 10 }}", "added_on": "2020-01-13 00:00:00", "updated_on": "2020-01-13 00:00:00"}}]