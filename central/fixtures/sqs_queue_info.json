[{"model": "handlers.SQSQueueInfo", "fields": {"gateway_prefix": "obd-central-pq", "queue_name": "obd-central-pq1", "queue_url": "", "added_on": "2020-01-13 00:00:00", "updated_on": "2020-01-13 00:00:00"}}, {"model": "handlers.SQSQueueInfo", "fields": {"gateway_prefix": "obd-central-pq", "queue_name": "obd-central-pq2", "queue_url": "", "added_on": "2020-01-13 00:00:00", "updated_on": "2020-01-13 00:00:00"}}, {"model": "handlers.SQSQueueInfo", "fields": {"gateway_prefix": "obd-central-pq", "queue_name": "obd-central-pq3", "queue_url": "", "added_on": "2020-01-13 00:00:00", "updated_on": "2020-01-13 00:00:00"}}, {"model": "handlers.SQSQueueInfo", "fields": {"gateway_prefix": "obd-central-on-hold", "queue_name": "obd-central-on-hold", "queue_url": "", "added_on": "2020-01-13 00:00:00", "updated_on": "2020-01-13 00:00:00"}}, {"model": "handlers.SQSQueueInfo", "fields": {"gateway_prefix": "service_router", "queue_name": "obd_service_route_queue", "queue_url": "", "added_on": "2020-01-13 00:00:00", "updated_on": "2020-01-13 00:00:00"}}, {"model": "handlers.SQSQueueInfo", "fields": {"gateway_prefix": "process_manager", "queue_name": "obd_pm_invoker", "queue_url": "", "added_on": "2020-01-13 00:00:00", "updated_on": "2020-01-13 00:00:00"}}, {"model": "handlers.SQSQueueInfo", "fields": {"gateway_prefix": "response_manager", "queue_name": "obd_central_rm", "queue_url": "", "added_on": "2020-01-13 00:00:00", "updated_on": "2020-01-13 00:00:00"}}]