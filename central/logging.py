import logging
import uuid
from contextlib import contextmanager
from logging import Log<PERSON><PERSON>ord
from threading import local as Local
from typing import Any, Dict

from pythonjsonlogger.jsonlogger import JsonFormatter

logger = logging.getLogger(__name__)


def get_uuid():
    """Returns a uuid in hex format."""
    return uuid.uuid4().hex


class Config(Local):
    def __init__(self):
        # initialize for all thread
        self.uid = get_uuid()
        self.ivr_id = None
        self.request_id = None
        self.company_id = None


cfg = Config()


def configure_logging(uid="", ivr_id=None, request_id=None, company_id=None):
    cfg.uid = uid or getattr(cfg, "uid", get_uuid())
    cfg.ivr_id = ivr_id
    cfg.request_id = request_id
    cfg.company_id = company_id
    return cfg


def get_config():
    return cfg


def reset_uid(uid=None):
    uid = uid or get_uuid()
    logger.info(f"Changing uid: {cfg.uid} to {uid}")
    cfg.uid = uid or getattr(cfg, "uid", get_uuid())


def reset_logging_config():
    logger.debug("Resetting logging config!!")
    configure_logging()


class CustomJsonFormatter(JsonFormatter):
    def add_fields(
        self,
        log_record: Dict[str, Any],
        record: LogRecord,
        message_dict: Dict[str, Any],
    ) -> None:
        super().add_fields(log_record, record, message_dict)

        log_record = self.add_trace_id(log_record)
        log_record = self.add_ivr_id(log_record)
        log_record = self.add_request_id(log_record)
        log_record = self.add_company_id(log_record)

    def add_trace_id(self, log_record):
        if not log_record.get("trace_id"):
            log_record["trace_id"] = get_config().uid
        return log_record

    def add_ivr_id(self, log_record):
        if get_config().ivr_id and not log_record.get("ivr_id"):
            log_record["ivr_id"] = get_config().ivr_id
        return log_record

    def add_request_id(self, log_record):
        if get_config().request_id and not log_record.get("request_id"):
            log_record["request_id"] = get_config().request_id
        return log_record

    def add_company_id(self, log_record):
        if get_config().company_id and not log_record.get("company_id"):
            log_record["company_id"] = get_config().company_id
        return log_record


@contextmanager
def logging_cri_context(
    cri_obj,
    ivr_id_attr="ivr_id",
    request_id_attr="request_id",
    company_id_attr="c_id",
    is_reset_uid=False,
):
    if is_reset_uid:
        reset_uid()

    configure_logging(
        ivr_id=getattr(cri_obj, ivr_id_attr, None),
        request_id=getattr(cri_obj, request_id_attr, None),
        company_id=getattr(cri_obj, company_id_attr, None),
    )

    try:
        yield
    finally:
        # Teardown logging
        reset_logging_config()


@contextmanager
def logging_request_context(
    ivr_id=None,
    request_id=None,
    company_id=None,
):
    configure_logging(
        ivr_id=ivr_id, request_id=request_id, company_id=company_id
    )

    try:
        yield
    finally:
        # Teardown logging
        reset_logging_config()
