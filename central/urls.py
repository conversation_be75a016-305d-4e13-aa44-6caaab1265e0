"""central URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
	https://docs.djangoproject.com/en/1.11/topics/http/urls/
Examples:
Function views
	1. Add an import:  from my_app import views
	2. Add a URL to urlpatterns:  url(r'^$', views.home, name='home')
Class-based views
	1. Add an import:  from other_app.views import Home
	2. Add a URL to urlpatterns:  url(r'^$', Home.as_view(), name='home')
Including another URLconf
	1. Import the include() function: from django.conf.urls import url, include
	2. Add a URL to urlpatterns:  url(r'^blog/', include('blog.urls'))
"""
from django.conf.urls import url
from django.contrib import admin
from rest_framework.authtoken import views

from api.views import (
	ALBHealthCheck, CompanyDisplayNumbers, CompanyIvrsList, CompanyUsageCheck,
	GroupIVR, IvrInfoDiscovery, NotifCompanyUsageCheck, NotifGroupIVR, ServerGroups,
	IvrActions, IvrRequestsCount, DisabledUsers, JunctionRequestsCount, JobEventAPIView
)

# from drf_yasg.views import get_schema_view
# from drf_yasg import openapi
# from rest_framework import permissions
# from drf_yasg.generators import OpenAPISchemaGenerator
# from utills.swagger_custom import schema_view

urlpatterns = [
	url(r'^$', ALBHealthCheck.as_view(), name='home'),
	url(r'^admin/', admin.site.urls),
	url(r'^api/(?P<version>[1])/ivrinfo/(?P<id>.+)$', IvrInfoDiscovery.as_view(), name='api.get_ivr_info'),
	url(r'^api/(?P<version>[1])/ivrinfo', IvrInfoDiscovery.as_view(), name='api.ivr_info'),
	url(r'^api/(?P<version>[1])/company_display_numbers', CompanyDisplayNumbers.as_view(), name='api.company_display_numbers'),
	url(r'^api/(?P<version>[1])/company_ivrs_list', CompanyIvrsList.as_view(), name='api.company_ivrs_list'),
	url(r'^api/(?P<version>[1])/notif-companyusage-check', NotifCompanyUsageCheck.as_view(), name='api.comapny_usage_check'),
	url(r'^api/(?P<version>[1])/companyusage-check', CompanyUsageCheck.as_view(), name='api.comapny_usage_check'),
	url(r'^api/(?P<version>[1])/server-groups', ServerGroups.as_view(), name='api.group_server'),
	url(r'^api/(?P<version>[1])/notif-group-ivr', NotifGroupIVR.as_view(), name='api.group_ivr_invoker'),
	url(r'^api/(?P<version>[1])/group-ivr', GroupIVR.as_view(), name='api.group_ivr'),
	url(r'^api/(?P<version>[1])/get-user-token', views.obtain_auth_token, name='api.get_token'),
	url(r'^api/(?P<version>[1])/ivr-actions', IvrActions.as_view(), name='api.ivr_actions'),
	url(r'^api/(?P<version>[1])/ivr-requests-count', IvrRequestsCount.as_view(), name='api.ivr_actions'),
	url(r'^api/(?P<version>[1])/disabled-users', DisabledUsers.as_view(), name='api.disabled_users'),
	url(r'^api/(?P<version>[1])/junction-requests-count', JunctionRequestsCount.as_view(), name='api.junction_req_count'),
	url(r'^api/(?P<version>[1])/job-abort-event',JobEventAPIView.as_view(), name='api.job_event')
	# url(r'^swagger/(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),
	# url(r'^swagger/$', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
	# url(r'^redoc/$', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc')
]
