import os

from django.conf import settings

from celery import Celery
from celery.signals import before_task_publish, task_prerun

from central.logging import cfg, reset_uid

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "central.settings.local")

app = Celery("central")
app.config_from_object("django.conf:settings", namespace="CELERY")
app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)


@before_task_publish.connect
def add_trace_id(sender=None, headers=None, **kwargs):
    trace_id = cfg.uid
    if trace_id:
        headers["trace_id"] = trace_id


@task_prerun.connect
def restore_trace_id(task, **kwargs):
    headers = getattr(task.request, "headers", {})
    if headers and headers.get("trace_id"):
        reset_uid(headers["trace_id"])
