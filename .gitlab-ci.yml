# This file is a template, and might need editing before it works on your project.
# Official framework image. Look for the different tagged releases at:
# https://hub.docker.com/r/library/python
image: python:3.6

stages:
  - lint
  - test
  - deploy

# Pick zero or more services to be used on all builds.
# Only needed when using a docker container to run your tests in.
# Check out: http://docs.gitlab.com/ce/ci/docker/using_docker_images.html#what-is-a-service

variables:
  POSTGRES_DB: database_name

# This folder is cached between builds
# http://docs.gitlab.com/ce/ci/yaml/README.html#cache
cache:
  paths:
   - .pip

# This is a basic example for a gem or script which doesn't use
# services such as redis or postgres
before_script:
  - mkdir -p .pip
  - pip install -U pip
  - pip --cache-dir=.pip install -r requirements.txt
  # - find . -name '*.pyc' -delete

# To get Django tests to work you may need to create a settings file using
# the following DATABASES:
# 
# DATABASES = {
#     'default': {
#        'ENGINE': 'django.db.backends.postgresql_psycopg2',
#        'NAME': 'ci',
#        'USER': 'postgres',
#        'PASSWORD': 'postgres',
#        'HOST': 'postgres',
#        'PORT': '5432',
#    },
# }
#
# and then adding `--settings app.settings.ci` (or similar) to the test command

lint:
  stage: lint
  script:
  - prospector

test:
  stage: test
  script:
  - echo "Test Ok"
  # - pip install -r requirements-testing.txt
  # - python manage.py makemigrations --merge --noinput
  # - python manage.py migrate
  # - python manage.py collectstatic --noinput
  # - python manage.py test --keepdb
  only:
  - master


deploy_staging:
  stage: deploy
  script:
    - echo "Deploy to staging server"
    - python deploy-config/fab-file.py
  environment:
    name: staging
    url: https://staging..com/
  only:
  - gaurav_dev

deploy_prod:
  stage: deploy
  script:
    - echo "Deploy to production server"
    # - fab prod deploy
  environment:
    name: production
    url: https://www..com/
  when: manual
  only:
  - master
