
pipeline{
    agent{
        label "slave"
    }

    options {
        timestamps()
        parallelsAlwaysFailFast()
        disableConcurrentBuilds()
        gitLabConnection('gitlab')
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
    }
    stages{
        stage("Build"){
            steps{
                slackSend channel: "jenkins-stage", message: """
            Build started `${currentBuild.fullDisplayName}`. (<${env.BUILD_URL}|Open>) (<${env.CHANGE_URL ?: env.GIT_URL}|Repo>)
            """, color: "warning"
            }
        }


        stage("Test") {
            parallel{
                stage('test 3.10') {
                    agent {
                        docker {
                        label 'slave'
                        image 'python:3.10-buster'
                        args '-u 0'
                        }
                    }
                    steps {
                        updateGitlabCommitStatus name: 'test - 3.10', state: 'pending'
                        sh 'pip install -U wheel'
                        sh 'pip install -r requirements/local.txt'
                        sh 'pytest'

                        junit allowEmptyResults: true, testResults: 'test_reports/junit*.xml'
                        cobertura coberturaReportFile: 'coverage.xml', maxNumberOfBuilds: 10, enableNewApi: true
                    }
                    post {
                        success {
                            updateGitlabCommitStatus name: 'test - 3.10', state: 'success'
                            slackSend channel: "jenkins-stage", message: "Test successfully ran for - python3.10"
                        }
                        unsuccessful {
                            updateGitlabCommitStatus name: 'test - 3.10', state: 'failed'
                            slackSend channel: "jenkins-stage", message: "Test unsuccessful for - python3.10"
                        }
                        cleanup{
                            sh "rm -rf .pytest_cache test_reports/ coverage.xml htmlcov"
                        }
                    }
                }
                stage('test 3.10 - channel_cleanup_lambda') {
                    agent {
                        docker {
                        label 'slave'
                        image 'python:3.10-buster'
                        args '-u 0'
                        }
                    }
                    steps {
                        dir("aws_lambdas/channel_cache_cleanup"){
                            updateGitlabCommitStatus name: 'test - 3.10 - channel_cleanup_lambda', state: 'pending'
                            sh 'pip install -U wheel'
                            sh 'pip install -r test.txt'
                            sh 'pytest'
                        }
                    }
                    post {
                        success {
                            updateGitlabCommitStatus name: 'test 3.10 - channel_cleanup_lambda', state: 'success'
                            slackSend channel: "jenkins-stage", message: "channel_cache_cleanup lambda test successfully ran for - python3.10"
                        }
                        unsuccessful {
                            updateGitlabCommitStatus name: 'test 3.10 - channel_cleanup_lambda', state: 'failed'
                            slackSend channel: "jenkins-stage", message: "channel_cache_cleanup lambda test unsuccessful for - python3.10"
                        }
                        cleanup{
                            sh "rm -rf .pytest_cache test_reports/ coverage.xml htmlcov"
                        }
                    }
                }
                stage('test 3.10 - obd_central_notifier') {
                    agent {
                        docker {
                        label 'slave'
                        image 'python:3.10-buster'
                        args '-u 0'
                        }
                    }
                    steps {
                        dir("aws_lambdas/obd_central_notifier"){
                            updateGitlabCommitStatus name: 'test - 3.10 - obd_central_notifier', state: 'pending'
                            sh 'pip install -U wheel'
                            sh 'pip install -r test.txt'
                            sh 'pytest'
                        }
                    }
                    post {
                        success {
                            updateGitlabCommitStatus name: 'test 3.10 - obd_central_notifier', state: 'success'
                            slackSend channel: "jenkins-stage", message: "obd_central_notifier lambda test successfully ran for - python3.10"
                        }
                        unsuccessful {
                            updateGitlabCommitStatus name: 'test 3.10 - obd_central_notifier', state: 'failed'
                            slackSend channel: "jenkins-stage", message: "obd_central_notifier lambda test unsuccessful for - python3.10"
                        }
                        cleanup{
                            sh "rm -rf .pytest_cache test_reports/ coverage.xml htmlcov"
                        }
                    }
                }
                stage('test 3.11') {
                    agent {
                        docker {
                        label 'slave'
                        image 'python:3.11-buster'
                        args '-u 0'
                        }
                    }
                    steps {
                        updateGitlabCommitStatus name: 'test - 3.11', state: 'pending'
                        sh 'pip install -U wheel'
                        sh 'pip install -r requirements/local.txt'
                        sh 'pytest'
                        junit allowEmptyResults: true, testResults: 'test_reports/junit*.xml'
                        cobertura coberturaReportFile: 'coverage.xml', maxNumberOfBuilds: 10, enableNewApi: true
                    }
                    post {
                        success {
                            updateGitlabCommitStatus name: 'test - 3.11', state: 'success'
                            slackSend channel: "jenkins-stage", message: "Test successfully ran  for - python3.11"
                        }
                        unsuccessful {
                            updateGitlabCommitStatus name: 'test - 3.11', state: 'failed'
                            slackSend channel: "jenkins-stage", message: "Test unsuccessful for - python3.11"
                        }
                        cleanup{
                            sh "rm -rf .pytest_cache test_reports/ coverage.xml htmlcov"
                        }
                    }
                }
                
            }
        }

        stage("Approval"){
            when { 
                anyOf {
                branch pattern: "release/[\\w.]+", comparator: "REGEXP"
                branch  "master"
                buildingTag()
                changeRequest branch: 'release/[\\w.]+', comparator: "REGEXP"

            }
            }
            options {
                timeout(time: 1, unit: 'DAYS')
            }
            steps {
                updateGitlabCommitStatus name: 'approve_deploy', state: 'pending'
                script {
                    user_inp = input id: 'deployer', message: 'Select deploy env', parameters: [string(defaultValue: 'current', description: 'Enter APP version number (eg. v1, v2, v3) \n Note: when envionment select is "blue", the version is always "beta"', name: 'app_version', trim: true), choice(choices: ["staging", "blue", "green", "None"], name: 'deploy_env')]
                    env.DEPLOY_TO = user_inp.deploy_env
                    env.APP_VERSION = user_inp.app_version

                    }
                }
            post {
                success {
                    updateGitlabCommitStatus name: 'approve_deploy', state: 'success'
                }
                failure {
                    updateGitlabCommitStatus name: 'approve_deploy', state: 'failed'
                }
                aborted {
                    updateGitlabCommitStatus name: 'approve_deploy', state: 'success'
                }
            }

        }
        stage('Deploy (stage)') {
            when { 
                environment name: 'DEPLOY_TO', value: 'staging'
            }
            options {
                withAWS(credentials: 'AWS-Credentials-Stage', region: 'ap-south-1')
            }
            environment { 
                GIT_CREDS = credentials('gitlab-http') 
                WORKSPACE = "stage"
            }
            steps {
                updateGitlabCommitStatus name: 'staging_deploy', state: 'pending'
                dir("deploy/ansible") {
                    sh "./update-env.sh fetch"
                    sh "GITLAB_USER=$GIT_CREDS_USR GITLAB_TOKEN=$GIT_CREDS_PSW ansible-playbook -i inventory/staging.aws_ec2.yml central_staging.yml -e git_branch=$GIT_BRANCH -e app_version=${env.APP_VERSION} -e git_repo=${env.GIT_URL}"
                }

                dir("deploy/terraform") {
                  s3Download(file: './terraform.tfvars', bucket: 'stage-ecs-env-myop', path: 'central/terraform.tfvars')
                  withAWS(credentials: 'AWS-Credentials-Stage', region: 'ap-south-1') {
                    sh "terraform init"
                    sh "./workspace.sh"
                  }
                  script {
                    def terraformPlanExitCode = sh(
                        script: "terraform plan -detailed-exitcode",
                        returnStatus: true
                    )
        
                    if (terraformPlanExitCode == 2) {
                      input message: "Terraform plan reviewed?", ok: "Yes"
                      sh "terraform apply -auto-approve"
                    } else if (terraformPlanExitCode == 0) {
                      echo "No changes in Terraform plan. Skipping apply step."
                    } else {
                      error "Terraform plan failed or encountered an error. Skipping apply step."
                    }
                  }
               }
            }

            post {
                success {
                    updateGitlabCommitStatus name: 'staging_deploy', state: 'success'
                    slackSend channel: "jenkins-stage", message: "STAGING deployment complete: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'good'
                }
                failure {
                    updateGitlabCommitStatus name: 'staging_deploy', state: 'failed'
                    slackSend channel: "jenkins-stage", message: "STAGING deployment failed: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'danger'
                }
                aborted {
                    updateGitlabCommitStatus name: 'staging_deploy', state: 'success'
                    slackSend channel: "jenkins-stage", message: "STAGING deployment cancelled: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'good'
                }
            }
        }

        stage('Deploy (green)') {
            when { 
                environment name: 'DEPLOY_TO', value: 'green'
            }
            options {
                withAWS(credentials: 'AWS-Credentials', region: 'ap-south-1')
            }
            environment { 
                GIT_CREDS = credentials('gitlab-http')
                WORKSPACE = "prod"
            }
            steps {
                updateGitlabCommitStatus name: 'green_deploy', state: 'pending'
                dir("deploy/ansible") {
                    sh "./update-env.sh -i central/${env.APP_VERSION} fetch"
                    sh "GITLAB_USER=$GIT_CREDS_USR GITLAB_TOKEN=$GIT_CREDS_PSW ansible-playbook -i inventory/prod.aws_ec2.yml central_prod.yml -e git_branch=$GIT_BRANCH -e app_version=${env.APP_VERSION} -e git_repo=${env.GIT_URL}"
                }
                dir("deploy/terraform") {
                s3Download(file: './terraform.tfvars', bucket: 'prod-ecs-env-myop', path: 'central/terraform.tfvars')
                withAWS(credentials: 'AWS-Credentials-Stage', region: 'ap-south-1') {
                    sh "terraform init"
                    sh "./workspace.sh"
                }
                script {
                    def terraformPlanExitCode = sh(
                        script: "terraform plan -detailed-exitcode",
                        returnStatus: true
                    )

                    if (terraformPlanExitCode == 2) {
                    input message: "Terraform plan reviewed?", ok: "Yes"
                    sh "terraform apply -auto-approve"
                    } else if (terraformPlanExitCode == 0) {
                    echo "No changes in Terraform plan. Skipping apply step."
                    } else {
                    error "Terraform plan failed or encountered an error. Skipping apply step."
                    }
                }
            }
    }

            post {
                success {
                    updateGitlabCommitStatus name: 'green_deploy', state: 'success'
                    slackSend channel: "jenkins-stage", message: "GREEN deployment complete: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'good'
                }
                failure {
                    updateGitlabCommitStatus name: 'green_deploy', state: 'failed'
                    slackSend channel: "jenkins-stage", message: "GREEN deployment failed: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'danger'
                }
                aborted {
                    updateGitlabCommitStatus name: 'green_deploy', state: 'success'
                    slackSend channel: "jenkins-stage", message: "GREEN deployment cancelled: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'good'
                }
            }
        }
    }
    post {
        aborted {
            slackSend channel: "jenkins-stage", message: "Job Cancelled: `${currentBuild.fullDisplayName}` | <${env.BUILD_URL}|Open>", color: '#e8e6e3'
        }

        failure {
            slackSend channel: "jenkins-stage", message: "Job Failed: `${currentBuild.fullDisplayName}` | <${env.BUILD_URL}|Open>", color: 'danger'
        }

        success {
            slackSend channel: "jenkins-stage", message: """
            Build succeeded `${currentBuild.fullDisplayName}`. (<${env.BUILD_URL}|Open>) (<${env.CHANGE_URL ?: env.GIT_URL}|Repo>)
            """, color: "good"
        }
        always {
            cleanWs()
        }
    }
}

