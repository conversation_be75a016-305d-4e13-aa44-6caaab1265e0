appnope==0.1.3
asgiref==3.6.0
asttokens==2.2.1
async-timeout==4.0.2
backcall==0.2.0
bcrypt==4.0.1
boto3==1.26.109
botocore==1.29.109
celery==5.3.*
certifi==2022.12.7
cffi==1.15.1
charset-normalizer==3.1.0
click==8.1.3
click-didyoumean==0.3.0
click-plugins==1.1.1
click-repl==0.2.0
cryptography==40.0.1
decorator==5.1.1
Django==3.2.18
django-cacheops==6.2
django-cors-headers==3.14.0
django-debug-toolbar==4.0.0
django-extensions==3.2.1
django-redis==5.2.0
django-storages==1.13.2
djangorestframework==3.14.0
drf-yasg==1.21.5
executing==1.2.0
fabric==3.0.0
fakeredis[lua]==2.12.0
funcy==1.18
gunicorn==19.7.1
idna==3.4
inflection==0.5.1
invoke==2.0.0
ipython==8.12.0
itypes==1.2.0
jedi==0.18.2
Jinja2==3.1.2
jmespath==1.0.1
jsonfield==3.1.0
MarkupSafe==2.1.2
matplotlib-inline==0.1.6
mock==5.0.1
msgpack==0.6.2
packaging==23.0
paramiko==3.1.0
parso==0.8.3
pexpect==4.8.0
pickleshare==0.7.5
prompt-toolkit==3.0.38
ptyprocess==0.7.0
pure-eval==0.2.2
pycparser==2.21
Pygments==2.14.0
PyMySQL==1.0.3
PyNaCl==1.5.0
python-dateutil==2.8.2
python-dotenv==1.0.0
python-json-logger==2.0.7
pytz==2023.3
redis==4.5.4
requests==2.*
retrying==1.3.4
ruamel.yaml==0.17.21
ruamel.yaml.clib==0.2.7
s3transfer==0.6.0
six==1.16.0
sortedcontainers==2.4.0
sqlparse==0.4.3
stack-data==0.6.2
termcolor==2.2.0
traitlets==5.9.0
uritemplate==4.1.1
urllib3==1.26.15
wcwidth==0.2.6
Werkzeug==2.2.3
