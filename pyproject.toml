[tool.black]
line-length = 79
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 79
multi_line_output = 3
include_trailing_comma = true
known_first_party = 'handlers'
known_django = ['django', 'rest_framework']
known_myoperator = 'myoperator'
sections = [
  'FUTURE',
  'STDLIB',
  'DJANGO',
  'THIRDPARTY',
  'MYOPERATOR',
  'FIRSTPARTY',
  'LOCALFOLDER',
]
