[pytest]
addopts = --cov --cov-report=xml --cov-report=html --junitxml=test_reports/junit.xml
        --ignore=aws_lambdas
python_files = tests.py test_*.py *_tests.py

markers =
    unittest: unit tests
    commands: mark tests for management commands
    central_1: mark tests  for central_1
    central_2: mark test for central_2
    central_delay_q: mark test for central_delay_q
    data_deletion_handler: mark test for data_deletion_handler
    expiry_manager: mark test for expiry_manager
    group_ivr_info_relation_handler: mark test for group_ivr_info_relation_handler
    group_server_consistancy: mark test for group_server_consistancy
    on_hold_circulator: mark test for on_hold_circulator
    on_hold_processor: mark test for on_hold_processor
    response_manager: mark test for response_manager
    reverse_expiry_request_mark_from_ivr_processor: mark test for reverse_expiry_request_mark_from_ivr_processor
    on_hold: mark test for on_hold
    run_group_server_consistancy_handler: mark test for run_group_server_consistancy_handler
    group_server_consistancy_main: mark test for group_server_consistancy_main
    utills: mark test for utills
    group_server_relation_manager: mark test for group_server_relation_manager
    logger_handler: mark test for logger_handler
    routing_manager: mark test for routing_manager


env=
    DJANGO_SETTINGS_MODULE = central.settings.testing
    KAM_GROUP_SERVERS_LIST_ROUTE_NAME = "list"
    KAM_GROUP_SERVERS_ROUTE_NAME = "server"
    GLOBAL_CANCELLATION_ROUTE_NAME= "cancel"
    PROCESS_FLOW_ROUTER_NAME="process"
    UDC_API_BASE_URL=http://test/

    AWS_ACCESS_KEY_ID=21
    AWS_SECRET_ACCESS_KEY=!@312
    AWS_REGION_NAME=ap-south-1
    OBD_INTERNAL_API_URL=http://test123/
    TRUE_CALLER_API_URL=http://test123/
    TRUE_CALLER_KEY_ID=test_key_id
    TRUE_CALLER_CLIENT_ID=test_client_id
    TRUE_CALLER_API_KEY=test_api_key
    TRUE_CALLER_ENABLED=true