# Changelog
All notable changes to this project will be documented in this file.

---
## [1.4] - 13-10-2020
### Added
#### Data_Deletion
-  added new management command for deleting data from given models.


## [1.3] - 11-10-2020
### Changed
#### Central_1
-  changed recursion logic added checks for `single_ivr_threshold`.

#### ChangedLog
-  ChangeLog file added

### Added
#### Models
-  added `ivr_type` in `IvrInfo` model.

#### Central_1
-  added `central_1_loop_count` to increase number of executions for single execution.
-  added `single_ivr_threshold` for limiting the number of requests of single IVR for an execution.
-  added `is_onhold_req=CentralRequestInfo.ENTER` check for type 2 requests.

## [1.2] - 06-10-2020
### Changed
#### OnHoldProcessor
-  logic to disable a req if on_hold_req count of a user is greater than 1.

#### External API
-  changed user_lock ttl - 15s for agent_first and 30s for customer_first

### Added
#### Models
-  added `OnHoldUserTracing` for storing on_hold_req count of a user.

#### Models Signal
-  added post hook single to manage UserTracing, and to enable a req if a req of a user gets completed.


## [1.1] - 05-10-2020
### Changed
#### Central_2
-  logic to send requests to group_queues moved to a seperate file.

### Added
#### OnHoldCirculator
-  data directly send to group_queue instead of sending it to pq1 when UDC is available.