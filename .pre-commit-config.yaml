exclude: "docs|migrations|.git|.tox|venv"
default_stages: [commit]
fail_fast: true

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        files: (^|/)a/.+\.(py|html|sh|css|js)$

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: isort (python)
        args: [--settings-path, pyproject.toml]

  - repo: local
    hooks:
      - id: black
        name: black
        entry: black
        language: python
        types: [python]
        args: [--config, pyproject.toml]

      - id: flake8
        name: flake8
        entry: flake8
        language: python
        types: [python]

      - id: pytest-check
        name: pytest-check
        entry: venv/bin/pytest
        types: [python]
        language: python
        pass_filenames: false
        always_run: true
        verbose: true
