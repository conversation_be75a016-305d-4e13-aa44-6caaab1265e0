[group:{{ app_version_name }}]
programs={{ app_version_name }}_gunicorn,{{ app_version_name }}_on_hold_processor_handler,{{ app_version_name }}_2_handler

[program:{{ app_version_name }}_gunicorn] 
directory =  {{ project_base }}/{{ app_version }}
command = bash {{ project_base }}/{{ app_version }}/gunicorn.bash
autostart=true
user = ubuntu   
stopsignal=KILL   
killasgroup=true  
stdout_logfile = {{ log_dir }}/supervisor/out_central_gunicorn_supervisor.log
stderr_logfile = {{ log_dir }}/supervisor/err_central_gunicorn_supervisor.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8

[program:{{ app_version_name }}_on_hold_processor_handler]
directory = {{ project_base }}/{{ app_version }}
command = bash {{ project_base }}/{{ app_version }}/deploy/management_command.sh on_hold_processor_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = {{ log_dir }}/supervisor/out_on_hold_processor_handler.log
stderr_logfile = {{ log_dir }}/supervisor/err_on_hold_processor_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8

[program:{{ app_version_name }}_2_handler]
directory = {{ project_base }}/{{ app_version }}
command = bash {{ project_base }}/{{ app_version }}/deploy/management_command.sh central_2_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = {{ log_dir }}/supervisor/out_central_2_handler.log
stderr_logfile = {{ log_dir }}/supervisor/err_central_2_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8