[program:central] 
directory = /home/<USER>/apps/central
command = bash /home/<USER>/apps/central/gunicorn.bash  
autostart=true
user = ubuntu   
stopsignal=KILL   
killasgroup=true  
stdout_logfile = /var/log/apps/central/supervisor/out_central_gunicorn_supervisor.log
stderr_logfile = /var/log/apps/central/supervisor/err_central_gunicorn_supervisor.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8,REDIS_HOST='*********',REDIS_PORT='6380',AWS_SECRET_ACCESS_KEY='CWb995iYGD/ur3NmHF1yeCxWWF0vTpoAnFz+Mz/t',AWS_ACCESS_KEY_ID='********************',celery_default_queue='obd_central_queue',shared_redis_host='*********',shared_redis_port='6381',source_name='call',cancel_service='cancel_service',response_manager='response_manager',expiry_manager='expiry_manager',udc_api_base_url='https://microapi.myoperator.dev/obd/',fixdid_api_base_url='https://microapi.myoperator.dev/fix-did/',obd_queue_tag_key='obd',obd_queue_tag_value='obd',shared_redis_version='cs',server_route_queue_name='obd_service_route_queue',ivr_rule_route_name='ivr_rule',process_flow_router_name='process_flow_get',kam_group_servers_route_name='kam_group_servers',kam_group_servers_list_route_name='kam_group_server_list',global_cancellation_route_name='global_cancellation',kam_group_servers_token='16c32ccf89a8f9639eecc42c2365a6b516dc8e8a',routing_info_url='http://*********/api/1/routinginfo',cancellation_enabled=0,cancelled_request_status=2,pm_auth_token="e62ceb56c28bbdc71b57fa113d59592b2c0cb7f8"


[program:central_1_handler]
directory = /home/<USER>/apps/central
command = bash /home/<USER>/apps/central/deploy/management_command.sh central_1_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = /var/log/apps/central/supervisor/out_central_1_handler.log
stderr_logfile = /var/log/apps/central/supervisor/err_central_1_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8,REDIS_HOST='*********',REDIS_PORT='6380',AWS_SECRET_ACCESS_KEY='CWb995iYGD/ur3NmHF1yeCxWWF0vTpoAnFz+Mz/t',AWS_ACCESS_KEY_ID='********************',celery_default_queue='obd_central_queue',shared_redis_host='*********',shared_redis_port='6381',source_name='call',cancel_service='cancel_service',response_manager='response_manager',expiry_manager='expiry_manager',udc_api_base_url='https://microapi.myoperator.dev/obd/',fixdid_api_base_url='https://microapi.myoperator.dev/fix-did/',obd_queue_tag_key='obd',obd_queue_tag_value='obd',shared_redis_version='cs',server_route_queue_name='obd_service_route_queue',ivr_rule_route_name='ivr_rule',process_flow_router_name='process_flow_get',kam_group_servers_route_name='kam_group_servers',kam_group_servers_list_route_name='kam_group_server_list',global_cancellation_route_name='global_cancellation',kam_group_servers_token='16c32ccf89a8f9639eecc42c2365a6b516dc8e8a',routing_info_url='http://*********/api/1/routinginfo',cancellation_enabled=0,cancelled_request_status=2,pm_auth_token="e62ceb56c28bbdc71b57fa113d59592b2c0cb7f8"


[program:central_2_handler]
directory = /home/<USER>/apps/central
command = bash /home/<USER>/apps/central/deploy/management_command.sh central_2_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = /var/log/apps/central/supervisor/out_central_2_handler.log
stderr_logfile = /var/log/apps/central/supervisor/err_central_2_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8,REDIS_HOST='*********',REDIS_PORT='6380',AWS_SECRET_ACCESS_KEY='CWb995iYGD/ur3NmHF1yeCxWWF0vTpoAnFz+Mz/t',AWS_ACCESS_KEY_ID='********************',celery_default_queue='obd_central_queue',shared_redis_host='*********',shared_redis_port='6381',source_name='call',cancel_service='cancel_service',response_manager='response_manager',expiry_manager='expiry_manager',udc_api_base_url='https://microapi.myoperator.dev/obd/',fixdid_api_base_url='https://microapi.myoperator.dev/fix-did/',obd_queue_tag_key='obd',obd_queue_tag_value='obd',shared_redis_version='cs',server_route_queue_name='obd_service_route_queue',ivr_rule_route_name='ivr_rule',process_flow_router_name='process_flow_get',kam_group_servers_route_name='kam_group_servers',kam_group_servers_list_route_name='kam_group_server_list',global_cancellation_route_name='global_cancellation',kam_group_servers_token='16c32ccf89a8f9639eecc42c2365a6b516dc8e8a',routing_info_url='http://*********/api/1/routinginfo',cancellation_enabled=0,cancelled_request_status=2,pm_auth_token="e62ceb56c28bbdc71b57fa113d59592b2c0cb7f8"


[program:on_hold_processor_handler]
directory = /home/<USER>/apps/central
command = bash /home/<USER>/apps/central/deploy/management_command.sh on_hold_processor_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = /var/log/apps/central/supervisor/out_on_hold_processor_handler.log
stderr_logfile = /var/log/apps/central/supervisor/err_on_hold_processor_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8,REDIS_HOST='*********',REDIS_PORT='6380',AWS_SECRET_ACCESS_KEY='CWb995iYGD/ur3NmHF1yeCxWWF0vTpoAnFz+Mz/t',AWS_ACCESS_KEY_ID='********************',celery_default_queue='obd_central_queue',shared_redis_host='*********',shared_redis_port='6381',source_name='call',cancel_service='cancel_service',response_manager='response_manager',expiry_manager='expiry_manager',udc_api_base_url='https://microapi.myoperator.dev/obd/',fixdid_api_base_url='https://microapi.myoperator.dev/fix-did/',obd_queue_tag_key='obd',obd_queue_tag_value='obd',shared_redis_version='cs',server_route_queue_name='obd_service_route_queue',ivr_rule_route_name='ivr_rule',process_flow_router_name='process_flow_get',kam_group_servers_route_name='kam_group_servers',kam_group_servers_list_route_name='kam_group_server_list',global_cancellation_route_name='global_cancellation',kam_group_servers_token='16c32ccf89a8f9639eecc42c2365a6b516dc8e8a',routing_info_url='http://*********/api/1/routinginfo',cancellation_enabled=0,cancelled_request_status=2,pm_auth_token="e62ceb56c28bbdc71b57fa113d59592b2c0cb7f8"

[program:on_hold_circulation_handler]
directory = /home/<USER>/apps/central
command = bash /home/<USER>/apps/central/deploy/management_command.sh on_hold_circulation_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = /var/log/apps/central/supervisor/out_on_hold_circulation_handler.log
stderr_logfile = /var/log/apps/central/supervisor/err_on_hold_circulation_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8,REDIS_HOST='*********',REDIS_PORT='6380',AWS_SECRET_ACCESS_KEY='CWb995iYGD/ur3NmHF1yeCxWWF0vTpoAnFz+Mz/t',AWS_ACCESS_KEY_ID='********************',celery_default_queue='obd_central_queue',shared_redis_host='*********',shared_redis_port='6381',source_name='call',cancel_service='cancel_service',response_manager='response_manager',expiry_manager='expiry_manager',udc_api_base_url='https://microapi.myoperator.dev/obd/',fixdid_api_base_url='https://microapi.myoperator.dev/fix-did/',obd_queue_tag_key='obd',obd_queue_tag_value='obd',shared_redis_version='cs',server_route_queue_name='obd_service_route_queue',ivr_rule_route_name='ivr_rule',process_flow_router_name='process_flow_get',kam_group_servers_route_name='kam_group_servers',kam_group_servers_list_route_name='kam_group_server_list',global_cancellation_route_name='global_cancellation',kam_group_servers_token='16c32ccf89a8f9639eecc42c2365a6b516dc8e8a',routing_info_url='http://*********/api/1/routinginfo',cancellation_enabled=0,cancelled_request_status=2,pm_auth_token="e62ceb56c28bbdc71b57fa113d59592b2c0cb7f8"


[program:response_manager_handler]
directory = /home/<USER>/apps/central
command = bash /home/<USER>/apps/central/deploy/management_command.sh response_manager_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = /var/log/apps/central/supervisor/out_response_manager_handler.log
stderr_logfile = /var/log/apps/central/supervisor/err_response_manager_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8,REDIS_HOST='*********',REDIS_PORT='6380',AWS_SECRET_ACCESS_KEY='CWb995iYGD/ur3NmHF1yeCxWWF0vTpoAnFz+Mz/t',AWS_ACCESS_KEY_ID='********************',celery_default_queue='obd_central_queue',shared_redis_host='*********',shared_redis_port='6381',source_name='call',cancel_service='cancel_service',response_manager='response_manager',expiry_manager='expiry_manager',udc_api_base_url='https://microapi.myoperator.dev/obd/',fixdid_api_base_url='https://microapi.myoperator.dev/fix-did/',obd_queue_tag_key='obd',obd_queue_tag_value='obd',shared_redis_version='cs',server_route_queue_name='obd_service_route_queue',ivr_rule_route_name='ivr_rule',process_flow_router_name='process_flow_get',kam_group_servers_route_name='kam_group_servers',kam_group_servers_list_route_name='kam_group_server_list',global_cancellation_route_name='global_cancellation',kam_group_servers_token='16c32ccf89a8f9639eecc42c2365a6b516dc8e8a',routing_info_url='http://*********/api/1/routinginfo',cancellation_enabled=0,cancelled_request_status=2,pm_auth_token="e62ceb56c28bbdc71b57fa113d59592b2c0cb7f8"


[program:expiry_manager_handler]
directory = /home/<USER>/apps/central
command = bash /home/<USER>/apps/central/deploy/management_command.sh expiry_manager_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = /var/log/apps/central/supervisor/out_expiry_manager_handler.log
stderr_logfile = /var/log/apps/central/supervisor/err_expiry_manager_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8,REDIS_HOST='*********',REDIS_PORT='6380',AWS_SECRET_ACCESS_KEY='CWb995iYGD/ur3NmHF1yeCxWWF0vTpoAnFz+Mz/t',AWS_ACCESS_KEY_ID='********************',celery_default_queue='obd_central_queue',shared_redis_host='*********',shared_redis_port='6381',source_name='call',cancel_service='cancel_service',response_manager='response_manager',expiry_manager='expiry_manager',udc_api_base_url='https://microapi.myoperator.dev/obd/',fixdid_api_base_url='https://microapi.myoperator.dev/fix-did/',obd_queue_tag_key='obd',obd_queue_tag_value='obd',shared_redis_version='cs',server_route_queue_name='obd_service_route_queue',ivr_rule_route_name='ivr_rule',process_flow_router_name='process_flow_get',kam_group_servers_route_name='kam_group_servers',kam_group_servers_list_route_name='kam_group_server_list',global_cancellation_route_name='global_cancellation',kam_group_servers_token='16c32ccf89a8f9639eecc42c2365a6b516dc8e8a',routing_info_url='http://*********/api/1/routinginfo',cancellation_enabled=0,cancelled_request_status=2,pm_auth_token="e62ceb56c28bbdc71b57fa113d59592b2c0cb7f8"


[program:central_delay_queue_handler]
directory = /home/<USER>/apps/central
command = bash /home/<USER>/apps/central/deploy/management_command.sh central_delay_queue_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = /var/log/apps/central/supervisor/out_central_delay_queue_handler.log
stderr_logfile = /var/log/apps/central/supervisor/err_central_delay_queue_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8,REDIS_HOST='*********',REDIS_PORT='6380',AWS_SECRET_ACCESS_KEY='CWb995iYGD/ur3NmHF1yeCxWWF0vTpoAnFz+Mz/t',AWS_ACCESS_KEY_ID='********************',celery_default_queue='obd_central_queue',shared_redis_host='*********',shared_redis_port='6381',source_name='call',cancel_service='cancel_service',response_manager='response_manager',expiry_manager='expiry_manager',udc_api_base_url='https://microapi.myoperator.dev/obd/',fixdid_api_base_url='https://microapi.myoperator.dev/fix-did/',obd_queue_tag_key='obd',obd_queue_tag_value='obd',shared_redis_version='cs',server_route_queue_name='obd_service_route_queue',ivr_rule_route_name='ivr_rule',process_flow_router_name='process_flow_get',kam_group_servers_route_name='kam_group_servers',kam_group_servers_list_route_name='kam_group_server_list',global_cancellation_route_name='global_cancellation',kam_group_servers_token='16c32ccf89a8f9639eecc42c2365a6b516dc8e8a',routing_info_url='http://*********/api/1/routinginfo',cancellation_enabled=0,cancelled_request_status=2,pm_auth_token="e62ceb56c28bbdc71b57fa113d59592b2c0cb7f8"


[program:company_usage_check_handler]
directory = /home/<USER>/apps/central
command = bash /home/<USER>/apps/central/deploy/management_command.sh company_usage_check_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = /var/log/apps/central/supervisor/out_company_usage_check_handler.log
stderr_logfile = /var/log/apps/central/supervisor/err_company_usage_check_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8,REDIS_HOST='*********',REDIS_PORT='6380',AWS_SECRET_ACCESS_KEY='CWb995iYGD/ur3NmHF1yeCxWWF0vTpoAnFz+Mz/t',AWS_ACCESS_KEY_ID='********************',celery_default_queue='obd_central_queue',shared_redis_host='*********',shared_redis_port='6381',source_name='call',cancel_service='cancel_service',response_manager='response_manager',expiry_manager='expiry_manager',udc_api_base_url='https://microapi.myoperator.dev/obd/',fixdid_api_base_url='https://microapi.myoperator.dev/fix-did/',obd_queue_tag_key='obd',obd_queue_tag_value='obd',shared_redis_version='cs',server_route_queue_name='obd_service_route_queue',ivr_rule_route_name='ivr_rule',process_flow_router_name='process_flow_get',kam_group_servers_route_name='kam_group_servers',kam_group_servers_list_route_name='kam_group_server_list',global_cancellation_route_name='global_cancellation',kam_group_servers_token='16c32ccf89a8f9639eecc42c2365a6b516dc8e8a',routing_info_url='http://*********/api/1/routinginfo',cancellation_enabled=0,cancelled_request_status=2,pm_auth_token="e62ceb56c28bbdc71b57fa113d59592b2c0cb7f8"


[program:group_server_consistancy_handler]
directory = /home/<USER>/apps/central
command = bash /home/<USER>/apps/central/deploy/management_command.sh group_server_consistancy_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = /var/log/apps/central/supervisor/out_group_server_consistancy_handler.log
stderr_logfile = /var/log/apps/central/supervisor/err_group_server_consistancy_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8,REDIS_HOST='*********',REDIS_PORT='6380',AWS_SECRET_ACCESS_KEY='CWb995iYGD/ur3NmHF1yeCxWWF0vTpoAnFz+Mz/t',AWS_ACCESS_KEY_ID='********************',celery_default_queue='obd_central_queue',shared_redis_host='*********',shared_redis_port='6381',source_name='call',cancel_service='cancel_service',response_manager='response_manager',expiry_manager='expiry_manager',udc_api_base_url='https://microapi.myoperator.dev/obd/',fixdid_api_base_url='https://microapi.myoperator.dev/fix-did/',obd_queue_tag_key='obd',obd_queue_tag_value='obd',shared_redis_version='cs',server_route_queue_name='obd_service_route_queue',ivr_rule_route_name='ivr_rule',process_flow_router_name='process_flow_get',kam_group_servers_route_name='kam_group_servers',kam_group_servers_list_route_name='kam_group_server_list',global_cancellation_route_name='global_cancellation',kam_group_servers_token='16c32ccf89a8f9639eecc42c2365a6b516dc8e8a',routing_info_url='http://*********/api/1/routinginfo',cancellation_enabled=0,cancelled_request_status=2,pm_auth_token="e62ceb56c28bbdc71b57fa113d59592b2c0cb7f8"


[program:group_ivrinfo_relation_handler]
directory = /home/<USER>/apps/central
command = bash /home/<USER>/apps/central/deploy/management_command.sh group_ivrinfo_relation_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = /var/log/apps/central/supervisor/out_group_ivrinfo_relation_handler.log
stderr_logfile = /var/log/apps/central/supervisor/err_group_ivrinfo_relation_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8,REDIS_HOST='*********',REDIS_PORT='6380',AWS_SECRET_ACCESS_KEY='CWb995iYGD/ur3NmHF1yeCxWWF0vTpoAnFz+Mz/t',AWS_ACCESS_KEY_ID='********************',celery_default_queue='obd_central_queue',shared_redis_host='*********',shared_redis_port='6381',source_name='call',cancel_service='cancel_service',response_manager='response_manager',expiry_manager='expiry_manager',udc_api_base_url='https://microapi.myoperator.dev/obd/',fixdid_api_base_url='https://microapi.myoperator.dev/fix-did/',obd_queue_tag_key='obd',obd_queue_tag_value='obd',shared_redis_version='cs',server_route_queue_name='obd_service_route_queue',ivr_rule_route_name='ivr_rule',process_flow_router_name='process_flow_get',kam_group_servers_route_name='kam_group_servers',kam_group_servers_list_route_name='kam_group_server_list',global_cancellation_route_name='global_cancellation',kam_group_servers_token='16c32ccf89a8f9639eecc42c2365a6b516dc8e8a',routing_info_url='http://*********/api/1/routinginfo',cancellation_enabled=0,cancelled_request_status=2,pm_auth_token="e62ceb56c28bbdc71b57fa113d59592b2c0cb7f8"