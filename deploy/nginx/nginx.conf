upstream central {
    # server unix:/tmp/gunicorn.sock fail_timeout=0;
    #server unix:///tmp/appd.sock;
    # For a TCP configuration:
    server 0.0.0.0:8009 fail_timeout=0;
}

# This server should only get non SSL traffic. SSL should be
# handled at Load balancer level
server {
        listen  80;
        server_name localhost;

        client_max_body_size 4G;

        location / {
            # checks for static file, if not found proxy to app
            try_files $uri @proxy_to_app;
        }
        location /static {
            add_header X-Robots-Tag "noindex, follow";
            alias /home/<USER>/apps/central/static;
        }
        location @proxy_to_app {
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";

                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header Host $http_host;
                proxy_redirect off;
                proxy_set_header X-Forwarded-Protocol https;
                proxy_set_header X-Forwarded-Host $server_name;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_pass   http://central;

        }
                error_page 500 502 503 504 /500.html;
                location = /500.html {
                        root /home/<USER>/apps/central/static/;
                }

                error_page 404 /404.html;
                location = /404.html {
                        root /home/<USER>/apps/central/static/;

                }
}
