

locals {
  common_tags = {
    Service   = "obd"
    App       = "central"
    CreatedBy = "terraform"
    obd       = "obd"
  }
}
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "s3" {
    bucket     = "stage-myop-terraform-state"
    key        = "obd/central/central.tfstate"
    region     = "ap-south-1"
  }
}

provider "aws" {
  region     = var.region
  default_tags {
    tags = local.common_tags
  }
}
