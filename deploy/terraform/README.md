## Terraform deployment

`AWS_PROFILE` env variable from outside provides the AWS credentials to fetch state file
We keep state file of terraform on AWS staging.

Differentiation of resources are done via workspace. There are following workspace in every application:
- dev
- stage
- prod

You need to select proper workspace while deploying to that environment. However, state file will ALWAY<PERSON>
be fetched from AWS staging. That means, `AWS_PROFILE` env var will ALWAYS point to your staging credentials


Assuming your `~/.aws/credentials` contains:

```conf
[dev]
aws_access_key_id = BBBB
aws_secret_access_key = pqrs/abc

[stage]
aws_access_key_id = AAAAA
aws_secret_access_key = abcd/xyz
```

### Deploy on dev

edit [main.tf](./main.tf) file and add `profile` to provider "aws" block:

```terraform
provider "aws" {
  region  = var.region
  profile = <aws dev profile name> # add this line
}
```
and then run (assuming your aws staging profile name is `stage`):
```sh 
AWS_PROFILE=stage terraform init -upgrade
AWS_PROFILE=stage terraform workspace select dev
AWS_PROFILE=stage terraform plan
AWS_PROFILE=stage terraform apply
```


### Deploy on staging

edit [main.tf](./main.tf) file and add `profile` to provider "aws" block:

```terraform
provider "aws" {
  region  = var.region
  profile = <aws staging profile name>
}
```
and then run:
```sh 
AWS_PROFILE=stage terraform init -upgrade
AWS_PROFILE=stage terraform workspace select stage
AWS_PROFILE=stage terraform plan
AWS_PROFILE=stage terraform apply
```
