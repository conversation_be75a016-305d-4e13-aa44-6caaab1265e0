variable "region" {
  type    = string
  default = "ap-south-1"
}


variable "office_ip_sg_name" {
  type        = string
  description = "OfficeIP security group name"
}

variable "vpc_name" {}
variable "private_subnet_name" {}

variable "SHARED_CACHE_REDIS_HOST" {
  type        = string
  description = "Shared Cache Redis Host"
}

variable "SHARED_CACHE_REDIS_PORT" {
  type        = string
  description = "Shared Cache Redis Port"
}

variable "SHARED_CACHE_REDIS_DB" {
  type        = string
  description = "Shared Cache Redis DB"
}
variable "LIVECALLS_SNS_TOPIC_NAME" {
  type        = string
  description = "LIVECALLS_SNS_TOPIC_NAME"
  default     = "livecalls"
}
variable "FIX_DID_SNS_TOPIC_NAME" {
  type        = string
  description = "FIX_DID_SNS_TOPIC_NAME"
}
variable "OBD_CENTRAL_GROUP_IVR_RELATION_SQS_NAME" {
  type        = string
  description = "OBD_CENTRAL_GROUP_IVR_RELATION_SQS_NAME"
}
variable "OBD_RM_SQS_NAME" {
  type        = string
  description = "OBD_RM_SQS_NAME"
}
variable "OBD_SERVICE_ROUTE_SQS_NAME" {
  type        = string
  description = "OBD_SERVICE_ROUTE_SQS_NAME"
}
variable "OBD_CENTRAL_DELAY_QUEUE_SQS_NAME" {
  type        = string
  description = "OBD_CENTRAL_DELAY_QUEUE_SQS_NAME"
}

variable "OBD_CENTRAL_PQ2_NAME" {
  type        = string
  description = "OBD_CENTRAL_PQ2_NAME"
}
variable "OBD_CENTRAL_PQ3_NAME" {
  type        = string
  description = "OBD_CENTRAL_PQ3_NAME"
}
variable "OBD_CENTRAL_ON_HOLD_NAME" {
  type        = string
  description = "OBD_CENTRAL_ON_HOLD_NAME"
}
variable "OBD_CENTRAL_NOTIFIER_LAMBDA_NAME" {
  type        = string
  description = "OBD_CENTRAL_NOTIFIER_LAMBDA_NAME"
}


variable "OBD_SG_NAME" {
  type        = string
  description = "OBD_SG_NAME"
}


variable "LAMBDA_ROLE" {
  type        = string
  description = "OBD_LAMBDA_ROLEG_NAME"
}



variable "COMAPNY_USAGE_CHECK_CACHE_COUNT_KEY" {
  type        = string
  description = "COMAPNY_USAGE_CHECK_CACHE_COUNT_KEY for OBD central Notifier lambda"
}
variable "COMPANYUSAGE_CHECK_SNS_NAME" {
  type        = string
  description = "COMPANYUSAGE_CHECK_SNS_NAME for OBD central Notifier lambda"
}
variable "GROUP_IVRINFO_RELETION_CACHE_COUNT_KEY" {
  type        = string
  description = "GROUP_IVRINFO_RELETION_CACHE_COUNT_KEY for OBD central Notifier lambda"
}
variable "GROUP_IVR_RELETION_SNS_NAME" {
  type        = string
  description = "GROUP_IVR_RELETION_SNS_NAME for OBD central Notifier lambda"
}
variable "REDIS_HOST" {
  type        = string
  description = "REDIS_HOST for OBD central Notifier lambda"
}
variable "REDIS_PORT" {
  type        = string
  description = "REDIS_PORT for OBD central Notifier lambda"
}
variable "REDIS_VERSION_KEY" {
  type        = string
  description = "REDIS_VERSION_KEY for OBD central Notifier lambda"
}


variable "CENTRAL_NOTIFIER_ALIAS_NAME" {
  type        = string
  description = "CENTRAL_NOTIFIER_ALIAS_NAME"
}