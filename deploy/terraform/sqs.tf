
resource "aws_sqs_queue" "obd_central_group_ivr_relation" {
  name                       = var.OBD_CENTRAL_GROUP_IVR_RELATION_SQS_NAME
  message_retention_seconds  = 864000
  visibility_timeout_seconds = 30
  fifo_queue                 = false
  policy = (templatefile("./files/sqs_from_sns_policy.json", {
    sqs_name   = var.OBD_CENTRAL_GROUP_IVR_RELATION_SQS_NAME,
    region     = var.region,
    account_id = data.aws_caller_identity.current.account_id,
    sns_name   = data.aws_sns_topic.fix_did_sns_topic.name,
    action     = "SQS:SendMessage",
    sid        = "Sid1592317710052"

  }))

  tags = {
    Name = var.OBD_CENTRAL_GROUP_IVR_RELATION_SQS_NAME
  }
}

resource "aws_sqs_queue" "obd_central_rm" {
  name                       = var.OBD_RM_SQS_NAME
  message_retention_seconds  = 864000
  visibility_timeout_seconds = 30
  fifo_queue                 = false

  tags = {
    Name = var.OBD_RM_SQS_NAME
  }
}
resource "aws_sqs_queue" "obd_service_route" {
  name                       = var.OBD_SERVICE_ROUTE_SQS_NAME
  message_retention_seconds  = 345600
  visibility_timeout_seconds = 360
  fifo_queue                 = false

  tags = {
    Name = var.OBD_SERVICE_ROUTE_SQS_NAME
  }
}

resource "aws_sqs_queue" "obd_central_delay_queue" {
  name                       = var.OBD_CENTRAL_DELAY_QUEUE_SQS_NAME
  message_retention_seconds  = 864000
  visibility_timeout_seconds = 30
  fifo_queue                 = false

  tags = {
    Name = var.OBD_CENTRAL_DELAY_QUEUE_SQS_NAME
  }
}

resource "aws_sqs_queue" "obd_central_pq2" {
  name                       = var.OBD_CENTRAL_PQ2_NAME
  message_retention_seconds  = 864000
  visibility_timeout_seconds = 15
  fifo_queue                 = false

  tags = {
    Name = var.OBD_CENTRAL_PQ2_NAME
  }
}

resource "aws_sqs_queue" "obd_central_pq3" {
  name                       = var.OBD_CENTRAL_PQ3_NAME
  message_retention_seconds  = 864000
  visibility_timeout_seconds = 15
  fifo_queue                 = false

  tags = {
    Name = var.OBD_CENTRAL_PQ3_NAME
  }
}

resource "aws_sqs_queue" "obd_central_on_hold" {
  name                       = var.OBD_CENTRAL_ON_HOLD_NAME
  message_retention_seconds  = 864000
  visibility_timeout_seconds = 30
  fifo_queue                 = false

  tags = {
    Name = var.OBD_CENTRAL_ON_HOLD_NAME
  }
}
