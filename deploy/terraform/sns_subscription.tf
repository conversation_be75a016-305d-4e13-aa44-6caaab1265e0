
resource "aws_sns_topic_subscription" "channel_cache_cleanup_live_call_subscription" {
  topic_arn            = data.aws_sns_topic.livecalls_sns_topic.arn
  protocol             = "lambda"
  endpoint             = module.channel_cache_cleanup_lambda.lambda_function_arn
  raw_message_delivery = false
}


resource "aws_lambda_permission" "allow_sns_for_channel_cache_cleanup" {
  statement_id  = "AllowExecutionFromAccountSns"
  action        = "lambda:InvokeFunction"
  function_name = module.channel_cache_cleanup_lambda.lambda_function_name
  principal     = "sns.amazonaws.com"
  source_arn    = data.aws_sns_topic.livecalls_sns_topic.arn
  depends_on = [module.obd_central_notifier_lambda]
}

resource "aws_sns_topic_subscription" "obd_central_notifier_fix_did_subscription" {
  topic_arn  = data.aws_sns_topic.fix_did_sns_topic.arn
  protocol   = "lambda"
  endpoint   = aws_lambda_alias.obd_central_notifier.arn
  depends_on = [module.obd_central_notifier_lambda]
}


resource "aws_lambda_permission" "allow_sns_for_obd_central_notifier" {
  statement_id  = "AllowExecutionFromFixDidSns"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_alias.obd_central_notifier.arn
  principal     = "sns.amazonaws.com"
  source_arn    = data.aws_sns_topic.fix_did_sns_topic.arn
}


resource "aws_sns_topic_subscription" "fix_did_obd_central_group_ivr_relation_sub" {
  topic_arn            = data.aws_sns_topic.fix_did_sns_topic.arn
  protocol             = "sqs"
  endpoint             = aws_sqs_queue.obd_central_group_ivr_relation.arn
  raw_message_delivery = true
}

