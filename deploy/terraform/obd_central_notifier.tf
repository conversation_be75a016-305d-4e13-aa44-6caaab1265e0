
module "obd_central_notifier_lambda" {
  source        = "terraform-aws-modules/lambda/aws"
  version       = "4.16.0"
  function_name = var.OBD_CENTRAL_NOTIFIER_LAMBDA_NAME
  source_path   = "../../aws_lambdas/obd_central_notifier"
  handler       = "handler.lambda_handler"
  runtime       = "python3.10"

  vpc_subnet_ids                    = data.aws_subnets.private.ids
  vpc_security_group_ids            = [data.aws_security_group.obd.id]
  attach_network_policy             = true
  attach_cloudwatch_logs_policy     = true
  cloudwatch_logs_retention_in_days = 14
  create_role                       = false
  lambda_role                       = data.aws_iam_role.obd_lambda_role.arn
  publish                           = true
  timeout                           = 300
  memory_size                       = 256

  environment_variables = {
    COMAPNY_USAGE_CHECK_CACHE_COUNT_KEY    = var.COMAPNY_USAGE_CHECK_CACHE_COUNT_KEY
    COMPANYUSAGE_CHECK_SNS_NAME            = var.COMPANYUSAGE_CHECK_SNS_NAME
    GROUP_IVRINFO_RELETION_CACHE_COUNT_KEY = var.GROUP_IVRINFO_RELETION_CACHE_COUNT_KEY
    GROUP_IVR_RELETION_SNS_NAME            = var.GROUP_IVR_RELETION_SNS_NAME
    REDIS_HOST                             = var.REDIS_HOST
    REDIS_PORT                             = var.REDIS_PORT
    REDIS_VERSION_KEY                      = var.REDIS_VERSION_KEY
  }

  tags = {
    Name = var.OBD_CENTRAL_NOTIFIER_LAMBDA_NAME
  }
}



resource "aws_lambda_alias" "obd_central_notifier" {
  name             = var.CENTRAL_NOTIFIER_ALIAS_NAME
  function_name    = module.obd_central_notifier_lambda.lambda_function_name
  function_version = module.obd_central_notifier_lambda.lambda_function_version
  depends_on       = [module.obd_central_notifier_lambda]
}


output "obd_central_notifier_lambda_arn" {
  value = module.channel_cache_cleanup_lambda.lambda_function_arn
}

output "obd_central_notifier_lambda_name" {
  value = module.channel_cache_cleanup_lambda.lambda_function_name
}

output "obd_central_notifier_lambda_invoke_arn" {
  value = module.channel_cache_cleanup_lambda.lambda_function_invoke_arn
}

