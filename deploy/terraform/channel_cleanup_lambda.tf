
module "channel_cache_cleanup_lambda" {
  source        = "terraform-aws-modules/lambda/aws"
  version       = "4.16.0"
  function_name = "obd-channel-cache-cleanup"
  source_path   = "../../aws_lambdas/channel_cache_cleanup"
  handler       = "handler.lambda_handler"
  runtime       = "python3.10"

  vpc_subnet_ids                    = data.aws_subnets.private.ids
  vpc_security_group_ids            = [data.aws_security_group.officeip.id]
  attach_network_policy             = true
  attach_cloudwatch_logs_policy     = true
  cloudwatch_logs_retention_in_days = 14
  create_role                       = true

  environment_variables = {
    REDIS_HOST = var.SHARED_CACHE_REDIS_HOST
    REDIS_PORT = var.SHARED_CACHE_REDIS_PORT
    REDIS_DB   = var.SHARED_CACHE_REDIS_DB
  }

  tags = {
    Service   = "obd"
    Module    = "central"
    CreatedBy = "terraform"
  }
}



output "channel_cache_cleanup_lambda_function_arn" {
  value = module.channel_cache_cleanup_lambda.lambda_function_arn
}

output "channel_cache_cleanup_lambda_function_name" {
  value = module.channel_cache_cleanup_lambda.lambda_function_name
}

output "channel_cache_cleanup_lambda_function_invoke_arn" {
  value = module.channel_cache_cleanup_lambda.lambda_function_invoke_arn
}

