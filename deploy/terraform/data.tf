
data "aws_caller_identity" "current" {}


data "aws_vpc" "my_vpc" {
  filter {
    name   = "tag:Name"
    values = ["${var.vpc_name}"]
  }
}



data "aws_subnets" "private" {

  filter {
    name   = "tag:Name"
    values = ["${var.private_subnet_name}"]
  }

  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.my_vpc.id]
  }
}

data "aws_security_group" "officeip" {
  name   = var.office_ip_sg_name
  vpc_id = data.aws_vpc.my_vpc.id
}

data "aws_security_group" "obd" {
  name   = var.OBD_SG_NAME
  vpc_id = data.aws_vpc.my_vpc.id
}

data "aws_sns_topic" "livecalls_sns_topic" {
  name = var.LIVECALLS_SNS_TOPIC_NAME
}
data "aws_sns_topic" "fix_did_sns_topic" {
  name = var.FIX_DID_SNS_TOPIC_NAME
}

data "aws_iam_role" "obd_lambda_role" {
  name = var.LAMBDA_ROLE
}
