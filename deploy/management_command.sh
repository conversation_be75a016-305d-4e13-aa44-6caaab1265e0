#!/bin/bash

PYTHON_INTERPRETER="${PWD}/venv/bin/python3"


if [[ $# -eq 0 ]] ; then
	echo 'please provide valid argument..'
	exit 0
fi

central_1_handler() {
	"${PYTHON_INTERPRETER}" manage.py central_1_handler
	sleep 1s
}
central_2_handler() {
	"${PYTHON_INTERPRETER}" manage.py central_2_handler
}
on_hold_processor_handler() {
	"${PYTHON_INTERPRETER}" manage.py on_hold_processor_handler
}
on_hold_circulation_handler() {
	"${PYTHON_INTERPRETER}" manage.py on_hold_circulation_handler
}
response_manager_handler() {
	"${PYTHON_INTERPRETER}" manage.py response_manager_handler
}
expiry_manager_handler() {
	"${PYTHON_INTERPRETER}" manage.py expiry_manager_handler
}
central_delay_queue_handler() {
	"${PYTHON_INTERPRETER}" manage.py central_delay_queue_handler
}
company_usage_check_handler() {
	"${PYTHON_INTERPRETER}" manage.py company_usage_check_handler
}
group_server_consistancy_handler() {
	"${PYTHON_INTERPRETER}" manage.py group_server_consistancy_handler
	#sleep 12h
}
group_ivrinfo_relation_handler() {
	"${PYTHON_INTERPRETER}" manage.py group_ivrinfo_relation_handler
	sleep 3s
}
expiry_manager_handler() {
	"${PYTHON_INTERPRETER}" manage.py expiry_manager_handler
	sleep 3s
}
reverse_expiry_request_mark_from_ivr_processor() {
	"${PYTHON_INTERPRETER}" manage.py reverse_expiry_request_mark_from_ivr_processor
}
data_deletion_handler() {
	"${PYTHON_INTERPRETER}" manage.py data_deletion_handler
}

if [ $1 == "central_1_handler" ]
then
  central_1_handler
fi

if [ $1 == "central_2_handler" ]
then
  central_2_handler
fi
if [ $1 == "on_hold_processor_handler" ]
then
  on_hold_processor_handler
fi
if [ $1 == "on_hold_circulation_handler" ]
then
  on_hold_circulation_handler
fi
if [ $1 == "response_manager_handler" ]
then
  response_manager_handler
fi
if [ $1 == "central_delay_queue_handler" ]
then
  central_delay_queue_handler
fi
if [ $1 == "company_usage_check_handler" ]
then
  company_usage_check_handler
fi
if [ $1 == "group_server_consistancy_handler" ]
then
  group_server_consistancy_handler
fi
if [ $1 == "group_ivrinfo_relation_handler" ]
then
  group_ivrinfo_relation_handler
fi
if [ $1 == "expiry_manager_handler" ]
then
  expiry_manager_handler
fi
if [ $1 == "reverse_expiry_request_mark_from_ivr_processor" ]
then
  reverse_expiry_request_mark_from_ivr_processor
fi
if [ $1 == "data_deletion_handler" ]
then
  data_deletion_handler
fi