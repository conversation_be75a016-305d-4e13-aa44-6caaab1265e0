---
- hosts: group_central_1a:group_central_2a
  remote_user: ubuntu
  become: True
  become_method: sudo
  become_user: root
  vars:
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_python_interpreter: /usr/bin/python3.10
      app_name: central
      app_version: current
      app_version_name: "{{ (app_version == 'current')|ternary(app_name, app_name+'_'+app_version) }}"
      git_repo:
      git_branch:
      project_base: "/var/app/python/{{ app_name }}"
      deploy_name: "{{ ansible_date_time.year }}{{ ansible_date_time.month }}{{ ansible_date_time.day}}{{ ansible_date_time.hour }}"
      release_dir: "{{ project_base }}/releases/{{ deploy_name }}"
      pip_requirement_file: "{{ release_dir }}/requirements/production.txt"
      venv_dir: "{{ release_dir }}/venv"
      base_log_dir: "/var/log/app/{{ app_name }}"
      log_dir: "{{ base_log_dir }}/{{ (app_version == 'current')|ternary('', app_version) }}"
  tasks:
    - name: Create directory
      file:
        path: "{{ release_dir }}"
        state: directory
        mode: 0755
        group: root
        owner: root

    - import_tasks: tasks/current_release.yml
      name: Print current release

    - import_tasks: tasks/git.yml
      name: Git sync tasks
    - name: Install virtualenv and install requirements
      pip:
       requirements: "{{ pip_requirement_file }}"
       virtualenv: "{{ venv_dir }}"
       virtualenv_command: /usr/bin/python3.10 -m venv
    - name: copying ENV file
      copy:
        src: .env
        dest: "{{ project_base }}/releases/{{ deploy_name }}/.env"
    - name: Create log path
      file:
        path: "{{ log_dir }}"
        state: directory
        mode: 0777

    - name: Create directory for stage
      file:
        path: "{{ release_dir }}/deploy/supervisor/stage"
        state: directory
        mode: 0755
        group: root
        owner: root

    - name: Create supervisor file 1a
      template:
        src: central_1a_stage.conf
        dest: "{{ release_dir }}/deploy/supervisor/stage/central_1a.conf"

    - name: Create supervisor file 2a
      template:
        src: central_2a_stage.conf
        dest: "{{ release_dir }}/deploy/supervisor/stage/central_2a.conf"

    - name: Executing collectstatic
      shell: "venv/bin/python manage.py collectstatic --no-input"
      args:
        chdir: "{{ release_dir }}"

- hosts: group_central_1a
  remote_user: ubuntu
  become: True
  become_method: sudo
  become_user: root
  vars:
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_python_interpreter: /usr/bin/python3.10
      app_name: central
      app_version: current
      app_version_name: "{{ (app_version == 'current')|ternary(app_name, app_name+'_'+app_version) }}"
      project_base: "/var/app/python/{{ app_name }}"
      deploy_name: "{{ ansible_date_time.year }}{{ ansible_date_time.month }}{{ ansible_date_time.day}}{{ ansible_date_time.hour }}"

  tasks:

    - name: Create version symlink
      shell: ln -sfn "releases/{{ deploy_name }}" "{{ app_version }}"
      args:
        chdir: "{{ project_base }}"

    - name: Symlink supervisor
      shell: ln -sfn "{{ project_base }}/{{ app_version }}/deploy/supervisor/stage/central_1a.conf" "{{ app_version_name }}.conf"
      args:
        chdir: /etc/supervisor/conf.d
    - name: Restart supervisor
      shell: "supervisorctl reread; supervisorctl update {{ app_version_name }}; supervisorctl restart {{ app_version_name }}:"

- hosts: group_central_2a
  remote_user: ubuntu
  become: True
  become_method: sudo
  become_user: root
  vars:
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_python_interpreter: /usr/bin/python3.10
      app_name: central
      app_version: current
      app_version_name: "{{ (app_version == 'current')|ternary(app_name, app_name+'_'+app_version) }}"
      project_base: "/var/app/python/{{ app_name }}"
      deploy_name: "{{ ansible_date_time.year }}{{ ansible_date_time.month }}{{ ansible_date_time.day}}{{ ansible_date_time.hour }}"
  tasks:

    - name: Create version symlink
      shell: ln -sfn "releases/{{ deploy_name }}" "{{ app_version }}"
      args:
        chdir: "{{ project_base }}"

    - name: Symlink supervisor
      shell: ln -sfn "{{ project_base }}/{{ app_version }}/deploy/supervisor/stage/central_2a.conf" "{{ app_version_name }}.conf"
      args:
        chdir: /etc/supervisor/conf.d
    - name: Restart supervisor
      shell: "supervisorctl reread; supervisorctl update {{ app_version_name }}; supervisorctl restart {{ app_version_name }}:"
