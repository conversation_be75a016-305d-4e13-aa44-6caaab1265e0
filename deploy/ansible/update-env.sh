#!/bin/bash

set -e
set -o pipefail

secret_id="central/current"

fetch_secrets() {
  aws secretsmanager get-secret-value --secret-id $secret_id | jq --raw-output .SecretString | jq -r 'to_entries[] | "\(.key)=\(.value)"' > .env
}


while [ "$1" != "" ]; do
  case $1 in
    -i | --id )           shift
                          secret_id=$1
                          ;;
    fetch )               fetch_secrets
    esac
    shift
done
