[group:{{ app_version_name }}]
programs={{ app_version_name }}_gunicorn,{{ app_version_name }}_1_handler,{{ app_version_name }}_2_handler,{{ app_version_name }}_response_manager_handler,{{ app_version_name }}_group_ivrinfo_relation_handler

[program:{{ app_version_name }}_gunicorn]
directory = {{ project_base }}/{{ app_version }}
command = bash {{ project_base }}/{{ app_version }}/gunicorn.bash
autostart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = {{ log_dir }}/supervisor/out_central_gunicorn_supervisor.log
stderr_logfile = {{ log_dir }}/supervisor/err_central_gunicorn_supervisor.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8


[program:{{ app_version_name }}_1_handler]
directory = {{ project_base }}/{{ app_version }}
command = bash {{ project_base }}/{{ app_version }}/deploy/management_command.sh central_1_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = {{ log_dir }}/supervisor/out_central_1_handler.log
stderr_logfile = {{ log_dir }}/supervisor/err_central_1_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8


[program:{{ app_version_name }}_2_handler]
directory = {{ project_base }}/{{ app_version }}
command = bash {{ project_base }}/{{ app_version }}/deploy/management_command.sh central_2_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = {{ log_dir }}/supervisor/out_central_2_handler.log
stderr_logfile = {{ log_dir }}/supervisor/err_central_2_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8

[program:{{ app_version_name }}_response_manager_handler]
directory = {{ project_base }}/{{ app_version }}
command = bash {{ project_base }}/{{ app_version }}/deploy/management_command.sh response_manager_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = {{ log_dir }}/supervisor/out_response_manager_handler.log
stderr_logfile = {{ log_dir }}/supervisor/err_response_manager_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8


[program:{{ app_version_name }}_group_ivrinfo_relation_handler]
directory = {{ project_base }}/{{ app_version }}
command = bash {{ project_base }}/{{ app_version }}/deploy/management_command.sh group_ivrinfo_relation_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = {{ log_dir }}/supervisor/out_group_ivrinfo_relation_handler.log
stderr_logfile = {{ log_dir }}/supervisor/err_group_ivrinfo_relation_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8