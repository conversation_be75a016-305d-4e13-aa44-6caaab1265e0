---
- stat:
    path: "{{ current_dir }}"
  register: st
- name: Create directory
  file:
    path: "{{ release_dir }}"
    state: directory
    mode: 0755
    group: root
    owner: root
  when: deploy_name|int > st.stat.lnk_source|basename|int
- name: Rsync directory
  synchronize:
    src: "{{ project_base }}/releases/{{ st.stat.lnk_source|basename }}/"
    dest: "{{ release_dir }}"
    rsync_opts:
    - "--exclude=venv"
    - "--exclude=__pycache__"
  delegate_to: "{{ inventory_hostname }}"
  when: deploy_name|int > st.stat.lnk_source|basename|int
