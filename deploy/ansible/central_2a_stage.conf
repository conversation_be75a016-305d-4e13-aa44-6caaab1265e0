[group:{{ app_version_name }}]
programs={{ app_version_name }}_gunicorn,{{ app_version_name }}_on_hold_processor_handler,{{ app_version_name }}_on_hold_circulation_handler,{{ app_version_name }}_central_delay_queue_handler

[program:{{ app_version_name }}_gunicorn]
directory = {{ project_base }}/{{ app_version }}
command = bash {{ project_base }}/{{ app_version }}/gunicorn.bash
autostart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = {{ log_dir }}/supervisor/out_central_gunicorn_supervisor.log
stderr_logfile = {{ log_dir }}/supervisor/err_central_gunicorn_supervisor.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8

[program:{{ app_version_name }}_on_hold_processor_handler]
directory = {{ project_base }}/{{ app_version }}
command = bash {{ project_base }}/{{ app_version }}/deploy/management_command.sh on_hold_processor_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = {{ log_dir }}/supervisor/out_on_hold_processor_handler.log
stderr_logfile = {{ log_dir }}/supervisor/err_on_hold_processor_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8

[program:{{ app_version_name }}_on_hold_circulation_handler]
directory = {{ project_base }}/{{ app_version }}
command = bash {{ project_base }}/{{ app_version }}/deploy/management_command.sh on_hold_circulation_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = {{ log_dir }}/supervisor/out_on_hold_circulation_handler.log
stderr_logfile = {{ log_dir }}/supervisor/err_on_hold_circulation_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8

[program:{{ app_version_name }}_central_delay_queue_handler]
directory = {{ project_base }}/{{ app_version }}
command = bash {{ project_base }}/{{ app_version }}/deploy/management_command.sh central_delay_queue_handler
autostart=true
autorestart=true
user = ubuntu
stopsignal=KILL
killasgroup=true
stdout_logfile = {{ log_dir }}/supervisor/out_central_delay_queue_handler.log
stderr_logfile = {{ log_dir }}/supervisor/err_central_delay_queue_handler.log
environment=LANG=en_US.UTF-8,LC_ALL=en_US.UTF-8

# [program:{{app_version_name}}_celery]
# directory={{ project_base }}/{{ app_version }}/
# command={{ project_base }}/{{ app_version }}/venv/bin/celery -A central worker --loglevel=INFO -n worker@{{ app_version_name }} --concurrency=2 --without-gossip --without-mingle --without-heartbeat -Ofair

# user=ubuntu
# numprocs=1
# stdout_logfile={{ log_dir }}/celery.log
# stderr_logfile={{ log_dir }}/celery_error.log
# autostart=true
# autorestart=true
# startsecs=0
# killasgroup=true

# ; Need to wait for currently executing tasks to finish at shutdown.
# ; Increase this if you have very long running tasks.
# ; stopwaitsecs = 600

# ; Causes supervisor to send the termination signal (SIGTERM) to the whole process group.
# stopasgroup=true

