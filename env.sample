DJANGO_SETTINGS_MODULE="central.settings.local"
DJANGO_ALLOWED_HOSTS="comma seperated string of allowed hosts"  [Optional] 
DB_ENGINE="django.db.backends.mysql" [Optional]
DB_NAME="central"
DB_USER="root"
DB_PASSWORD="root"
DB_HOST="127.0.0.1"
DB_PORT="3306"
READ_DB_ENGINE="django.db.backends.mysql" [Optional]
READ_DB_NAME="central_read" [Optional]
READ_DB_USER="root" [Optional]
READ_DB_PASSWORD="root" [Optional]
READ_DB_HOST="127.0.0.1" [Optional]
READ_DB_PORT="3306" [Optional]
REDIS_HOST="127.0.0.1"
REDIS_PORT=6369
REDIS_DB=0
shared_redis_host="127.0.0.1"
shared_redis_port=6369
shared_redis_db=2
DJANGO_SECRET="121212"  [Optional] [This should not be changed ever]
LOGFILE_PATH=common.log
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_REGION_NAME="ap-south-1"
celery_default_queue="adasdsad"
cancellation_api_url=""
cancellation_enabled="1"
cancelled_request_status=1
source_name=""
cancel_service=""
response_manager=""
expiry_manager=""
complete_service=""
routing_info_url=""
pm_auth_token=""
server_route_queue_name=""
ivr_rule_route_name=""
process_flow_router_name=""
kam_group_servers_route_name=""
kam_group_servers_list_route_name=""
global_cancellation_route_name=""
completed_requests_route_name=""
kam_group_servers_token=""
udc_api_base_url=""
rule_api_base_url=""
obd_queue_tag_key=""
obd_queue_tag_value=""
fixdid_api_base_url=""
shared_redis_version=""
canceled_request_key=""
jp_requests_list_api_base_url=""
jp_requests_list_api_token=""
API_CACHE_TTL=80 
KAM_API_CACHE_TTL=120
LOGGER_SLACK_URL=""