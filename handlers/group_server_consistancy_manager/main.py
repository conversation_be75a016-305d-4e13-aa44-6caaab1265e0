from django.conf import settings

from central.settings.handlers import conf
from handlers.models import Group
from utills.external_apis import ExternalAPIs
from utills.group_server_relation_manager.main import (
    GroupServerRelationManager,
)


class GroupServerConsistancyHandler:
    @classmethod
    def run(cls):
        """
        This handler is responsible for managing consistency between Kamiolio and OBD Central groups and servers.
        Responsibilities:
                1. Get the Kamailio Groups Data
                2. Check for group existence
                        2.1 If Exist: Update groups name, settings, is_enable and also create server if not exist and
                                manage the relationship between groups and servers.
                        2.2 If not Exist: Create Group and also create servers if not exist and
                                manage the relationship between groups and servers.
                3. Get live groups and servers list from Kamailio
                4. Disable all extra groups and servers in OBD Central.
        Args:
                None
        Returns:
                False
        """
        kam_cache_key = conf.KAM_GROUP_SERVER_CACHE_KEY
        kam_api_name = conf.ROUTING_INFO_URL_NAMES[
            "kam_group_servers_route_name"
        ]
        payload = {
            "token": conf.SECRET_TOKENS["kam_group_servers_token"],
            "kam_group_id": "",
            "is_obd": 1,
        }

        groups_data = ExternalAPIs.talk_to_kamailio_api(
            kam_cache_key, kam_api_name, **payload
        )

        for kam_group_id in groups_data:
            group_obj = Group.objects.filter(kam_group_id=kam_group_id)
            if group_obj.exists():
                GroupServerRelationManager.update_group_servers(
                    group_obj, kam_group_id, groups_data[kam_group_id]
                )
            else:
                GroupServerRelationManager.add_group_servers(
                    kam_group_id, groups_data[kam_group_id]
                )

        # Active Kamiolio Groups & Servers List.
        kam_cache_key2 = conf.KAM_GROUP_SERVER_LIST_CACHE_KEY
        kam_api_name2 = conf.ROUTING_INFO_URL_NAMES[
            "kam_group_servers_list_route_name"
        ]
        payload2 = {
            "token": conf.SECRET_TOKENS["kam_group_servers_token"],
            "is_obd": 1,
        }
        group_server_list = ExternalAPIs.talk_to_kamailio_api(
            kam_cache_key2, kam_api_name2, **payload2
        )
        if not group_server_list:
            settings.API_LOGGER.error(
                "group_server_consistancy_manager - kam group_server_list not found"
            )
            return

        GroupServerRelationManager.disable_groups(group_server_list["groups"])
        GroupServerRelationManager.disable_servers(
            group_server_list["servers"]
        )
