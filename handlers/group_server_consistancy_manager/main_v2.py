import logging
from typing import Dict

from central.settings.handlers import conf
from handlers.models import Group
from utills.external_apis import ExternalAPIs
from utills.group_server_relation_manager.main_v2 import (
    GroupServerRelationManager_V2,
)

logger = logging.getLogger(__name__)


class GroupServerConsistancyHandler_V2:
    @staticmethod
    def get_pilot_number_from_group_data(group_data: Dict) -> str:
        return group_data.get("pilot_number")

    @classmethod
    def run(cls):
        """
        This handler is responsible for managing consistency between Kamiolio and OBD Central groups and servers.
        Responsibilities:
                1. Get the Kamailio Groups Data
                2. Check for group existence
                        2.1 If Exist: Update groups name, settings, is_enable and also create server if not exist and
                                manage the relationship between groups and servers.
                        2.2 If not Exist: Create Group and also create servers if not exist and
                                manage the relationship between groups and servers.
                3. Get live groups and servers list from Kamailio
                4. Disable all extra groups and servers in OBD Central.
        Args:
                None
        Returns:
                False
        """

        cls.create_or_update_groups()
        cls.disable_group_servers()

    @classmethod
    def create_or_update_groups(cls):
        kam_cache_key = conf.KAM_GROUP_SERVER_CACHE_KEY
        kam_api_name = conf.ROUTING_INFO_URL_NAMES[
            "kam_group_servers_route_name"
        ]
        payload = {
            "token": conf.SECRET_TOKENS["kam_group_servers_token"],
            "kam_group_id": "",
            "is_obd": 1,
        }

        groups_data = ExternalAPIs.talk_to_kamailio_api(
            kam_cache_key, kam_api_name, **payload
        )

        for kam_group_id in groups_data:
            pilot_number = cls.get_pilot_number_from_group_data(
                groups_data[kam_group_id]
            )
            if not pilot_number:
                logger.error(
                    f"Pilot Number not found in Group Data: {groups_data[kam_group_id]}"
                )
                continue

            group_obj = Group.objects.filter(kam_group_id=kam_group_id)
            if group_obj.exists():
                GroupServerRelationManager_V2.update_group_servers(
                    group_obj, kam_group_id, groups_data[kam_group_id]
                )
            else:
                GroupServerRelationManager_V2.add_group_servers(
                    kam_group_id, groups_data[kam_group_id], pilot_number
                )

    @classmethod
    def disable_group_servers(cls):
        kam_cache_key2 = conf.KAM_GROUP_SERVER_LIST_CACHE_KEY
        kam_api_name2 = conf.ROUTING_INFO_URL_NAMES[
            "kam_group_servers_list_route_name"
        ]
        payload2 = {
            "token": conf.SECRET_TOKENS["kam_group_servers_token"],
            "is_obd": 1,
        }
        group_server_list = ExternalAPIs.talk_to_kamailio_api(
            kam_cache_key2, kam_api_name2, **payload2
        )
        if not group_server_list:
            logger.error(
                "group_server_consistancy_manager - kam group_server_list not found"
            )
            return
        GroupServerRelationManager_V2.disable_groups(
            group_server_list["groups"]
        )
        GroupServerRelationManager_V2.disable_servers(
            group_server_list["servers"]
        )
