import json
import time

from django.conf import settings

from central.settings.handlers import conf
from handlers.models import CentralRequestInfo
from handlers.tasks import route_completed_request
from utills.cache_manager.main import CacheManager
from utills.helpers.helper import Helper
from utills.sqs_manager.main import SQSManager


class ResponseManager:
    @classmethod
    def run(cls):
        """
        Responsibilities:
                1. RESPONSE_Q_PULLING
                2. Hit Process-Flow
                3. Hit Process-Manager
                2. Update CentralReqInfo
                3. Delete from RESPONSE_Q
        Parameters:
                None
        Returns:
                False
        """

        rm_q_name_url = Helper.get_queue_name_url_mapping(
            "queue_name",
            conf.RESPONSE_MANAGER_QUEUE,
            conf.RESPONSE_MANAGER_GATEWAY_PREFIX,
        )

        rm_queue_url = rm_q_name_url[conf.RESPONSE_MANAGER_QUEUE]

        rm_msg_count = CacheManager.get_value(conf.RESPONSE_MANANGER_COUNT_KEY)

        if not rm_msg_count:
            log_msg = "response_manager - cache request count key is None, exiting after sleeping for {dur}s...".format(
                dur=conf.RM_SLEEP_DUR
            )
            Helper.sampling_logger_emitter(log_msg)
            time.sleep(conf.RM_SLEEP_DUR)
            return

        while rm_msg_count >= 0:
            # Loop will break if no msg found in RM_Q
            requests = SQSManager.fetch_message_sp(rm_queue_url, 10, False)
            if not requests:
                settings.API_LOGGER.info(
                    "response_manager - no new requests found, exiting after sleeping for {dur}s...".format(
                        dur=conf.RM_SLEEP_DUR
                    )
                )
                time.sleep(conf.RM_SLEEP_DUR)
                break

            for req in requests:
                req_body = json.loads(req["Body"])

                try:
                    central_req_obj = CentralRequestInfo.objects.filter(
                        request_id=req_body["request_id"]
                    )

                    if central_req_obj.exists():
                        central_req = central_req_obj.last()
                        if (
                            central_req.is_req_completed
                            == CentralRequestInfo.YES
                            and central_req.completion_event_type
                            == CentralRequestInfo.FROM_RESPONSE_MANAGER
                        ):
                            settings.API_LOGGER.info(
                                "response_manager - request is already complete. request_id: {req_id}, deteting request, request_body: {rb}".format(
                                    req_id=req_body["request_id"], rb=req_body
                                )
                            )
                        else:
                            central_req.is_req_completed = (
                                CentralRequestInfo.YES
                            )
                            # to avoid event call of model tracing deltetion
                            central_req.is_onhold_req = CentralRequestInfo.NA
                            central_req.completion_event_type = (
                                CentralRequestInfo.FROM_RESPONSE_MANAGER
                            )
                            # Note: Please update this once confirmed from engine end.
                            central_req.event_response = req_body[
                                "event_response"
                            ]
                            central_req.save(
                                update_fields=[
                                    "is_req_completed",
                                    "is_onhold_req",
                                    "completion_event_type",
                                    "event_response",
                                    "updated_on",
                                ]
                            )

                        # async-invoke process-flow and process-manager
                        route_completed_request(
                            central_req,
                            "Completed",
                            "200",
                            conf.OBD_SERVICES["response_manager"],
                        )
                    else:
                        settings.API_LOGGER.error(
                            "response_manager - no entries in CentralRequestInfo for request_id: {req_id}, deteting request, request_body: {rb}".format(
                                req_id=req_body["request_id"], rb=req_body
                            )
                        )

                    SQSManager.sqs_delete_message(
                        rm_queue_url, req["ReceiptHandle"]
                    )
                    CacheManager.decr_value(
                        conf.RESPONSE_MANANGER_COUNT_KEY, True
                    )
                    rm_msg_count = rm_msg_count - 1
                except Exception as e:
                    settings.API_LOGGER.error(
                        "response_manager - error updating CentralRequestInfo for request_id: {req_id}, e: {e}".format(
                            req_id=req_body["request_id"], e=e
                        )
                    )
