import time
import traceback
from concurrent.futures import Thread<PERSON>oolExecutor

from django.conf import settings
from django.core.management.base import BaseCommand

from central.settings.handlers import conf
from handlers.central_1.helper import Central1_Helper
from handlers.central_1.main import Central1
from utills.helpers.helper import Helper


def run_central1():
    """Creates threads and calls central_1 handler"""

    # Getting IVR keys to process from cache
    ivrs_to_process = Central1_Helper.get_priority_ivr_mapping_to_process()
    if not ivrs_to_process:
        log_msg = "central_1 - No IVR Keys found in cache to process, exiting after sleeping for '{sleep}'s...".format(
            sleep=conf.CENTRAL_1_SLEEP_DUR
        )
        Helper.sampling_logger_emitter(log_msg)
        time.sleep(conf.CENTRAL_1_SLEEP_DUR)
        return

    # Getting PQs msg count and max_workers
    (
        c1_threshold,
        max_workers,
        pq2_thread_threshold,
        pq3_thread_threshold,
        single_ivr_threshold,
    ) = Central1_Helper.get_thresholds()

    if pq2_thread_threshold < 0:
        pq2_thread_threshold = 0
    if pq3_thread_threshold < 0:
        pq3_thread_threshold = 0

    # Exit process if no PQs are not available.
    if (pq2_thread_threshold + pq3_thread_threshold) <= 0:
        log_msg = "central_1 - PQ2 & PQ3 queues are full, exiting handler after sleeping for {dur}s...".format(
            dur=conf.CENTRAL_1_SLEEP_DUR
        )
        Helper.sampling_logger_emitter(log_msg)
        time.sleep(conf.CENTRAL_1_SLEEP_DUR)
        return

    settings.API_LOGGER.info(
        "central_1 - IVRs to process: {ivrs_to_process}, PQ2 available threshold: {pq2}, PQ3 available threshold: {pq3}".format(
            ivrs_to_process=ivrs_to_process,
            pq2=pq2_thread_threshold,
            pq3=pq3_thread_threshold,
        )
    )

    # Creating Threads based on max_workers value of DB.
    futures = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for i in range(0, max_workers):
            futures.append(
                executor.submit(
                    Central1().run,
                    (
                        c1_threshold,
                        pq2_thread_threshold,
                        pq3_thread_threshold,
                        ivrs_to_process,
                        single_ivr_threshold,
                    ),
                )
            )

    for f in futures:
        try:
            exp = f.result()
        except Exception:
            settings.API_LOGGER.error(
                "central_1 - error occured, e: {e}".format(
                    e="--".join(["Error:", str(traceback.format_exc())])
                )
            )


class Command(BaseCommand):
    help = "Run central service (pull from queue)"

    def handle(self, *args, **options):
        try:
            threshold = Helper.get_threshold(
                conf.CENTRAL_1_ENTITY, conf.CENTRAL_1_THRESHOLD
            )
            central_1_loop_count = threshold[conf.CENTRAL_1_LOOP_COUNT_KEY]
        except Exception:
            central_1_loop_count = conf.CENTRAL_1_THRESHOLD[
                conf.CENTRAL_1_LOOP_COUNT_KEY
            ]

        settings.API_LOGGER.tempinfo("central_1 - starting execution.")
        for _ in range(0, int(central_1_loop_count)):
            run_central1()
        settings.API_LOGGER.tempinfo("central_1 - ending execution.")
