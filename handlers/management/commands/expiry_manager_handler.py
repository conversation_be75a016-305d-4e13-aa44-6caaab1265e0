# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import json
import sys
import threading
import traceback
from concurrent.futures import ThreadPoolExecutor, wait, as_completed,ProcessPoolExecutor

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

from handlers.expiry_manager.main import ExpiryManager

def run_expiry_manager():
	settings.API_LOGGER.tempinfo("expiry_manager - starting execution.")
	ExpiryManager.process()
	settings.API_LOGGER.tempinfo("expiry_manager - ending execution.")

class Command(BaseCommand):
	help = 'Run central service (pull from queue)'

	def handle(self, *args, **options):
	    run_expiry_manager()