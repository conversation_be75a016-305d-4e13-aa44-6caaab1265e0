# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

from handlers.ivr_queue_remover.main import IVRQueueRemover


def run_ivr_queue_remover():
	IVRQueueRemover().run()


class Command(BaseCommand):
	help = 'Run central service (pull from queue)'

	def handle(self, *args, **options):
		run_ivr_queue_remover()