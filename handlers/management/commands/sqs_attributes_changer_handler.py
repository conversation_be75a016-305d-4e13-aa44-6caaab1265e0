# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import json
import sys

from django.conf import settings
from django.core.management.base import BaseCommand

from handlers.models import SQSQueueInfo

from utills.sqs_manager.main import SQSManager


def run_sqs_attributes_changer():
	try:

		for index, prefix_name in enumerate(settings.QUEUES_PREFIX_LIST):
			print("{i}. {prefix_name}".format(i=index + 1, prefix_name=prefix_name))

		queue_prefix = input("Please enter a queue_prefix from given valid queue_prefix list. Note: This is a criticle change you must be sure that queue_prefix must not clash with other queues: ").strip("")

		if not queue_prefix or len(queue_prefix) < 10:
			print("Invalid queue_prefix! len of queue_prefix must be greater than 10")
			return

		queue_urls = SQSQueueInfo.objects.filter(queue_name__startswith=queue_prefix).values_list('queue_url', flat=True).distinct().all()
		queue_urls = list(queue_urls)

		if not queue_urls:
			sys.exit("Queues Not Found with the givrn prefix.")

		show_queues_list = input("We have {len} Queues in SQSQueueInfo with given prefix, Do you Want to see the list? y/n: ".format(len=len(queue_urls)))
		if show_queues_list and (show_queues_list in ['y', 'Y']):
			for i, q_url in enumerate(queue_urls):
				print("{i}. {q_url}".format(i=i + 1, q_url=q_url))

		print("")
		attributes_dict = json.loads(input('Enter Json Attributes Dict {"MessageRetentionPeriod": "86400", "VisibilityTimeout": "300"}: '))
	except Exception as err:
		sys.exit("Invalid Json. e: {err}".format(err=err))

	for queue_url in queue_urls:
		try:
			print(SQSManager.set_queue_attributes(queue_url, attributes_dict))
		except Exception as err:
			print("Some Exception Occured While Processing Queue: {q_url}, err: {err}".format(q_url=queue_url, err=err))


class Command(BaseCommand):
	help = 'Run central service (pull from queue)'

	def handle(self, *args, **options):
		run_sqs_attributes_changer()
