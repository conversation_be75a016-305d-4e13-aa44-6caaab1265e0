import time
import traceback
from bisect import bisect
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor
from datetime import datetime, timedelta
from math import ceil
from random import random, shuffle

from django.conf import settings
from django.core.management.base import BaseCommand
from django.db import connections

from central.settings.handlers import conf
from handlers.models import CentralRequestInfo
from handlers.on_hold.circulator.main_v2 import OnHoldCirculator_V2
from utills.helpers.helper import Helper


def weighted_choice(choices):
    values, weights = zip(*choices)
    total = 0
    cum_weights = []
    for w in weights:
        total += w
        cum_weights.append(total)
    x = random() * total
    i = bisect(cum_weights, x)
    return values[i]


def equal_distibution_of_db_node():
    return weighted_choice([("default", 30), ("onhold_circulation", 70)])


def run_on_hold_circulator():
    settings.API_LOGGER.tempinfo("on_hold_circulator - starting execution.")

    threshold = Helper.get_threshold(
        conf.ON_HOLD_ENTITY, conf.ON_HOLD_THRESHOLD
    )
    max_workers = Helper.get_max_workers(
        threshold[conf.ON_HOLD_MAX_WORKERS], "on_hold_circulator"
    )

    futures = []

    get_read_db = "default"
    if "onhold_circulation" in connections.databases:
        get_read_db = equal_distibution_of_db_node()  # onhold_circulation'

    # to avoid max db connections and utilize existing conn of model for default conn
    try:
        if get_read_db == "onhold_circulation":
            central_rq_model_dynamic_db_pointed = (
                CentralRequestInfo.objects.using(get_read_db)
            )
        else:
            central_rq_model_dynamic_db_pointed = CentralRequestInfo.objects
    except Exception:
        # incase read db failure
        central_rq_model_dynamic_db_pointed = CentralRequestInfo.objects
        pass

    settings.API_LOGGER.info(
        "on_hold_circulator- db pointed: {db}".format(db=get_read_db)
    )
    # central_req_objs = CentralRequestInfo.objects.using(get_read_db).filter(
    # 		is_onhold_req=CentralRequestInfo.ENTER,
    # 		is_req_completed=CentralRequestInfo.NO,
    # 		is_enable=CentralRequestInfo.YES,
    # 		added_on__gte=datetime.now() - timedelta(days=3),
    # 		added_on__lte=datetime.now()
    # 	).exclude(
    # 		onhold_udc_check_availibilty_cnt__gt=0,
    # 		updated_on__gt= datetime.now() - timedelta(seconds=int(threshold[conf.ON_HOLD_DUPLICATE_DELAY]))
    # 	)[:threshold[conf.ON_HOLD_CIRCULATION_REQ_PROCESS_COUNT]]

    central_req_objs_qs = central_rq_model_dynamic_db_pointed.filter(
        is_onhold_req=CentralRequestInfo.ENTER,
        is_req_completed=CentralRequestInfo.NO,
        is_enable=CentralRequestInfo.YES,
    )

    if not central_req_objs_qs.exists():
        log_msg = "on_hold_circulator - no new request found, exiting after sleeping for {sleep}s...".format(
            sleep=conf.ON_HOLD_CIRCULATOR_SLEEP_DUR
        )
        Helper.sampling_logger_emitter(log_msg)
        time.sleep(conf.ON_HOLD_CIRCULATOR_SLEEP_DUR)
        return

    ids_to_grep = list(
        central_req_objs_qs.values_list("request_id", flat=True)
    )

    if central_req_objs_qs.count() > 50:
        # shuffle only when large request in onhold circulator
        shuffle(ids_to_grep)
        ids_to_grep = ids_to_grep[
            : threshold[conf.ON_HOLD_CIRCULATION_REQ_PROCESS_COUNT]
        ]

    central_req_objs = central_rq_model_dynamic_db_pointed.filter(
        request_id__in=ids_to_grep,
        updated_on__lte=datetime.now()
        - timedelta(seconds=int(threshold[conf.ON_HOLD_DUPLICATE_DELAY])),
    )[: threshold[conf.ON_HOLD_CIRCULATION_REQ_PROCESS_COUNT]]

    # .order_by('onhold_udc_check_availibilty_cnt')
    if central_req_objs.count() < 1:
        log_msg = "on_hold_circulator - no new request found, exiting after sleeping for {sleep}s...".format(
            sleep=conf.ON_HOLD_CIRCULATOR_SLEEP_DUR
        )
        Helper.sampling_logger_emitter(log_msg)
        time.sleep(conf.ON_HOLD_CIRCULATOR_SLEEP_DUR)
        return

    chunk_size = ceil(central_req_objs.count() / max_workers)
    chunk_start = 0
    chunk_end = chunk_size

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for i in range(0, max_workers):
            obj = OnHoldCirculator_V2(threshold)
            futures.append(
                executor.submit(
                    obj.run, central_req_objs[chunk_start:chunk_end]
                )
            )

            chunk_start, chunk_end = chunk_end, chunk_end + chunk_size

    for f in futures:
        try:
            exp = f.result()
        except Exception:
            settings.API_LOGGER.error(
                "on_hold_circulator - error occured, e: {e}".format(
                    e="--".join(["Error:", str(traceback.format_exc())])
                )
            )

    settings.API_LOGGER.tempinfo("on_hold_circulator - ending execution.")


class Command(BaseCommand):
    help = "Run central service (pull from queue)"

    def handle(self, *args, **options):
        try:
            threshold = Helper.get_threshold(
                conf.CENTRAL_1_ENTITY, conf.CENTRAL_1_THRESHOLD
            )
            onhold_circulation_loop_count = threshold[
                conf.CENTRAL_1_LOOP_COUNT_KEY
            ]
        except Exception:
            onhold_circulation_loop_count = conf.CENTRAL_1_THRESHOLD[
                conf.CENTRAL_1_LOOP_COUNT_KEY
            ]

        for _ in range(0, int(onhold_circulation_loop_count)):
            run_on_hold_circulator()

        Helper.close_db_connections()
