from django.conf import settings
from django.core.management.base import BaseCommand

from handlers.group_ivfinfo_relation_handler.main_v2 import (
    GroupIvrinfoRelationHandler_V2 as Handler,
)


def run_group_ivrinfo_relation_handler():
    settings.API_LOGGER.tempinfo(
        "group_ivrinfo_relation_handler - starting execution."
    )
    Handler().run()
    settings.API_LOGGER.tempinfo(
        "group_ivrinfo_relation_handler - ending execution."
    )


class Command(BaseCommand):
    help = "Run Group Ivr Info Relation Handler"

    def handle(self, *args, **options):
        run_group_ivrinfo_relation_handler()
