# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import json
import sys
import threading
import traceback
from concurrent.futures import ThreadPoolExecutor, wait, as_completed,ProcessPoolExecutor

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

from handlers.models import Group
from utills.sqs_manager.main import SQSManager

def run():
	group_queue_urls = []

	groups = Group.objects.filter(is_enable='1').all()

	for group in groups:
		assigned_queues = group.get_assigned_queues
		for key, value in assigned_queues.items():
			url = value.get('url')
			if url: group_queue_urls.append(url)

	for url in group_queue_urls:
		requests = SQSManager.fetch_message_sp(url, 10, False)
		for req in requests:
			SQSManager.sqs_delete_message(url, req['ReceiptHandle'])


def run_gp_queue_messages_remover_handler():
	settings.API_LOGGER.tempinfo("group_queue_messages_remover - starting execution.")
	run()
	settings.API_LOGGER.tempinfo("group_queue_messages_remover - ending execution.")

class Command(BaseCommand):
	help = 'Run central service (pull from queue)'
	"""
	This handler is just for testing purposes, its work is to removed messages from all the group queues,
	so that we can test the flow of central.
	"""

	def handle(self, *args, **options):
		run_gp_queue_messages_remover_handler()