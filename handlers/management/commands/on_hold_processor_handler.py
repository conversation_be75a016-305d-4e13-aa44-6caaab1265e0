from django.conf import settings
from django.core.management.base import BaseCommand

from central.settings.handlers import conf
from handlers.on_hold.processor.main import OnHoldProcessor
from utills.helpers.helper import Helper


def run_on_hold_processor():
    settings.API_LOGGER.tempinfo("on_hold_processor - starting execution.")
    try:
        OnHoldProcessor.process()
    except Exception as e:
        settings.API_LOGGER.error(
            "on_hold_processor - ending execution.%s" % (e)
        )

    settings.API_LOGGER.tempinfo("on_hold_processor - ending execution.")


class Command(BaseCommand):
    help = "Run central service (pull from queue)"

    def handle(self, *args, **options):
        try:
            threshold = Helper.get_threshold(
                conf.CENTRAL_1_ENTITY, conf.CENTRAL_1_THRESHOLD
            )
            onhold_processor_loop_count = threshold[
                conf.CENTRAL_1_LOOP_COUNT_KEY
            ]
        except Exception:
            onhold_processor_loop_count = conf.CENTRAL_1_THRESHOLD[
                conf.CENTRAL_1_LOOP_COUNT_KEY
            ]

        for _ in range(0, int(onhold_processor_loop_count)):
            run_on_hold_processor()
