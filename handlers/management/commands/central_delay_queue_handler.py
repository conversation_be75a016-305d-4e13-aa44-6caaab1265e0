from django.conf import settings
from django.core.management.base import BaseCommand

from handlers.central_delay_queue.main import CentralDelayQueue
from utills.helpers.helper import Helper


def run_central_delay_queue_handler():
    settings.API_LOGGER.tempinfo("central_delay_queue - starting execution.")
    CentralDelayQueue.process()
    Helper.close_db_connections()
    settings.API_LOGGER.tempinfo("central_delay_queue - ending execution.")


class Command(BaseCommand):
    help = "Run central service (pull from queue)"

    def handle(self, *args, **options):
        run_central_delay_queue_handler()
