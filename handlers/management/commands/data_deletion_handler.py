from __future__ import unicode_literals

import json
import math
import time
import traceback
from datetime import timedelta

from django.conf import settings
from django.core.management.base import BaseCommand
from django.utils import timezone

from central.settings.handlers import conf
from handlers import models
from utills.helpers.helper import Helper


def get_threshold():
    threshold = Helper.get_threshold(
        conf.DATA_DELETION_ENTITY, conf.DATA_DELETION_THRESHOLD
    )  # Threshold of central_1 and PQs.

    for key in [
        conf.DATA_KEEP_DAYS,
        conf.DATA_DELETE_CHUNK_SIZE,
        conf.DATA_DELETE_SLEEP_DUR,
    ]:
        try:
            threshold[key] = int(threshold[key])
        except Exception as e:
            threshold[key] = conf.DATA_DELETION_THRESHOLD[key]
            settings.API_LOGGER.error(
                "data_deletion - invalid db threshold - key: {k}, e: {e}".format(
                    k=key, e=e
                )
            )

    try:
        if not isinstance(threshold[conf.DATA_DELETE_MODELS_LIST], list):
            threshold[conf.DATA_DELETE_MODELS_LIST] = json.loads(
                threshold[conf.DATA_DELETE_MODELS_LIST]
            )
    except Exception as e:
        threshold[conf.DATA_DELETE_MODELS_LIST] = conf.DATA_DELETION_THRESHOLD[
            conf.DATA_DELETE_MODELS_LIST
        ]
        settings.API_LOGGER.error(
            "data_deletion - model list not found in common_settings - taking default from conf. - {k}, e: {e}".format(
                k=conf.DATA_DELETION_THRESHOLD[conf.DATA_DELETE_MODELS_LIST],
                e=e,
            )
        )

    return threshold


def main():
    try:
        threshold = get_threshold()
        n_days_back_date = timezone.now() - timedelta(
            days=threshold[conf.DATA_KEEP_DAYS]
        )

        for model_name in threshold[conf.DATA_DELETE_MODELS_LIST]:
            model_instance = getattr(models, model_name)
            total_requests_count = model_instance.objects.filter(
                added_on__lt=n_days_back_date
            ).count()
            if total_requests_count > 0:
                chunks = math.ceil(
                    total_requests_count
                    / threshold[conf.DATA_DELETE_CHUNK_SIZE]
                )
                for _ in range(chunks):
                    requests = model_instance.objects.filter(
                        added_on__lt=n_days_back_date
                    )[: threshold[conf.DATA_DELETE_CHUNK_SIZE]]
                    for req in requests:
                        req.delete()
                    time.sleep(threshold[conf.DATA_DELETE_SLEEP_DUR])

    except Exception as e:
        settings.API_LOGGER.error(
            "--".join(
                [
                    "Error:",
                    str(e),
                    str(traceback.format_exc()),
                    str(traceback.extract_stack()),
                ]
            )
        )


class Command(BaseCommand):
    help = "Flush all data while keeping previous n months"

    def handle(self, *args, **options):
        main()
