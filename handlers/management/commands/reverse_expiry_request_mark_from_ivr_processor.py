from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand

from central.settings.handlers import conf
from handlers.models import CentralRequestInfo, OnHoldUserTracing
from utills.shared_cache_manager.main import SharedCacheManager


class ReverseExpiryRequestMarkFromIvrProcessorRunner:
    @classmethod
    def reverse_expiry_mark_from_ivr_processor(cls):
        try:
            # get requests canceled from IVR_Processor
            pattern_prefix = ":{version}:{key}".format(
                version=conf.REDIS_VERSION_KEY, key=conf.CANCELED_REQUEST_KEY
            )
            canceled_requests = SharedCacheManager.get_keys(
                pattern="{prefix}*".format(prefix=pattern_prefix)
            )
            canceled_request_ids = [
                req.split(pattern_prefix)[-1] for req in canceled_requests
            ]

            if canceled_request_ids:
                settings.API_LOGGER.info(
                    "expiry_manager - disabling canceled requests sent from IVR Processor - requets_id: {rids}".format(
                        rids=canceled_request_ids
                    )
                )

                central_req_info_obj = CentralRequestInfo.objects.filter(
                    request_id__in=canceled_request_ids
                )
                if central_req_info_obj.exists():
                    for central_req in central_req_info_obj:
                        central_req.is_req_completed = CentralRequestInfo.YES
                        central_req.is_onhold_req = CentralRequestInfo.NA
                        central_req.completion_event_type = (
                            CentralRequestInfo.FROM_IVR_PROCESSOR_FAILED
                        )
                        central_req.is_enable = CentralRequestInfo.NO
                        central_req.updated_on = datetime.now()
                        central_req.save(
                            update_fields=[
                                "is_req_completed",
                                "is_enable",
                                "completion_event_type",
                                "is_onhold_req",
                                "updated_on",
                            ]
                        )

            # delete processed canceled requests from shared_cache
            SharedCacheManager.delete_many_keys(canceled_requests)
        except Exception as e:
            settings.API_LOGGER.error(
                "reverse_expiry_request_mark_from_ivr_processor - requests_id: {e}".format(
                    e=e
                )
            )

    @classmethod
    def on_hold_user_tracing_patch(cls):
        # patch-checker if all user request got stopped
        try:
            objs = OnHoldUserTracing.objects.all().values_list(
                "user_id", "ivr_id"
            )
            for i in objs:
                cri_objs = CentralRequestInfo.objects.filter(
                    ivr_id=i[1],
                    is_req_completed=0,
                    is_enable=1,
                    is_onhold_req=1,
                    user_id=i[0],
                )
                # print(cri_objs.count())
                ob = OnHoldUserTracing.objects.filter(
                    user_id=i[0], ivr_id=i[1]
                )
                if ob.exists():
                    ob = ob.first()
                    if cri_objs.count() == 0 and ob.count_disabled_req >= 2:
                        # settings.API_LOGGER.error("[ERROR] no request is enabled for this user_id, ivr_id: {rid}-cnt:{rid1}".format(rid=i, rid1=cri_objs.count()))
                        obj = CentralRequestInfo.objects.filter(
                            ivr_id=i[1],
                            is_req_completed=0,
                            is_enable=0,
                            is_onhold_req=1,
                            user_id=i[0],
                        )
                        if obj.exists():
                            settings.API_LOGGER.info(
                                "[ERROR] no request is enabled for this user_id, ivr_id: {rid}-cnt:{rid1}".format(
                                    rid=i, rid1=cri_objs.count()
                                )
                            )
                            obj = obj.first()
                            CentralRequestInfo.objects.filter(
                                id=obj.id
                            ).invalidated_update(is_enable=1)
        except Exception as e:
            # settings.API_LOGGER.error("error-usertracing checker")
            settings.API_LOGGER.error(
                "error-usertracing checker {rid}".format(rid=e)
            )


class Command(BaseCommand):
    help = "reverse_expiry_request_mark_from_ivr_processor"

    def handle(self, *args, **options):
        ReverseExpiryRequestMarkFromIvrProcessorRunner.reverse_expiry_mark_from_ivr_processor()
        ReverseExpiryRequestMarkFromIvrProcessorRunner.on_hold_user_tracing_patch()
