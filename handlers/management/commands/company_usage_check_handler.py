# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import json
import sys
import threading
import traceback
from concurrent.futures import ThreadPoolExecutor, wait, as_completed,ProcessPoolExecutor

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

from handlers.company_usage_check.main import CompanyUsageCheck

def run_usage_check():
	settings.API_LOGGER.tempinfo("company_usage_check - starting execution.")
	CompanyUsageCheck.run()
	settings.API_LOGGER.tempinfo("company_usage_check - ending execution.")


class Command(BaseCommand):
	help = 'Run Company Usage Check service (pull from sns)'

	def handle(self, *args, **options):
		run_usage_check()
