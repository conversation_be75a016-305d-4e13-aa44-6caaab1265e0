# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

from handlers.cache_consistency_checker.main import CacheConsistency<PERSON>he<PERSON>


def run_cache_consistency_checker():
	CacheConsistencyChecker().run()


class Command(BaseCommand):
	help = 'Run central service (pull from queue)'

	def handle(self, *args, **options):
		run_cache_consistency_checker()