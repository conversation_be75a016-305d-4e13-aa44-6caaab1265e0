import traceback
from concurrent.futures import Thread<PERSON>oolExecutor

from django.conf import settings
from django.core.management.base import BaseCommand

from central.settings.handlers import conf
from handlers.response_manager.main import ResponseManager
from utills.helpers.helper import Helper


def run_response_manager():
    settings.API_LOGGER.tempinfo("response_manager - starting execution.")

    threshold = Helper.get_threshold(
        conf.RESPONSE_MANAGER_ENTITY, conf.RESPONSE_MANAGER_THRESHOLD
    )
    max_workers = Helper.get_max_workers(
        threshold[conf.RESPONSE_MANAGER_MAX_WORKERS], "response_manager"
    )

    futures = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for i in range(0, max_workers):
            futures.append(executor.submit(ResponseManager.run))

    for f in futures:
        try:
            exp = f.result()
        except Exception:
            settings.API_LOGGER.error(
                "response_manager - error occured, e: {e}".format(
                    e="--".join(["Error:", str(traceback.format_exc())])
                )
            )

    settings.API_LOGGER.tempinfo("response_manager - ending execution.")


class Command(BaseCommand):
    help = "Run central service (pull from queue)"

    def handle(self, *args, **options):
        run_response_manager()
