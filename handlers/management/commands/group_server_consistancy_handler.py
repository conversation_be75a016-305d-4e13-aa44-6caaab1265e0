from django.conf import settings
from django.core.management.base import BaseCommand

from handlers.group_server_consistancy_manager.main_v2 import (
    GroupServerConsistancyHandler_V2,
)


def run_group_server_consistancy_handler():
    settings.API_LOGGER.tempinfo(
        "group_server_consistancy_manager - starting execution."
    )
    GroupServerConsistancyHandler_V2.run()
    settings.API_LOGGER.tempinfo(
        "group_server_consistancy_manager - ending execution."
    )


class Command(BaseCommand):
    help = "Run central service (pull from queue)"

    def handle(self, *args, **options):
        run_group_server_consistancy_handler()
