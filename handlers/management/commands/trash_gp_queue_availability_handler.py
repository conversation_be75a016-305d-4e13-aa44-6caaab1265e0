# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import json
import sys
import threading
import traceback
from concurrent.futures import ThreadPoolExecutor, wait, as_completed,ProcessPoolExecutor

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

from handlers.gp_queue_availability.main import GPQueueAvailabilityChecker

def run_gp_queue_availability_handler():
	settings.API_LOGGER.tempinfo("group_queue_availability - starting execution.")
	GPQueueAvailabilityChecker.process()
	settings.API_LOGGER.tempinfo("group_queue_availability - ending execution.")

class Command(BaseCommand):
	help = 'Run central service (pull from queue)'


	def handle(self, *args, **options):
	    run_gp_queue_availability_handler()