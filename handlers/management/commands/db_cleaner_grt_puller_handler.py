from django.core.management.base import BaseCommand
from django.conf import settings

from handlers.db_cleaner.grt_puller.main import GRT<PERSON>uller


def run_grt_puller():
	settings.API_LOGGER.tempinfo("GRTPuller - starting execution.")

	GRTPuller().run()

	settings.API_LOGGER.tempinfo("GRTPuller - ending execution.")


class Command(BaseCommand):
	help = 'Run central service (pull from queue)'

	def handle(self, *args, **options):
		run_grt_puller()