from django.core.management.base import BaseCommand
from django.conf import settings

from handlers.db_cleaner.s3_pusher.main import S3Pusher


def run_s3_pusher():
	settings.API_LOGGER.tempinfo("S3Pusher - starting execution.")

	S3Pusher().run()

	settings.API_LOGGER.tempinfo("S3Pusher - ending execution.")


class Command(BaseCommand):
	help = 'Run central service (pull from queue)'

	def handle(self, *args, **options):
		run_s3_pusher()