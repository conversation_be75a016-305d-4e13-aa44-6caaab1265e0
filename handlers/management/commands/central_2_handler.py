import time
import traceback
from concurrent.futures import ThreadPoolExecutor

from django.conf import settings
from django.core.management.base import BaseCommand

from central.settings.handlers import conf
from handlers.central_2.helper import Central2_Helper
from handlers.central_2.main_v2 import Central2_V2
from utills.helpers.helper import Helper


def run_central2():
    """Creates threads and calls central_2 handler"""

    # checking cache keys
    if not Central2_Helper.is_cache_keys_exists():
        log_msg = "central_2 - PQs cache key not found, exiting after sleeping for '{sleep}'s...".format(
            sleep=conf.CENTRAL_2_SLEEP_DUR
        )
        Helper.sampling_logger_emitter(log_msg)
        time.sleep(conf.CENTRAL_2_SLEEP_DUR)
        return

    # creating threads
    max_workers, c2_thread_threshold = Central2_Helper.get_thresholds()

    futures = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        for i in range(0, max_workers):
            futures.append(
                executor.submit(Central2_V2().run, (c2_thread_threshold,))
            )

    for f in futures:
        try:
            exp = f.result()
        except Exception:
            settings.API_LOGGER.error(
                "central_2 - error occurred, e: {e}".format(
                    e="--".join(["Error:", str(traceback.format_exc())])
                )
            )


class Command(BaseCommand):
    help = "Run central service (pull from queue)"

    def handle(self, *args, **options):
        try:
            threshold = Helper.get_threshold(
                conf.CENTRAL_1_ENTITY, conf.CENTRAL_1_THRESHOLD
            )
            central_2_loop_count = threshold[conf.CENTRAL_1_LOOP_COUNT_KEY]
        except Exception:
            central_2_loop_count = conf.CENTRAL_1_THRESHOLD[
                conf.CENTRAL_1_LOOP_COUNT_KEY
            ]

        settings.API_LOGGER.tempinfo("central_2 - starting execution.")
        for _ in range(0, int(central_2_loop_count)):
            run_central2()
        settings.API_LOGGER.tempinfo("central_2 - ending execution.")
