import logging
from functools import partial
from typing import Dict, Union

from django.conf import settings

from central.logging import logging_cri_context, reset_uid
from handlers.central_2.main import Central2
from handlers.models import CentralRequestInfo
from utills.cache_manager.main import CacheManager
from utills.exceptions import GroupUnavailableException
from utills.helpers.pm_helpers import ProcessManagerHelper
from utills.request_handler import <PERSON>quest<PERSON>and<PERSON>
from utills.shared_cache_manager.main import SharedCacheManager
from utills.sqs_manager.main import SQSManager
from utills.sqs_manager.sqs_event import CallRequestSqsEvent
from utills.udc_helper import UDCHelper

logger = logging.getLogger(__name__)


class Central2_V2(Central2):
    def __init__(self):
        super().__init__()
        self.request_handler = RequestHandler()
        self.pm_helper = ProcessManagerHelper()

    def get_cri_obj(self, request_id: str):
        cri_obj = CentralRequestInfo.objects.filter(
            request_id=request_id
        ).last()
        if not cri_obj:
            logger.error(
                f"central_2 - deleting sqs event with request_id: {request_id}, CentralRequestInfo entry not found"
            )
            return None

        return cri_obj

    def process_request(
        self, raw_sqs_event: Dict, queue_url: str, queue_cache_key: str
    ):
        reset_uid()  # To keep a separate trace-id per request

        sqs_event = CallRequestSqsEvent(
            raw_sqs_event, queue_url, queue_cache_key
        )
        logger.info(f"central_2 - request received: {sqs_event.body}")

        cri_obj: CentralRequestInfo = self.get_cri_obj(sqs_event.request_id)
        if not cri_obj:
            self.delete_request(sqs_event)
            return

        with logging_cri_context(cri_obj):
            self.request_handler.setup(cri_obj)
            try:
                self.request_handler.process(
                    partial(self.on_udc_unavailable, sqs_event),
                    partial(self.on_destination_success, sqs_event),
                    partial(self.on_destination_failure, sqs_event),
                    on_retry=partial(
                        self.on_udc_unavailable, sqs_event
                    ),  # send to hold if the destination fails!!
                )
            except GroupUnavailableException as error:
                if error.cancel_request:
                    self.cancle_request(
                        sqs_event.event,
                        sqs_event.body,
                        sqs_event.queue_url,
                        sqs_event.queue_cache_key,
                    )

    def on_udc_unavailable(
        self, sqs_event: CallRequestSqsEvent, cri_obj: CentralRequestInfo
    ):
        if not self.send_to_hold(
            sqs_event,
            cri_obj,
        ):
            logger.info("Cancelling request reason:- UDC unavailable")
            self.cancle_request(
                sqs_event.event,
                sqs_event.body,
                sqs_event.queue_url,
                sqs_event.queue_cache_key,
            )

    def on_destination_success(
        self,
        sqs_event: CallRequestSqsEvent,
        udc_helper: UDCHelper,
        cri_obj: CentralRequestInfo,
        response: Union[Dict, str],
    ):
        udc_helper.set_request_in_cache()
        self.delete_request(sqs_event)
        self.pm_helper.invoke_process_manager_with_api_response(
            cri_obj, response
        )

    def on_destination_failure(
        self,
        sqs_event: CallRequestSqsEvent,
        cri_obj: CentralRequestInfo,
        response: Union[Dict, str],
        status_code: str,
    ):
        # add entry in shared_cache
        canceled_request_key = "{version}{prefix}{key}".format(
            version=settings.REDIS_VERSION_KEY,
            prefix=settings.CANCELED_REQUEST_KEY,
            key=cri_obj.request_id,
        )
        SharedCacheManager.set_value(
            canceled_request_key, 1, settings.CANCELED_REQUESTS_KEY_TTL
        )

        self.delete_request(sqs_event)
        self.pm_helper.invoke_process_manager_with_api_response(
            cri_obj, response, status_code, success=False
        )

    def send_to_hold(
        self,
        sqs_event: CallRequestSqsEvent,
        cri_obj: CentralRequestInfo,
    ) -> bool:
        call_hold = self.check_to_send_request_to_hold(cri_obj.body)
        if call_hold:
            self.send_request_to_on_hold(
                sqs_event.event,
                sqs_event.body,
                sqs_event.queue_url,
                sqs_event.queue_cache_key,
            )
        return call_hold

    def delete_request(self, sqs_event: CallRequestSqsEvent):
        logger.info(
            f"Deleting event: {sqs_event.receipt_handle} from SQS: {sqs_event.queue_url}"
        )
        SQSManager.sqs_delete_message(
            sqs_event.queue_url, sqs_event.receipt_handle
        )
        logger.info(
            f"Decrementing cache for cache_key:{sqs_event.queue_cache_key}"
        )
        CacheManager.decr_value(sqs_event.queue_cache_key, True)
