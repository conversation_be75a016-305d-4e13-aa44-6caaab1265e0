from math import ceil

from django.conf import settings

from central.settings.handlers import conf
from utills.cache_manager.main import CacheManager
from utills.helpers.helper import Helper


class Central2_Helper:
    @staticmethod
    def is_cache_keys_exists():
        """
        Responsibilities:
                Checks if all PQs cache keys are missing or not.
        Returns:
                bool
        """
        for q_name in [conf.CENTRAL_PQ1, conf.CENTRAL_PQ2, conf.CENTRAL_PQ3]:
            msg_count = CacheManager.get_value(
                conf.CENTRAL_QUEUES_COUNT_KEYS.get(q_name)
            )
            if msg_count:
                break
        else:  # forelse
            return False

        return True

    @staticmethod
    def get_thresholds():
        """
        Responsibilities:
                1. Get common_settings for of central_1, and central_2 from DB.
                2. In case common_settings is incorrect then, raises alert and uses the default from conf.
                3. Calculate threshold for each threads (pq1+pq2+pq3 / m), m=number of threads
        Returns:
                tuple: max_workers, c2_thread_threshold
        """
        c1_common_settings = Helper.get_threshold(
            conf.CENTRAL_1_ENTITY, conf.CENTRAL_1_THRESHOLD
        )
        c2_common_settings = Helper.get_threshold(
            conf.CENTRAL_2_ENTITY, conf.CENTRAL_2_THRESHOLD
        )

        # Just Extra Validation, in case someone puts wrong settings from admin panel.
        try:
            pq1_threshold = int(c1_common_settings[conf.CENTRAL_PQ1])
            pq2_threshold = int(c1_common_settings[conf.CENTRAL_PQ2])
            pq3_threshold = int(c1_common_settings[conf.CENTRAL_PQ3])
        except Exception as e:
            pq1_threshold = conf.CENTRAL_1_THRESHOLD[conf.CENTRAL_PQ1]
            pq2_threshold = conf.CENTRAL_1_THRESHOLD[conf.CENTRAL_PQ2]
            pq3_threshold = conf.CENTRAL_1_THRESHOLD[conf.CENTRAL_PQ3]
            settings.API_LOGGER.error(
                "central_2 - invalid db thresholds: {t}, **taking default from conf**, e: {e}".format(
                    t=c1_common_settings, e=e
                )
            )

        try:
            max_workers = Helper.get_max_workers(
                c2_common_settings[conf.CENTRAL_2_MAX_WORKERS], "central_2"
            )
        except Exception as e:
            max_workers = conf.CENTRAL_1_THRESHOLD[conf.CENTRAL_2_MAX_WORKERS]
            settings.API_LOGGER.error(
                "central_2 - invalid max_workers thresholds: {t}, **taking default from conf**, e: {e}".format(
                    t=c2_common_settings, e=e
                )
            )

        c2_thread_threshold = ceil(
            (pq1_threshold + pq2_threshold + pq3_threshold) / max_workers
        )
        return max_workers, c2_thread_threshold
