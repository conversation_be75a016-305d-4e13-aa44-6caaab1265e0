import json
from datetime import datetime
from typing import Dict

from django.conf import settings

from central.settings.handlers import conf
from handlers.models import CentralRequestInfo
from utills.cache_manager.main import CacheManager
from utills.exceptions import UDCLockedException, UDCUnavailable
from utills.group_queues_pusher.main import GroupQueuesPusher
from utills.group_queues_routing_manager.main import GroupQueuesRoutingManager
from utills.helpers.helper import Helper
from utills.sqs_manager.main import SQSManager
from utills.udc_helper import UDCHelper


class Central2:
    def __init__(self):
        self.cq_name_urls = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )
        self.ohq_name_urls = Helper.get_queue_name_url_mapping(
            "queue_name", conf.ON_HOLD_Q_NAME, conf.ON_HOLD_Q_GATEWAY_PREFIX
        )
        self.ohq_url = self.ohq_name_urls[conf.ON_HOLD_Q_NAME]
        self.central_queues = [conf.CENTRAL_PQ2, conf.CENTRAL_PQ3]
        self.udc_helper: UDCHelper = None

    def set_udc_helper(self, instance: CentralRequestInfo):
        self.udc_helper = UDCHelper(instance)

    def run(self, args):
        """
        Responsibilities:
            1. Process Central Priority Queues, starting from PQ1-PQ2-PQ3
            2. UDC availability check for each request
            3. If UDC is available then based on assigned servers push to Group
                Queue
            4. If UDC is not available then push request to ON_HOLD_Q
            5. Delete from req Central Priority Queue
            6. In case of any availability check failure ignore the request,
                it would to picked again from PQs in later executions
        """
        c2_threshold = args[0]

        for q_name in self.central_queues:
            if c2_threshold < 1:
                settings.API_LOGGER.info(
                    "central_2 - handler threshold has exhausted, stopping the process..."
                )
                break
            c2_threshold = self.process_queue(
                q_name,
                conf.CENTRAL_QUEUES_COUNT_KEYS.get(q_name),
                c2_threshold,
            )

    def process_queue(self, q_name, q_key, c2_threshold):
        """
        Responsibilities:
            1. Queue Pulling based on cache msg count and the current
                threshold of central_2
        Parameters:
            q_name: priority_queue_name
            q_key:  cache key
        Returns:
            False
        """

        msg_count = CacheManager.get_value(q_key)

        if not msg_count:
            settings.API_LOGGER.tempinfo(
                "central_2 - cache key not found, for pq: {q_name}, c2_threshold: {t}".format(
                    q_name=q_name, t=c2_threshold
                )
            )
            return c2_threshold

        settings.API_LOGGER.tempinfo(
            "central_2 - processing pq: '{q}', central_2 available c2_threshold: {t}".format(
                q=q_name, t=c2_threshold
            )
        )

        while msg_count > 0:
            if c2_threshold < 1:
                break

            limit = 10
            if c2_threshold < limit:
                limit = c2_threshold

            requests = SQSManager.fetch_message_sp(
                self.cq_name_urls[q_name], limit, False
            )

            if not requests:
                break

            for req in requests:
                c2_threshold = c2_threshold - 1
                self.process_request(req, self.cq_name_urls[q_name], q_key)

            msg_count -= len(requests)

        return c2_threshold

    def check_to_send_request_to_hold(self, req_body: Dict) -> bool:
        """
        Determines the call hold status based on the `call_hold` value in the request body.

        If `call_hold` is explicitly True, returns True (the call will go on hold).
        If `call_hold` is explicitly False, returns False (the call will be canceled).
        For any other value, returns True (default to holding the call).
        """
        call_hold = req_body.get("call_hold", True) is not False
        settings.API_LOGGER.info(
            f"central_2 - request_id: {req_body['request_id']}, ivr_id: {req_body['ivr_id']}, call_hold: {call_hold}"
        )
        return call_hold

    def process_request(self, req, pq_url, q_key):
        """
        Algo:
            1. Get IVR Group
            2. If no group found after applying all filters mark cancel request
        Parameters:
            req: req_body of sqs message
            pq_url: pq_url to delete msg from pq.
            q_key: cache key
        Returns:
            False
        """
        body = json.loads(req["Body"])
        group = GroupQueuesRoutingManager().assign_group(body)

        if not group:
            settings.API_LOGGER.info(
                "central_2 - request_id: {request_id}, ivr_id: {ivr_id}, ignoring requests because of group unavailability!".format(
                    ivr_id=body["ivr_id"], request_id=body["request_id"]
                )
            )
            return

        if "is_cancle_request" in group:
            settings.API_LOGGER.info(
                "central_2 - request_id: {request_id}, ivr_id: {ivr_id}, canceling request because of group not found!".format(
                    ivr_id=body["ivr_id"], request_id=body["request_id"]
                )
            )
            self.cancle_request(req, body, pq_url, q_key)
            return

        self.process_group(req, body, pq_url, group, q_key)

    def process_group(self, req, body, pq_url, group, q_key):
        """
        Algo:
            1. If the UDC check is positive push to group queues.
            2. If the UDC check is negative push to on_hold_q.
            3. Dec pq cache count and increase group cache key count in
                shared cache or on-hold cache key count in the central cache.
            4. If some error occurred in UDC Check then ignoring the request,
                it will be picked again from
            priority queue after some time.
        Parameters:
            req: req_body of sqs message
            pq_url: pq_url to delete msg from pq.
            group: group to process
            q_key: cache key
        Returns:
            False
        """
        central_req_info_obj = CentralRequestInfo.objects.filter(
            request_id=body["request_id"]
        )
        if not central_req_info_obj.exists():
            SQSManager.sqs_delete_message(pq_url, req["ReceiptHandle"])
            CacheManager.decr_value(q_key, True)
            settings.API_LOGGER.error(
                "central_2 - deleting body request_id: {req_id}-{bdy}, CentralRequestInfo entry not found".format(
                    req_id=body["request_id"], bdy=body
                )
            )
            return
        instance = central_req_info_obj.last()

        toggle_view_only = Helper.get_toggle_view_only_value(body["ivr_id"])
        try:
            self.set_udc_helper(instance)
            self.udc_helper.udc_checks(user_view_only=toggle_view_only)
        except UDCUnavailable:
            call_hold = self.check_to_send_request_to_hold(body)
            if call_hold:
                self.send_request_to_on_hold(req, body, pq_url, q_key)
            else:
                settings.API_LOGGER.info(
                    f"central_2 - cancelling request reason:- UDC unavailable, request_id: {body['request_id']}, ivr_id: {body['ivr_id']}, call_hold: {call_hold}"
                )
                self.cancle_request(req, body, pq_url, q_key)
            return

        except UDCLockedException as error:
            settings.API_LOGGER.info(
                "central_2 - request_id: {req_id}, ivr_id: {ivr_id}, message: {m}".format(
                    req_id=body["request_id"],
                    ivr_id=body["ivr_id"],
                    m=error.response.get("message"),
                ),
                extra={"status_code": error.response.get("status")},
            )
            return

        GroupQueuesPusher().push_to_group_queue(
            "central_2",
            body,
            group,
            self.udc_helper,
            q_key,
            pq_url,
            req["ReceiptHandle"],
        )

    def send_request_to_on_hold(self, request, req_body, queue_url, queue_key):
        settings.API_LOGGER.info(
            "central_2 - request_id: {req_id}, UDC unavailable sending to OHQ".format(
                req_id=req_body["request_id"]
            )
        )
        SQSManager.send_message_to_sqs(self.ohq_url, request["Body"])
        CacheManager.incr_value(conf.ON_HOLD_COUNT_KEY)
        settings.API_LOGGER.info(
            "central_2 - request_id: {req_id}, deleting from priority_queue: {pq}".format(
                pq=queue_url, req_id=req_body["request_id"]
            )
        )
        SQSManager.sqs_delete_message(queue_url, request["ReceiptHandle"])
        CacheManager.decr_value(queue_key, True)

    def cancle_request(self, req, body, pq_url, q_key):
        """
        Algo:
            1. Mark complete request in DB.
            2. Invoke process manager
            3. Delete request from PQs, and decrease cache key count.
        Parameters:
            req: req_body of sqs message
            pq_url: pq_url to delete msg from pq.
            group: group to process
            q_key: cache key
        Returns:
            False
        """
        central_req_info_obj = CentralRequestInfo.objects.filter(
            request_id=body["request_id"]
        )
        if central_req_info_obj.exists():
            central_req_info_obj.invalidated_update(
                is_req_completed=CentralRequestInfo.YES,
                is_enable=CentralRequestInfo.NO,
                updated_on=datetime.now(),
            )
            pm_response = Helper.make_pm_response(
                "call", "obd_central group not found!", ""
            )
            Helper.invoke_process_manager(
                body["request_id"],
                body["company_id"],
                body["ivr_id"],
                pm_response,
                "404",
                conf.OBD_SERVICES["cancel_service"],
            )
        else:
            settings.API_LOGGER.error(
                "central_2 - request_id: {req_id}, CentralRequestInfo data not found!".format(
                    req_id=body["request_id"]
                )
            )

        settings.API_LOGGER.info(
            "central_2 - request_id: {req_id}, deleting from priority_queue, after cancelation, pq: {pq}".format(
                pq=pq_url, req_id=body["request_id"]
            )
        )
        SQSManager.sqs_delete_message(pq_url, req["ReceiptHandle"])
        CacheManager.decr_value(q_key, True)
