import json
import logging
import time
from typing import Dict, Optional

from django.conf import settings
from django.db import transaction
from django.utils import timezone

from central.logging import reset_uid
from handlers.group_ivfinfo_relation_handler.main import (
    GroupIvrinfoRelationHandler,
)
from handlers.models import Group, GroupIvrInfoRelationship, IvrInfo
from utills.cache_manager.main import CacheManager
from utills.exceptions import DIDSyncException
from utills.external_apis import ExternalAPIs
from utills.group_server_relation_manager.main_v2 import (
    GroupServerRelationManager_V2,
)
from utills.sqs_manager.main import SQSManager
from utills.sqs_manager.sqs_event import FixDidSqsEvent

logger = logging.getLogger(__name__)


class GroupIvrinfoRelationHandler_V2(GroupIvrinfoRelationHandler):
    TOTAL_LOOPS = settings.GROUP_IVRINFO_LOOP_COUNT

    def __init__(self):
        self.actions = settings.GROUP_IVR_RELATION_SQS_EVENT_ACTIONS
        self.data_fields = settings.GROUP_IVR_RELATION_SQS_DATA_FIELDS
        self.queue_url = self.get_queue_url()

    def cache_key_exists(self) -> bool:
        msg_count = CacheManager.get_value(
            settings.GROUP_IVRINFO_RELETION_COUNT_KEY
        )
        if msg_count:
            return True
        return False

    def run(self):
        if not self.cache_key_exists():
            logger.debug(
                f"group_ivrinfo_relation_handler - cache count key not found, sleeping for {settings.GROUP_IVRINFO_SLEEP_DUR}"
            )
            time.sleep(settings.GROUP_IVRINFO_SLEEP_DUR)
            return

        for _ in range(self.TOTAL_LOOPS):
            requests = SQSManager.fetch_message_sp(self.queue_url, 10, False)
            if not requests:
                log_msg = "group_ivrinfo_relation_handler - no new requests found in sqs, exiting"
                logger.debug(log_msg)
                break
            for event in requests:
                self.process_event(event)

    def process_event(self, event: Dict):
        reset_uid()
        try:
            sqs_event = FixDidSqsEvent(event)

            if self.event_is_deletable(sqs_event):
                logger.info(
                    f"Event is deletable, Deleting this event, body:{sqs_event.body} "
                )
                self.delete_request_from_sqs_and_decr_cache(sqs_event)
                return

        except (KeyError, TypeError, json.JSONDecodeError) as error:
            logger.error(
                f"Error: {error}, deleting event: {event}", exc_info=True
            )
            self.delete_request(event["ReceiptHandle"])
            raise error

        logger.info(
            f"group_ivrinfo_relation_handler - event body: {sqs_event.body}"
        )
        if sqs_event.is_map_event():
            self.map_dids(sqs_event)
        elif sqs_event.is_unmap_event():
            self.unmap_dids(sqs_event)
        else:
            logger.error(
                f"group_ivrinfo_relation_handler - request event action didn't match with any valid action, request_body:{sqs_event.body}, deleting request!!"
            )
            self.delete_request_from_sqs_and_decr_cache(sqs_event)

    def event_is_deletable(self, sqs_event: FixDidSqsEvent) -> bool:
        if (
            not sqs_event.data
            or self.is_discardable(sqs_event.body)
            or (not self.is_valid_data(sqs_event.data))
        ):
            return True
        return False

    def map_dids(self, sqs_event: FixDidSqsEvent):
        is_deletable = True
        logger.info(
            f"group_ivrinfo_relation_handler - inside map_dids, {sqs_event}"
        )
        for did in sqs_event.fix_dids:
            try:
                did_response = (
                    self.get_validated_did_detail_response_to_map_did(
                        sqs_event, did
                    )
                )

                self.map_did(sqs_event.company_id, did_response)

            except DIDSyncException as error:
                if error.log_exception:
                    logger.error(
                        f"DIDSyncException:- message: {error.message} for {did} with is_deletable: {error.is_deletable}",
                        exc_info=True,
                    )
                is_deletable = error.is_deletable

        if is_deletable:
            self.delete_request_from_sqs_and_decr_cache(sqs_event)

    def map_did(self, company_id: str, did_details_response: Dict):
        group = self.get_group_obj(did_details_response)
        if not group:
            raise DIDSyncException("unable to get group", is_deletable=False)
        number_obj = self.get_number_obj(did_details_response, group.pk)

        for ivr in did_details_response["ivrs"]:
            ivrinfo_obj = IvrInfo.objects.filter(
                ivr_id=ivr["id"], c_id=company_id
            ).first()

            if not ivrinfo_obj:
                raise DIDSyncException(
                    f"ivr: {ivr} not found in DB!!", is_deletable=True
                )

            with transaction.atomic():
                (
                    mapping_obj,
                    created,
                ) = GroupIvrInfoRelationship.objects.get_or_create(
                    group_id=group.pk,
                    ivr_info_id=ivrinfo_obj.pk,
                    number_id=number_obj.pk,
                )
                if created:
                    logger.info(
                        f"group_ivrinfo_relation_handler - creating mapping, ivrinfo_id: {ivrinfo_obj.pk}, group_id:{group.pk}, number_id: {number_obj.pk}"
                    )
                else:
                    logger.info(
                        f"group_ivrinfo_relation_handler - mapping already exists: {mapping_obj.pk}"
                    )

    def unmap_dids(self, sqs_event: FixDidSqsEvent):
        return super().unmap_dids(
            sqs_event.company_id,
            sqs_event.fix_dids,
            sqs_event.receipt_handle,
            sqs_event.event,
        )

    def get_validated_did_detail_response_to_map_did(
        self, sqs_event: FixDidSqsEvent, did: str
    ):
        did_details_response = self.get_did_details(sqs_event.company_id, did)
        if not did_details_response:
            raise DIDSyncException(is_deletable=True, log_exception=False)

        if self.check_common_cases_to_skip(
            did_details_response,
            sqs_event.company_id,
            did,
            sqs_event.event,
        ):
            raise DIDSyncException(is_deletable=True, log_exception=False)

        if not did_details_response.get("ivrs"):
            raise DIDSyncException(
                "no ivr mapped", is_deletable=True, log_exception=False
            )
        if not did_details_response.get("kam_group_id"):
            raise DIDSyncException(
                "kam_group_id not found in response!!", is_deletable=True
            )
        if not did_details_response.get("group_priority"):
            raise DIDSyncException(
                "group_priority not found in response!!", is_deletable=True
            )
        if not did_details_response.get("pilot_number"):
            raise DIDSyncException(
                "pilot_number not found in response!!",
                is_deletable=True,
            )

        return did_details_response

    def get_did_details(self, company_id: str, fix_did: str) -> Optional[Dict]:
        data, _ = ExternalAPIs.talk_to_retrieve_did_api(company_id, fix_did)
        if data:
            return data
        else:
            return None

    def get_group_data_from_kamailio(
        self, kam_group_id: str
    ) -> Optional[Dict]:
        kam_cache_key = settings.KAM_GROUP_SERVER_CACHE_KEY
        kam_api_name = settings.ROUTING_INFO_URL_NAMES[
            "kam_group_servers_route_name"
        ]
        payload = {
            "token": settings.SECRET_TOKENS["kam_group_servers_token"],
            "is_obd": 1,
            "kam_group_id": kam_group_id,
        }

        group_data = ExternalAPIs.talk_to_kamailio_api(
            kam_cache_key, kam_api_name, **payload
        )
        if not group_data:
            logger.error(
                "group_ivrinfo_relation_handler - unable to get kam_data for kam_group_id: {kam_group_id}".format(
                    kam_group_id=kam_group_id
                )
            )
            return None
        return group_data

    def get_group_obj(self, did_details_response: Dict) -> Optional[Group]:
        kam_group_id = did_details_response["kam_group_id"]
        group_filter_obj = Group.objects.filter(kam_group_id=kam_group_id)
        if not group_filter_obj.exists():
            group_data = self.get_group_data_from_kamailio(kam_group_id)
            if not group_data:
                return

            GroupServerRelationManager_V2.add_group_servers(
                kam_group_id,
                group_data[kam_group_id],
                did_details_response["pilot_number"],
            )
            # updating extra data of group.
            Group.objects.filter(kam_group_id=kam_group_id).invalidated_update(
                group_priority=did_details_response["group_priority"],
                updated_on=timezone.now(),
            )

        group_obj = group_filter_obj.first()
        return group_obj

    def delete_request_from_sqs_and_decr_cache(
        self, sqs_event: FixDidSqsEvent
    ):
        logger.info(
            f"Deleting event with receipt_handle: {sqs_event.receipt_handle} from sqs"
        )
        return self.delete_request(sqs_event.receipt_handle)
