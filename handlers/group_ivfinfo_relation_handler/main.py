import json
import time
from datetime import datetime

from django.conf import settings
from django.db import transaction

from central.logging import reset_uid
from central.settings.handlers import conf
from handlers.models import Group, GroupIvrInfoRelationship, IvrInfo, Number
from utills.cache_manager.main import CacheManager
from utills.external_apis import ExternalAPIs
from utills.group_server_relation_manager.main import (
    GroupServerRelationManager,
)
from utills.helpers.helper import Helper
from utills.sqs_manager.main import SQSManager


class GroupIvrinfoRelationHandler:
    """
    This handler is responsible for managing the relationship between IVRs, Groups, and Numbers.
    Flow:
                    1. Run only when cache count is found, else sleep and exit.
                    2. SQS Pulling, if no request found, stop the execution.
                    3. Discard Requests if action is present in discard actions.
                    4. If map action then calls fixdid API to get IVRs and group for given fixdid.
                                    4.1 If the group doesn't exist then hit Kamailio API, and create the group and corresponding servers.
                                    4.2 If Number does not exist create a new one.
                                    4.3 Validate IVR.
                                    4.4. Create Relation between group, ivrinfo and number.
                    5. If unmap action - then call fixdid API to get IVRs and group for given fixdid.
                                    5.1 If no data found then delete all relation of ivrinfo, group and numbers for given fixdid and company_id.
                                    5.2 If data found then ignore those IVRs returned by fixdid APIs and delete other relations.
    """

    def __init__(self):
        if not self.is_cache_key_exists():
            return  # return after sleep

        self.actions = conf.GROUP_IVR_RELATION_SQS_EVENT_ACTIONS
        self.data_fields = conf.GROUP_IVR_RELATION_SQS_DATA_FIELDS
        self.queue_url = self.get_queue_url()

    def run(self):
        while True:
            requests = SQSManager.fetch_message_sp(self.queue_url, 10, False)
            if not requests:
                log_msg = "group_ivrinfo_relation_handler - no new requests found in sqs, exiting"
                Helper.sampling_logger_emitter(log_msg)
                break

            for request in requests:
                reset_uid()
                request_body = json.loads(request["Body"])

                # case 1: discard event_actions - book/release dids
                if self.is_discardable(request_body):
                    self.delete_request(request["ReceiptHandle"])
                    continue

                # required fields check
                if not self.is_valid_data(request_body["data"]):
                    self.delete_request(request["ReceiptHandle"])
                    continue

                settings.API_LOGGER.info(
                    "group_ivrinfo_relation_handler - request body: {rb}".format(
                        rb=request_body
                    )
                )

                # data to process
                company_id = request_body["data"][
                    self.data_fields["company_id"]
                ]
                fixdids = request_body["data"][self.data_fields["fixed_did"]]

                if not isinstance(fixdids, list):
                    fixdids = [fixdids]

                if (
                    request_body["event"]["action"]
                    == self.actions["map_action"]
                ):
                    # case 2: map event_action - map_did_ivr
                    self.map_dids(
                        company_id, fixdids, request["ReceiptHandle"], request
                    )
                elif (
                    request_body["event"]["action"]
                    == self.actions["unmap_action"]
                ):
                    # case 3: unmap event_action - unmap_did_ivr
                    self.unmap_dids(
                        company_id, fixdids, request["ReceiptHandle"], request
                    )
                else:
                    # case 4: error - event action doesn't match with any valid action
                    settings.API_LOGGER.error(
                        "group_ivrinfo_relation_handler - request event action didn't match with any valid action: {actions}, request_body:{rb}, deleting request!".format(
                            actions=self.actions, rb=request_body
                        )
                    )

                    self.delete_request(request["ReceiptHandle"])

    def is_fix_did_outgoing(
        self, fix_did_response, company_id, fix_did, request
    ):
        if fix_did_response.get("type", None) != conf.FIX_DID_OUTGOING_TYPE:
            settings.API_LOGGER.info(
                "group_ivrinfo_relation_handler - invalid did type for company_id:{c_id} and did: {did}, fixdid_api response: {res}, request_data: {rd}".format(
                    c_id=company_id,
                    did=fix_did,
                    res=fix_did_response,
                    rd=request,
                )
            )
            return False
        return True

    def check_common_cases_to_skip(
        self, fix_did_response, company_id, fix_did, event
    ):
        """
        Checks if an event should be skipped based on the FIX DID response.

        Args:
            fix_did_response (dict): The response from the FIX DID API.
            company_id (str): The ID of the company.
            fix_did (str): The DID number.
            event (object): Event received from sns   .

        Returns:
            bool: True if the event should be skipped, False otherwise.

        Notes:
            This function checks the API version, outgoing DID status,
            if the event should be skipped.
        """
        fix_did_api_version = fix_did_response.get(
            "version", conf.FIX_DID_VERSION
        ).strip()
        if fix_did_api_version != conf.FIX_DID_VERSION:
            settings.API_LOGGER.info(
                f"group_ivrinfo_relation_handler -  invalid version for company_id:{company_id} and did: {fix_did}, fixdid_api response: {fix_did_response}, request_data: {event}"
            )
            return True

        if not self.is_fix_did_outgoing(
            fix_did_response, company_id, fix_did, event
        ):
            return True

        return False

    def map_dids(self, company_id, fixdids, ReceiptHandle, request):  # noqa
        is_deletable = True
        settings.API_LOGGER.info(
            "group_ivrinfo_relation_handler - inside map_dids, company_id:{c_id} and did: {did}".format(
                c_id=company_id, did=fixdids
            )
        )
        for fixdid in fixdids:
            fixdid_data, status_code = ExternalAPIs.talk_to_retrieve_did_api(
                company_id, fixdid
            )

            if not fixdid_data:
                settings.API_LOGGER.info(
                    "group_ivrinfo_relation_handler - fixdid_data data not found for company_id:{c_id} and did: {did}, fixdid_api response: {res}, status_code: {sc}, request_data: {rd}".format(
                        c_id=company_id,
                        did=fixdid,
                        res=fixdid_data,
                        sc=status_code,
                        rd=request,
                    )
                )
                # is_deletable = False
                continue

            if self.check_common_cases_to_skip(
                fixdid_data, company_id, fixdid, request
            ):
                is_deletable = True
                continue

            if not fixdid_data["kam_group_id"]:
                settings.API_LOGGER.error(
                    "group_ivrinfo_relation_handler - invalid kam_group_id for company_id:{c_id} and did: {did}, fixdid_api response: {res}, request_data: {rd}".format(
                        c_id=company_id,
                        did=fixdid,
                        res=fixdid_data,
                        rd=request,
                    )
                )
                is_deletable = True
                continue

            group_obj = self.get_group_obj(fixdid_data)
            if not group_obj:
                is_deletable = False
                settings.API_LOGGER.error(
                    "group_ivrinfo_relation_handler - unable to create group for fixdid data: {data}".format(
                        data=fixdid_data
                    )
                )
                continue

            number_obj = self.get_number_obj(fixdid_data, group_obj.id)

            if not fixdid_data["ivrs"]:
                settings.API_LOGGER.error(
                    "group_ivrinfo_relation_handler - no ivr mapped for company_id:{c_id} and did: {did}, fixdid_api response: {res}, request_data: {rd}".format(
                        c_id=company_id,
                        did=fixdid,
                        res=fixdid_data,
                        rd=request,
                    )
                )
                is_deletable = True
                continue

            for ivr in fixdid_data["ivrs"]:
                ivrinfo_filter_obj = IvrInfo.objects.filter(
                    ivr_id=ivr["id"], c_id=company_id
                )
                if not ivrinfo_filter_obj.exists():
                    is_deletable = True
                    settings.API_LOGGER.error(
                        "group_ivrinfo_relation_handler - ivr: {ivr} doesn't exists in DB, deleting request from sqs. SQS_DATA: {req}".format(
                            ivr=ivr, req=request
                        )
                    )
                    continue
                ivrinfo_obj = ivrinfo_filter_obj.first()

                group_ivrinfo_filter_obj = (
                    GroupIvrInfoRelationship.objects.filter(
                        group_id=group_obj.id,
                        ivr_info_id=ivrinfo_obj.id,
                        number_id=number_obj.id,
                    )
                )
                if not group_ivrinfo_filter_obj.exists():
                    settings.API_LOGGER.info(
                        "group_ivrinfo_relation_handler - creating group_ivrinfo relation, ivrinfo_id: {ivrinfo_id}, group_id:{group_id}, number_id: {number_id}".format(
                            ivrinfo_id=ivrinfo_obj.id,
                            group_id=group_obj.id,
                            number_id=number_obj.id,
                        )
                    )
                    with transaction.atomic():
                        GroupIvrInfoRelationship.objects.create(
                            group_id=group_obj.id,
                            ivr_info_id=ivrinfo_obj.id,
                            number_id=number_obj.id,
                        )
                else:
                    settings.API_LOGGER.info(
                        "group_ivrinfo_relation_handler - group_ivrinfo relation already exists, ivrinfo_id: {ivrinfo_id}, group_id:{group_id}, number_id: {number_id}".format(
                            ivrinfo_id=ivrinfo_obj.id,
                            group_id=group_obj.id,
                            number_id=number_obj.id,
                        )
                    )

        if is_deletable:
            self.delete_request(ReceiptHandle)

    def unmap_dids(self, company_id, fixdids, ReceiptHandle, request):
        is_deletable = True
        settings.API_LOGGER.info(
            "group_ivrinfo_relation_handler - inside unmap_dids, company_id:{c_id} and did: {did}".format(
                c_id=company_id, did=fixdids
            )
        )
        for fixdid in fixdids:
            fixdid_data, status_code = ExternalAPIs.talk_to_retrieve_did_api(
                company_id, fixdid
            )

            if not fixdid_data and status_code == "404":
                settings.API_LOGGER.info(
                    "group_ivrinfo_relation_handler - fixdid api data not found, deleting all relations of company_id:{c_id} and did: {did}".format(
                        c_id=company_id, did=fixdid
                    )
                )
                GroupIvrInfoRelationship.objects.filter(
                    ivr_info__c_id=company_id, number__number=fixdid
                ).delete()
                Number.objects.filter(number=fixdid).delete()

            elif fixdid_data and status_code == "200":
                if self.check_common_cases_to_skip(
                    fixdid_data, company_id, fixdid, request
                ):
                    is_deletable = True
                    continue

                ignore_ivrs = [ivr["id"] for ivr in fixdid_data["ivrs"]]
                settings.API_LOGGER.info(
                    "group_ivrinfo_relation_handler - ignoring ivrs: {ivrs}, while deleting relations between, company_id:{c_id} and did: {did}".format(
                        ivrs=ignore_ivrs, c_id=company_id, did=fixdid
                    )
                )
                GroupIvrInfoRelationship.objects.filter(
                    ivr_info__c_id=company_id, number__number=fixdid
                ).exclude(ivr_info__ivr_id__in=ignore_ivrs).delete()
                if not ignore_ivrs:
                    Number.objects.filter(number=fixdid).delete()
            else:
                is_deletable = False

        if is_deletable:
            self.delete_request(ReceiptHandle)

    def is_cache_key_exists(self):
        msg_count = CacheManager.get_value(
            conf.GROUP_IVRINFO_RELETION_COUNT_KEY
        )
        if not msg_count:
            log_msg = "group_ivrinfo_relation_handler - cache count key is None, exiting after sleeping for {dur}s...".format(
                dur=conf.GROUP_IVRINFO_SLEEP_DUR
            )
            # Helper.sampling_logger_emitter(msg=log_msg, sampling_probability=0.4, is_enable=True)
            settings.API_LOGGER.tempinfo(log_msg)

            time.sleep(conf.GROUP_IVRINFO_SLEEP_DUR)
            return False
        return True

    def get_queue_url(self):
        q_name_url_mapping = Helper.get_queue_name_url_mapping(
            "queue_name",
            conf.GROUP_IVRINFO_RELETION_QUEUE,
            conf.GROUP_IVRINFO_RELETION_QUEUE_GATEWAY_PREFIX,
        )

        return q_name_url_mapping[conf.GROUP_IVRINFO_RELETION_QUEUE]

    def is_discardable(self, request_body):
        for discard_action in self.actions["discard_actions"]:
            if request_body["event"]["action"] == discard_action:
                settings.API_LOGGER.info(
                    "group_ivrinfo_relation_handler - deleting sqs request because of discard action: {da}, sqs_body: {sb}".format(
                        sb=request_body, da=discard_action
                    )
                )
                break
        else:
            return False

        return True

    def is_valid_data(self, data):
        for field in self.data_fields:
            if self.data_fields[field] not in data:
                break
        else:
            return True

        settings.API_LOGGER.error(
            "group_ivrinfo_relation_handler - invalid data, required filed(s): {fields} are missing, request_data:{rb}, deleting request!".format(
                fields=list(self.data_fields.values()), rb=data
            )
        )
        return False

    def get_group_obj(self, data):
        group_filter_obj = Group.objects.filter(
            kam_group_id=data["kam_group_id"]
        )
        if not group_filter_obj.exists():
            kam_cache_key = conf.KAM_GROUP_SERVER_CACHE_KEY
            kam_api_name = conf.ROUTING_INFO_URL_NAMES[
                "kam_group_servers_route_name"
            ]
            payload = {
                "token": conf.SECRET_TOKENS["kam_group_servers_token"],
                "is_obd": 1,
                "kam_group_id": data["kam_group_id"],
            }

            groups_data = ExternalAPIs.talk_to_kamailio_api(
                kam_cache_key, kam_api_name, **payload
            )
            if not groups_data:
                settings.API_LOGGER.error(
                    "group_ivrinfo_relation_handler - unable to get kam_data for kam_group_id: {kam_group_id}".format(
                        kam_group_id=data["kam_group_id"]
                    )
                )
                return {}

            GroupServerRelationManager.add_group_servers(
                data["kam_group_id"], groups_data[data["kam_group_id"]]
            )
            try:
                # updating extra data of group.
                Group.objects.filter(
                    kam_group_id=data["kam_group_id"]
                ).invalidated_update(
                    group_priority=data["group_priority"],
                    updated_on=datetime.now(),
                )
            except Exception as e:
                settings.API_LOGGER.error(
                    "group_ivrinfo_relation_handler - error updaing group data, fixdid api data: {data}".format(
                        data=data
                    )
                )

        group_obj = group_filter_obj.first()
        return group_obj

    def get_number_obj(self, data, group_id):
        number_filter_obj = Number.objects.filter(number=data["did"])
        if not number_filter_obj.exists():
            with transaction.atomic():
                Number.objects.create(
                    number=data["did"],
                    number_priority=data["number_priority"],
                    group_id=group_id,
                    is_fix_did=Number.YES,
                )
            settings.API_LOGGER.info(
                "group_ivrinfo_relation_handler - creating Number, number: {did}, group_id:{group_id}".format(
                    did=data["did"], group_id=group_id
                )
            )
        else:
            number_filter_obj.invalidated_update(
                is_fix_did=Number.YES, updated_on=datetime.now()
            )

        number_obj = number_filter_obj.first()
        return number_obj

    def delete_request(self, ReceiptHandle):
        SQSManager.sqs_delete_message(self.queue_url, ReceiptHandle)
        CacheManager.decr_value(conf.GROUP_IVRINFO_RELETION_COUNT_KEY, True)
