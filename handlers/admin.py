# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django import forms
from django.contrib import admin
from django.db.models import Count, Q
from django.utils.translation import ugettext_lazy as _
from django_extensions.admin import ForeignKeyAutocompleteAdmin

from handlers.models import CompanyUsage, CommonSetting, Group, GroupIvrInfoRelationship, GroupServerRelationship, IvrInfo, Server, SQSQueueInfo, Number

class CompanyUsageAdmin(admin.ModelAdmin):
	search_fields = ['c_id']
	list_display = ['c_id','ban', 'event']
	exclude = ()


class CommonSettingAdmin(admin.ModelAdmin):
	search_fields = ['entity']
	list_display = ['entity','settings', 'added_on']
	exclude = ()


class IvrInfoAdmin(admin.ModelAdmin):
	search_fields = ['ivr_id', "common_setting"]
	list_display = ['id', 'ivr_id', 'c_id', 'ivr_type', 'common_setting', 'company_display_number', 'added_on', 'updated_on']
	exclude = ()


class GroupAdmin(admin.ModelAdmin):
	search_fields = ['name']
	list_display = ['id', 'name', 'region', 'group_alias', 'group_priority', 'kam_group_id', 'settings', 'assigned_queues', 'is_enable', 'is_default','added_on']
	exclude = ()


class GroupIvrInfoRelationshipAdmin(admin.ModelAdmin):
	search_fields = []
	list_display = ['group_id', 'ivr_info_id', 'number_id' ,'added_on']
	exclude = ()


class SQSQueueInfoAdmin(admin.ModelAdmin):
	search_fields = ['gateway_prefix']
	list_display = ['gateway_prefix', 'queue_name', 'queue_url', 'added_on']
	exclude = ()


class ServerAdmin(admin.ModelAdmin):
	search_fields = ['name']
	list_display = ['id', 'name', 'is_enable', 'added_on']
	exclude = ()


class GroupServerRelationshipAdmin(admin.ModelAdmin):
	search_fields = []
	list_display = ['server_id', 'group_id','added_on', 'updated_on']
	exclude = ()

class NumberAdmin(admin.ModelAdmin):
	search_fields = ['number']
	list_display = ['id', 'number', 'number_priority', 'is_fix_did', 'group']
	exclude = ()


admin.site.register(CompanyUsage, CompanyUsageAdmin)
admin.site.register(CommonSetting, CommonSettingAdmin)
admin.site.register(IvrInfo, IvrInfoAdmin)
admin.site.register(Group, GroupAdmin)
admin.site.register(GroupIvrInfoRelationship, GroupIvrInfoRelationshipAdmin)
admin.site.register(SQSQueueInfo, SQSQueueInfoAdmin)
admin.site.register(Server, ServerAdmin)
admin.site.register(GroupServerRelationship, GroupServerRelationshipAdmin)
admin.site.register(Number, NumberAdmin)