import json
import uuid
from datetime import datetime
from typing import Dict

from django.conf import settings
from django.db import models
from django.db.models import F
from django.db.models.signals import post_save
from django.dispatch import receiver
from rest_framework.exceptions import ValidationError

import jsonfield


class CommonSetting(models.Model):
    entity = models.CharField(max_length=255, unique=True)
    settings = models.TextField(null=True, blank=True)

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "common_settings"

    def __str__(self):
        return "%s (id:%s)" % (self.entity, self.id)

    @property
    def get_settings(self):
        try:
            return json.loads(self.settings)
        except Exception as e:
            return ""


class SQSQueueInfo(models.Model):
    gateway_prefix = models.CharField(max_length=255, null=True, blank=True)
    queue_name = models.CharField(max_length=255, unique=True)
    queue_url = models.Cha<PERSON><PERSON><PERSON>(max_length=255, null=True, blank=True)

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "sqs_queues_info"

    def __str__(self):
        return "%s (id:%s)" % (self.queue_name, self.id)

    # @property
    # def get_queue_name(self):
    # 	try:
    # 		return str(self.queue_prefix + self.queue_name)
    # 	except Exception as e:
    # 		return ""


# class RoutingInfo(models.Model):
# 	QUEUE = 1
# 	API = 0
# 	INDEX_CHOICES = (
# 		(QUEUE, 'Queue'),
# 		(API, 'Api')

# 	)

# 	name = models.CharField(max_length=254, unique=True)
# 	method = models.CharField(max_length=255)
# 	url = models.CharField(max_length=255)
# 	data = models.TextField(null=True, blank=True)
# 	service_type = models.CharField(max_length=255, default=QUEUE)


# 	added_on = models.DateTimeField(auto_now_add=True)
# 	updated_on = models.DateTimeField(auto_now=True)

# 	class Meta:
# 		db_table = 'routing_info'

# 	def __str__(self):
# 		return '%s (id:%s)' % (self.name, self.id)


class ApiResponseFailure(models.Model):
    request_id = models.CharField(max_length=255)
    status_code = models.IntegerField(null=True, blank=True)
    response = models.TextField(null=True, blank=True)

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "api_response_failure"

    def __str__(self):
        return "%s-%s (id:%s)" % (self.request_id, self.status_code, self.id)


class BaseRequestInfo(models.Model):
    NO = 0
    YES = 1
    ENTER = 1
    EXIT = 2
    IVR_TIMING_FAIL = 3
    EXPIRED = 4

    NA = 0
    FROM_RESPONSE_MANAGER = 1
    FROM_EXPIRY_MANAGER = 2
    FROM_IVR_PROCESSOR_FAILED = 3

    RESPONDED = 1
    NOT_RESPONDED = 2
    BRIDGED = 3
    MISSED = 4
    VOICEMAIL = 5
    SUCCESS = 6
    FAILED = 7

    ONHOLD_STAGES = (
        (NA, "na"),
        (ENTER, "Enter"),
        (EXIT, "Exit"),
        (IVR_TIMING_FAIL, "ivr_timing_fail"),
        (EXPIRED, "expired"),
    )

    REQUEST_CHOICES = (
        (NO, "No"),
        (YES, "Yes"),
    )

    COMPLETION_EVENT_TYPE = (
        (NA, "na"),
        (FROM_RESPONSE_MANAGER, "Response_manager"),
        (FROM_EXPIRY_MANAGER, "Expiry_manager"),
        (FROM_IVR_PROCESSOR_FAILED, "IVR_PROCESSOR_FAILED"),
    )

    EVENT_RESPONSE_TYPES = (
        (NA, "na"),
        (RESPONDED, "Responded"),
        (NOT_RESPONDED, "Not_Responded"),
        (BRIDGED, "Bridged"),
        (MISSED, "Missed"),
        (VOICEMAIL, "Voicemail"),
        (SUCCESS, "Success"),
        (FAILED, "Failed"),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    request_id = models.CharField(max_length=255)
    ivr_id = models.CharField(max_length=255, null=True, blank=True)
    c_id = models.CharField(max_length=255, null=True, blank=True)
    user_id = models.CharField(max_length=255, null=True, blank=True)
    request_type = models.IntegerField(null=True, blank=True)
    raw_data = models.TextField(null=True, blank=True)

    is_req_completed = models.PositiveSmallIntegerField(
        choices=REQUEST_CHOICES, default=NO
    )
    is_onhold_req = models.PositiveSmallIntegerField(
        choices=ONHOLD_STAGES, default=NA
    )
    is_enable = models.PositiveSmallIntegerField(
        choices=REQUEST_CHOICES, default=YES
    )

    # response [response_manager, expiry_manager]
    completion_event_type = models.PositiveSmallIntegerField(
        choices=COMPLETION_EVENT_TYPE, default=NA
    )
    event_response = models.PositiveSmallIntegerField(
        choices=EVENT_RESPONSE_TYPES, default=NA
    )

    onhold_udc_check_availibilty_cnt = models.IntegerField(default=0)
    iteration = models.IntegerField(default=1)

    expired_at = models.DateTimeField(null=True, blank=True)

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    job_id = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        abstract = True

    def __str__(self):
        return "%s (id:%s)" % (self.request_id, self.id)


class CentralRequestInfo(BaseRequestInfo):
    class Meta:
        db_table = "central_request_info"
        indexes = [
            models.Index(
                fields=[
                    "request_id",
                    "ivr_id",
                    "is_req_completed",
                    "is_enable",
                    "is_onhold_req",
                    "added_on",
                    "updated_on",
                    "onhold_udc_check_availibilty_cnt",
                ]
            ),
            models.Index(
                fields=[
                    "ivr_id",
                    "is_onhold_req",
                    "is_req_completed",
                    "is_enable",
                ]
            ),
            models.Index(fields=["user_id"]),
            models.Index(fields=["added_on"]),
            models.Index(fields=["is_onhold_req"]),
            models.Index(
                fields=["job_id", "is_onhold_req", "is_req_completed"]
            ),
        ]

    @property
    def body(self) -> Dict:
        return json.loads(self.raw_data)

    def mark_ivr_timing_failed(self):
        self.is_onhold_req = self.IVR_TIMING_FAIL
        self.onhold_udc_check_availibilty_cnt = 0
        self.is_enable = self.NO
        self.save(
            update_fields=[
                "is_onhold_req",
                "onhold_udc_check_availibilty_cnt",
                "is_enable",
                "updated_on",
            ]
        )


class OnHoldRequestTracing(models.Model):
    request_id = models.CharField(max_length=255, null=True, blank=True)
    count_req = models.IntegerField(default=0)

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "on_hold_request_tracing"

    def __str__(self):
        return "%s (id:%s)" % (self.request_id, self.id)


class OnHoldIvrTracing(models.Model):
    ivr_id = models.CharField(max_length=255, null=True, blank=True)
    count_ivr = models.IntegerField(default=0)

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "on_hold_ivr_tracing"

    def __str__(self):
        return "%s (id:%s)" % (self.ivr_id, self.id)


class OnHoldUserTracing(models.Model):
    user_id = models.CharField(max_length=255, null=True, blank=True)
    ivr_id = models.CharField(max_length=255, null=True, blank=True)
    count_user = models.IntegerField(default=0)
    count_disabled_req = models.IntegerField(default=0)

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "on_hold_user_tracing"

    def __str__(self):
        return "%s (id:%s)" % (self.user_id, self.id)


@receiver(post_save, sender=CentralRequestInfo)
def update_or_create_request_ivr_count(
    sender, instance=None, created=False, **kwargs
):
    # @file_cache.cached(timeout=40)
    def get_request_from_on_hold_ivr_tracing(ivr_id):
        return OnHoldIvrTracing.objects.filter(ivr_id=ivr_id).nocache()

    # @file_cache.cached(timeout=300)
    def get_request_from_on_hold_request_tracing(request_id):
        return OnHoldRequestTracing.objects.filter(
            request_id=request_id
        ).nocache()

    def get_request_from_on_hold_user_tracing(user_id, ivr_id):
        return OnHoldUserTracing.objects.filter(user_id=user_id, ivr_id=ivr_id)

    if (
        instance.is_onhold_req == CentralRequestInfo.ENTER
        and instance.is_req_completed != CentralRequestInfo.YES
    ):
        on_hold_ivr, _ = OnHoldIvrTracing.objects.get_or_create(
            ivr_id=instance.ivr_id
        )
        on_hold_ivr.count_ivr = F("count_ivr") + 1
        on_hold_ivr.save(update_fields=["count_ivr", "updated_on"])
        on_hold_req, _ = OnHoldRequestTracing.objects.get_or_create(
            request_id=instance.request_id
        )
        on_hold_req.count_req = F("count_req") + 1
        on_hold_req.save(update_fields=["count_req", "updated_on"])

        if instance.user_id and instance.request_type == 1:
            on_hold_user, _ = OnHoldUserTracing.objects.get_or_create(
                user_id=instance.user_id, ivr_id=instance.ivr_id
            )

            if on_hold_user.count_user >= 1:
                on_hold_user.count_disabled_req = F("count_disabled_req") + 1

            on_hold_user.count_user = F("count_user") + 1
            on_hold_user.save(
                update_fields=[
                    "count_user",
                    "count_disabled_req",
                    "updated_on",
                ]
            )

    if (
        instance.is_req_completed == CentralRequestInfo.YES
        or instance.is_onhold_req == CentralRequestInfo.IVR_TIMING_FAIL
    ):
        # response manager, expiry manager and onhold circulator barriers failed
        # Note: A req gets disabled when either its on_hold entry max repeat count is exausted or max udc check is exausted.
        onhold_request_tracing = get_request_from_on_hold_request_tracing(
            instance.request_id
        )  # OnHoldRequestTracing.objects.filter(request_id=instance.request_id)
        if onhold_request_tracing.exists():
            onhold_request_tracing = onhold_request_tracing.first()
            onhold_request_tracing.delete()

    if instance.is_onhold_req not in [
        CentralRequestInfo.NA,
        CentralRequestInfo.ENTER,
    ]:
        # onhold barrier failed, onhold exit
        # decrease IVR Trace count. when either the request gets sent to PQs or the request is disabled.
        onhold_ivr_tracing = get_request_from_on_hold_ivr_tracing(
            instance.ivr_id
        )  # OnHoldIvrTracing.objects.filter(ivr_id=instance.ivr_id)
        if onhold_ivr_tracing.exists():
            onhold_ivr_tracing = onhold_ivr_tracing.first()
            if onhold_ivr_tracing.count_ivr <= 1:
                onhold_ivr_tracing.delete()
            else:
                onhold_ivr_tracing.count_ivr = F("count_ivr") - 1
                onhold_ivr_tracing.save(
                    update_fields=["count_ivr", "updated_on"]
                )

        # user_tracing
        if instance.user_id and instance.request_type == 1:
            onhold_user_tracing_obj = get_request_from_on_hold_user_tracing(
                instance.user_id, instance.ivr_id
            )
            if onhold_user_tracing_obj.exists():
                onhold_user_tracing = onhold_user_tracing_obj.first()

                if onhold_user_tracing.count_disabled_req >= 1:
                    # enabling one request for an user
                    central_req_obj = CentralRequestInfo.objects.filter(
                        user_id=instance.user_id,
                        is_enable=CentralRequestInfo.NO,
                        is_req_completed=CentralRequestInfo.NO,
                        is_onhold_req=CentralRequestInfo.ENTER,
                    )
                    if central_req_obj.exists():
                        central_req_obj = central_req_obj.first()
                        try:
                            CentralRequestInfo.objects.filter(
                                id=central_req_obj.id
                            ).invalidated_update(
                                is_enable=CentralRequestInfo.YES,
                                updated_on=datetime.now(),
                            )
                            settings.API_LOGGER.info(
                                "model post save - enabling request_id: {rid1}, because of completed request_id: {rid}".format(
                                    rid=instance.request_id,
                                    rid1=central_req_obj.request_id,
                                )
                            )
                        except Exception as e:
                            settings.API_LOGGER.error(
                                "model post save error - for user tracing enabling {rid}".format(
                                    rid=e
                                )
                            )

                if onhold_user_tracing.count_user <= 1:
                    onhold_user_tracing.delete()
                else:
                    onhold_user_tracing.count_user = F("count_user") - 1
                    onhold_user_tracing.count_disabled_req = (
                        F("count_disabled_req") - 1
                    )
                    onhold_user_tracing.save(
                        update_fields=[
                            "count_user",
                            "count_disabled_req",
                            "updated_on",
                        ]
                    )


class IvrInfo(models.Model):
    NA = 0
    COC = 1
    P2P = 2
    IVR = 3

    IVR_TYPE = ((NA, "na"), (COC, "coc"), (P2P, "p2p"), (IVR, "ivr"))

    ivr_type = models.PositiveSmallIntegerField(choices=IVR_TYPE, default=NA)

    c_id = models.CharField(max_length=255)
    ivr_id = models.CharField(max_length=255, unique=True)
    company_priority = models.IntegerField(default=0)
    ivr_priority = models.IntegerField(default=0)
    # retry_time,num_retry,cl_id,d_id,name,extension,priority_1,desc,
    common_setting = jsonfield.JSONField(null=True, blank=True)

    company_display_number = models.CharField(
        max_length=255, null=True, blank=True
    )

    country_code = models.CharField(max_length=255, null=True, blank=True)

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "ivr_info"

    def __str__(self):
        return "%s (id:%s)" % (self.ivr_id, self.id)

    @property
    def get_groups(self):
        return GroupIvrInfoRelationship.objects.filter(
            ivr_info=self
        ).values_list("group", flat=True)

    def save(self, *args, **kwargs):  # pylint: disable=arguments-differ
        if isinstance(self.common_setting, int):
            raise ValidationError(
                {
                    "common_setting": "kindly provide valid common setting not int."
                }
            )
        super(IvrInfo, self).save(*args, **kwargs)


class Group(models.Model):
    YES = 1
    NO = 0

    TYPE = (
        (YES, "Yes"),
        (NO, "No"),
    )

    AWS_GROUP_TYPE = "aws"
    PHY_GROUP_TYPE = "phy"
    PHY_AWS_GROUP_TYPE = "phy_aws"

    AWS_GROUP_TYPES = [AWS_GROUP_TYPE, PHY_AWS_GROUP_TYPE]

    name = models.CharField(max_length=255)
    region = models.CharField(max_length=255, null=True, blank=True)
    group_alias = models.CharField(max_length=255, null=True, blank=True)
    group_priority = models.IntegerField(default=0)
    assigned_queues = models.TextField(null=True, blank=True)
    is_enable = models.PositiveSmallIntegerField(choices=TYPE, default=YES)

    kam_group_id = models.IntegerField()
    settings = jsonfield.JSONField(null=True, blank=True)
    is_default = models.PositiveSmallIntegerField(choices=TYPE, default=NO)

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "groups"

    def __str__(self):
        return "%s (id:%s)" % (self.name, self.id)

    # assigned queues format
    # {
    # 	"p_q": "q_name",
    # 	"np_q": "q_name"
    # }

    @property
    def get_assigned_queues(self):
        try:
            return json.loads(self.assigned_queues.replace("'", '"'))
        except Exception as e:
            return ""

    def is_type_aws(self) -> bool:
        return (
            self.settings
            and self.settings.get("group_type") in self.AWS_GROUP_TYPES
        )


class Number(models.Model):
    NO = 0
    YES = 1
    IS_FIX_DID_CHOICES = (
        (NO, "No"),
        (YES, "Yes"),
    )

    number = models.CharField(max_length=255, unique=True)
    number_priority = models.IntegerField(default=0)

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    is_fix_did = models.PositiveSmallIntegerField(
        choices=IS_FIX_DID_CHOICES, default=NO
    )

    group = models.ForeignKey(Group, on_delete=models.CASCADE)

    class Meta:
        db_table = "numbers"

    def __str__(self):
        return "%s (id:%s)" % (self.number, self.id)


class GroupIvrInfoRelationship(models.Model):
    ivr_info = models.ForeignKey(
        IvrInfo, blank=True, null=True, on_delete=models.CASCADE
    )
    group = models.ForeignKey(
        Group, blank=True, null=True, on_delete=models.CASCADE
    )
    number = models.ForeignKey(
        Number, blank=True, null=True, on_delete=models.CASCADE
    )

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "group_ivr_info_relationship"
        unique_together = ["ivr_info", "group", "number"]

    def __str__(self):
        return (
            "GroupIvrInfoRelationship: %s, Group: %s, Number: %s (id:%s)"
            % (self.ivr_info, self.group, self.number, self.id)
        )


class Server(models.Model):
    YES = 1
    NO = 0

    TYPE = (
        (YES, "Yes"),
        (NO, "No"),
    )

    name = models.CharField(max_length=255, unique=True)
    group = models.ManyToManyField(Group, through="GroupServerRelationship")
    is_enable = models.PositiveSmallIntegerField(choices=TYPE, default=YES)

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "servers"

    def __str__(self):
        return "%s (id:%s)" % (self.name, self.id)


class GroupServerRelationship(models.Model):
    group = models.ForeignKey(
        Group, blank=True, null=True, on_delete=models.CASCADE
    )
    server = models.ForeignKey(
        Server, blank=True, null=True, on_delete=models.CASCADE
    )
    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "group_server_relationship"
        unique_together = ["group", "server"]


class CompanyUsageEventManager(models.Manager):
    def get_queryset(self):
        return (
            super(CompanyUsageEventManager, self)
            .get_queryset()
            .exclude(event__in=[])
        )


class CompanyUsage(models.Model):
    c_id = models.CharField(max_length=255, unique=True)
    ban = models.CharField(max_length=255, null=True, blank=True)
    event = models.IntegerField(null=True, blank=True)

    added_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    activated_companies = CompanyUsageEventManager()
    objects = models.Manager()

    class Meta:
        db_table = "company_usage"

    def __str__(self):
        return "%s (id:%s)" % (self.c_id, self.id)


class TrashCentralRequestInfo(BaseRequestInfo):
    class Meta:
        db_table = "trash_central_request_info"
        indexes = [
            models.Index(fields=["request_id", "is_req_completed"]),
        ]
