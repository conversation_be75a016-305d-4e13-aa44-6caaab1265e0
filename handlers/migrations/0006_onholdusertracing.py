# -*- coding: utf-8 -*-
# Generated by Django 1.11.7 on 2020-09-30 17:21
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('handlers', '0005_auto_20200922_0614'),
    ]

    operations = [
        migrations.CreateModel(
            name='OnHoldUserTracing',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(blank=True, max_length=255, null=True)),
                ('count_user', models.IntegerField(default=0)),
                ('count_disabled_req', models.IntegerField(default=0)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'on_hold_user_tracing',
            },
        ),
    ]
