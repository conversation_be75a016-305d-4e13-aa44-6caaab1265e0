# -*- coding: utf-8 -*-
# Generated by Django 1.11.7 on 2022-07-05 08:07
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('handlers', '0015_onholdusertracing_ivr_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='centralrequestinfo',
            name='job_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='trashcentralrequestinfo',
            name='job_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddIndex(
            model_name='centralrequestinfo',
            index=models.Index(fields=['job_id', 'is_onhold_req', 'is_req_completed'], name='central_req_job_id_8955a5_idx'),
        ),
    ]
