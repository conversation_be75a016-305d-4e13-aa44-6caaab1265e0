# -*- coding: utf-8 -*-
# Generated by Django 1.11.7 on 2020-04-29 08:35
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import django.db.models.manager
import jsonfield.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ApiResponseFailure',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_id', models.CharField(max_length=255)),
                ('status_code', models.IntegerField(blank=True, null=True)),
                ('response', models.TextField(blank=True, null=True)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'api_response_failure',
            },
        ),
        migrations.CreateModel(
            name='CentralRequestInfo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_id', models.CharField(max_length=255)),
                ('ivr_id', models.CharField(blank=True, max_length=255, null=True)),
                ('c_id', models.CharField(blank=True, max_length=255, null=True)),
                ('user_id', models.CharField(blank=True, max_length=255, null=True)),
                ('request_type', models.IntegerField(blank=True, null=True)),
                ('raw_data', models.TextField(blank=True, null=True)),
                ('is_req_completed', models.PositiveSmallIntegerField(choices=[(0, 'No'), (1, 'Yes')], default=0)),
                ('is_onhold_req', models.PositiveSmallIntegerField(choices=[(0, 'na'), (1, 'Enter'), (2, 'Exit')], default=0)),
                ('is_enable', models.PositiveSmallIntegerField(choices=[(0, 'No'), (1, 'Yes')], default=1)),
                ('completion_event_type', models.PositiveSmallIntegerField(choices=[(0, 'na'), (1, 'Response_manager'), (2, 'Expiry_manager')], default=0)),
                ('event_response', models.PositiveSmallIntegerField(choices=[(0, 'na'), (1, 'Responded'), (2, 'Not_Responded'), (3, 'Bridged'), (4, 'Missed'), (5, 'Voicemail'), (6, 'Success'), (7, 'Failed')], default=0)),
                ('onhold_udc_check_availibilty_cnt', models.IntegerField(default=0)),
                ('iteration', models.IntegerField(default=1)),
                ('expired_at', models.DateTimeField(blank=True, null=True)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'central_request_info',
            },
        ),
        migrations.CreateModel(
            name='CommonSetting',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entity', models.CharField(max_length=255, unique=True)),
                ('settings', models.TextField(blank=True, null=True)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'common_settings',
            },
        ),
        migrations.CreateModel(
            name='CompanyUsage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('c_id', models.CharField(max_length=255, unique=True)),
                ('ban', models.CharField(blank=True, max_length=255, null=True)),
                ('event', models.IntegerField(blank=True, null=True)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'company_usage',
            },
            managers=[
                ('activated_companies', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='Group',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('region', models.CharField(blank=True, max_length=255, null=True)),
                ('group_alias', models.CharField(blank=True, max_length=255, null=True)),
                ('group_priority', models.IntegerField(default=0)),
                ('assigned_queues', models.TextField(blank=True, null=True)),
                ('is_enable', models.PositiveSmallIntegerField(choices=[(1, 'Yes'), (0, 'No')], default=1)),
                ('kam_group_id', models.IntegerField()),
                ('settings', jsonfield.fields.JSONField(blank=True, null=True)),
                ('is_default', models.PositiveSmallIntegerField(choices=[(1, 'Yes'), (0, 'No')], default=0)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'groups',
            },
        ),
        migrations.CreateModel(
            name='GroupIvrInfoRelationship',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'group_ivr_info_relationship',
            },
        ),
        migrations.CreateModel(
            name='GroupServerRelationship',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'group_server_relationship',
            },
        ),
        migrations.CreateModel(
            name='IvrInfo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('c_id', models.CharField(max_length=255)),
                ('ivr_id', models.CharField(max_length=255, unique=True)),
                ('company_priority', models.IntegerField(default=0)),
                ('ivr_priority', models.IntegerField(default=0)),
                ('common_setting', jsonfield.fields.JSONField(blank=True, null=True)),
                ('company_display_number', models.CharField(blank=True, max_length=255, null=True)),
                ('country_code', models.CharField(blank=True, max_length=255, null=True)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'ivr_info',
            },
        ),
        migrations.CreateModel(
            name='Number',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.CharField(max_length=255, unique=True)),
                ('number_priority', models.IntegerField(default=0)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('is_fix_did', models.PositiveSmallIntegerField(choices=[(0, 'No'), (1, 'Yes')], default=0)),
            ],
            options={
                'db_table': 'numbers',
            },
        ),
        migrations.CreateModel(
            name='OnHoldIvrTracing',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ivr_id', models.CharField(blank=True, max_length=255, null=True)),
                ('count_ivr', models.IntegerField(default=0)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'on_hold_ivr_tracing',
            },
        ),
        migrations.CreateModel(
            name='OnHoldRequestTracing',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_id', models.CharField(blank=True, max_length=255, null=True)),
                ('count_req', models.IntegerField(default=0)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'on_hold_request_tracing',
            },
        ),
        migrations.CreateModel(
            name='Server',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('is_enable', models.PositiveSmallIntegerField(choices=[(1, 'Yes'), (0, 'No')], default=1)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'servers',
            },
        ),
        migrations.CreateModel(
            name='SQSQueueInfo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gateway_prefix', models.CharField(blank=True, max_length=255, null=True)),
                ('queue_name', models.CharField(max_length=255, unique=True)),
                ('queue_url', models.CharField(blank=True, max_length=255, null=True)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'sqs_queues_info',
            },
        ),
        migrations.CreateModel(
            name='TrashCentralRequestInfo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_id', models.CharField(max_length=255)),
                ('ivr_id', models.CharField(blank=True, max_length=255, null=True)),
                ('c_id', models.CharField(blank=True, max_length=255, null=True)),
                ('user_id', models.CharField(blank=True, max_length=255, null=True)),
                ('request_type', models.IntegerField(blank=True, null=True)),
                ('raw_data', models.TextField(blank=True, null=True)),
                ('is_req_completed', models.PositiveSmallIntegerField(choices=[(0, 'No'), (1, 'Yes')], default=0)),
                ('is_onhold_req', models.PositiveSmallIntegerField(choices=[(0, 'na'), (1, 'Enter'), (2, 'Exit')], default=0)),
                ('is_enable', models.PositiveSmallIntegerField(choices=[(0, 'No'), (1, 'Yes')], default=1)),
                ('completion_event_type', models.PositiveSmallIntegerField(choices=[(0, 'na'), (1, 'Response_manager'), (2, 'Expiry_manager')], default=0)),
                ('event_response', models.PositiveSmallIntegerField(choices=[(0, 'na'), (1, 'Responded'), (2, 'Not_Responded'), (3, 'Bridged'), (4, 'Missed'), (5, 'Voicemail'), (6, 'Success'), (7, 'Failed')], default=0)),
                ('onhold_udc_check_availibilty_cnt', models.IntegerField(default=0)),
                ('iteration', models.IntegerField(default=1)),
                ('expired_at', models.DateTimeField(blank=True, null=True)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'trash_central_request_info',
            },
        ),
        migrations.AddIndex(
            model_name='trashcentralrequestinfo',
            index=models.Index(fields=['request_id', 'is_req_completed'], name='trash_centr_request_be698e_idx'),
        ),
        migrations.AddField(
            model_name='server',
            name='group',
            field=models.ManyToManyField(through='handlers.GroupServerRelationship', to='handlers.Group'),
        ),
        migrations.AddField(
            model_name='number',
            name='group',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='handlers.Group'),
        ),
        migrations.AddField(
            model_name='groupserverrelationship',
            name='group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='handlers.Group'),
        ),
        migrations.AddField(
            model_name='groupserverrelationship',
            name='server',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='handlers.Server'),
        ),
        migrations.AddField(
            model_name='groupivrinforelationship',
            name='group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='handlers.Group'),
        ),
        migrations.AddField(
            model_name='groupivrinforelationship',
            name='ivr_info',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='handlers.IvrInfo'),
        ),
        migrations.AddField(
            model_name='groupivrinforelationship',
            name='number',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='handlers.Number'),
        ),
        migrations.AddIndex(
            model_name='centralrequestinfo',
            index=models.Index(fields=['request_id', 'is_req_completed', 'is_enable', 'is_onhold_req'], name='central_req_request_2b39d1_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='groupserverrelationship',
            unique_together=set([('group', 'server')]),
        ),
        migrations.AlterUniqueTogether(
            name='groupivrinforelationship',
            unique_together=set([('ivr_info', 'group', 'number')]),
        ),
    ]
