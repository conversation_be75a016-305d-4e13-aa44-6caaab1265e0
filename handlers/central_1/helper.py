from math import ceil

from django.conf import settings

from central.settings.handlers import conf
from handlers.models import IvrInfo
from utills.cache_manager.main import CacheManager
from utills.external_apis import ExternalAPIs
from utills.helpers.helper import Helper
from utills.sqs_manager.main import SQSManager


class Central1_Helper:
    @staticmethod
    def get_priority_ivr_mapping_to_process():
        """
        Returns the dict of priority with available IVRs list for that priority from Redis.
        It also ignores the paused_ivrs and exlude_ivrs from the above-mentioned result.
        Note: Paused IVRs are stored as separate keys for each IVR.
        Note: Exlude IVRs are stored as a list of IVRs with "obd-central-exclude-ivrs"
        Parameters:
                None
        Returns:
                dict: {'1': ['ivr1'], '2': ['ivr3']}
        """
        ignored_ivrs = []
        paused_ivrs = []
        exclude_ivrs = []
        ivrs_to_process = {}

        ivrs = CacheManager.get_many(conf.CACHE_IVR_KEY_NAME)
        ivrs = [ivr.split(conf.CACHE_IVR_KEY_NAME)[-1] for ivr in ivrs]
        paused_ivr_keys = CacheManager.get_keys(conf.CACHE_PAUSED_IVR_KEY_NAME)
        exclude_ivr_keys = CacheManager.get_keys(
            conf.CACHE_EXCLUDE_IVR_KEY_NAME
        )

        for paused_ivr in paused_ivr_keys:
            paused_ivrs.append(
                paused_ivr.split(conf.CACHE_PAUSED_IVR_KEY_NAME)[-1]
            )  # Getting the IVR-ID

        for exclude_ivr in exclude_ivr_keys:
            exclude_ivrs.append(
                exclude_ivr.split(conf.CACHE_EXCLUDE_IVR_KEY_NAME)[-1]
            )

        if paused_ivrs:
            settings.API_LOGGER.info(
                "central_1 - paused IVRs {paused_ivrs}".format(
                    paused_ivrs=paused_ivrs
                )
            )
            ignored_ivrs.extend(paused_ivrs)
        if exclude_ivrs:
            settings.API_LOGGER.info(
                "central_1 - exclude IVRs {exclude_ivrs}".format(
                    exclude_ivrs=exclude_ivrs
                )
            )
            ignored_ivrs.extend(exclude_ivrs)

        not_ignored_ivrs = list(set(ivrs) - set(ignored_ivrs))

        # Creating a mapping of priority with ivrs list.
        for ivr_id in not_ignored_ivrs:
            ivr_info_obj = IvrInfo.objects.filter(ivr_id=ivr_id)
            if not ivr_info_obj.exists():
                ivr_cache_key = "{prefix}{ivr_id}".format(
                    prefix=conf.CACHE_IVR_KEY_NAME, ivr_id=ivr_id
                )
                settings.API_LOGGER.error(
                    "central_1 - IvrInfo data not found for ivr_id: {ivr_id}, deleting cache key: {k}.".format(
                        ivr_id=ivr_id, k=ivr_cache_key
                    )
                )
                CacheManager.delete_key(ivr_cache_key)
                continue
            ivr_info = ivr_info_obj.first()
            if ivr_info.ivr_priority not in ivrs_to_process:
                ivrs_to_process[ivr_info.ivr_priority] = []
            ivrs_to_process[ivr_info.ivr_priority].append(ivr_id)

        return ivrs_to_process

    @staticmethod
    def get_thresholds():
        """
        Responsibilities:
                1. Get threshold from DB.
                2. In case threshold is incorrect then, raises alert and use default from conf.
                3. Get SQS available msg count (current_msg_count - q_threshold)
                4. Calculate threshold for each threads (n / m), n=free_resource_count, m=number of threads
        Parameters:
                None
        Returns:
                tuple: c1_threshold, max_workers, pq2_thread_threshold, pq3_thread_threshold
        """
        threshold = Helper.get_threshold(
            conf.CENTRAL_1_ENTITY, conf.CENTRAL_1_THRESHOLD
        )  # Threshold of central_1 and PQs.

        # Just Extra Validation, in case someone puts wrong settings from admin panel.
        try:
            c1_threshold = int(threshold[conf.CENTRAL_1])
            max_workers = int(threshold[conf.CENTRAL_1_MAX_WORKERS])
            pq2_threshold = int(threshold[conf.CENTRAL_PQ2])
            pq3_threshold = int(threshold[conf.CENTRAL_PQ3])
            single_ivr_threshold = int(
                threshold[conf.SINGLE_IVR_THRESHOLD_KEY]
            )
        except Exception as e:
            c1_threshold = conf.CENTRAL_1_THRESHOLD[conf.CENTRAL_1]
            max_workers = conf.CENTRAL_1_THRESHOLD[conf.CENTRAL_1_MAX_WORKERS]
            pq2_threshold = conf.CENTRAL_1_THRESHOLD[conf.CENTRAL_PQ2]
            pq3_threshold = conf.CENTRAL_1_THRESHOLD[conf.CENTRAL_PQ3]
            single_ivr_threshold = conf.CENTRAL_1_THRESHOLD[
                conf.SINGLE_IVR_THRESHOLD_KEY
            ]
            settings.API_LOGGER.error(
                "central_1 - invalid db thresholds: {t}, **taking default from conf**, e: {e}".format(
                    t=threshold, e=e
                )
            )

        max_workers = Helper.get_max_workers(max_workers, "central_1")

        cq_name_url = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )

        pq2_current_msg_count = SQSManager.get_queue_attributes(
            cq_name_url[conf.CENTRAL_PQ2]
        )["ApproximateNumberOfMessages"]

        pq3_current_msg_count = SQSManager.get_queue_attributes(
            cq_name_url[conf.CENTRAL_PQ3]
        )["ApproximateNumberOfMessages"]

        # Threshold for messages that can be pushed to PQs
        pq2_free_threshold = pq2_threshold - int(pq2_current_msg_count)
        pq3_free_threshold = pq3_threshold - int(pq3_current_msg_count)

        pq2_thread_threshold = ceil(
            pq2_free_threshold / max_workers
        )  # n(total threshold)/m(no of threads)
        pq3_thread_threshold = ceil(pq3_free_threshold / max_workers)

        single_ivr_thread_threshold = ceil(single_ivr_threshold / max_workers)

        return (
            c1_threshold,
            max_workers,
            pq2_thread_threshold,
            pq3_thread_threshold,
            single_ivr_thread_threshold,
        )

    @staticmethod
    def is_request_cancelled(ivr_id, request_id):
        request_status = conf.CANCELLED_REQUEST_STATUS
        response = False
        if int(conf.CANCELLATION_SERVICE_CHECK_ENABLED):
            response = ExternalAPIs.talk_to_cancellation_api(
                ivr_id, request_id
            )
            if not response:
                return response

            if response.get("detail"):
                cancel_req_data = response.get("detail")[0]
                cancel_req_status = cancel_req_data.get("request_status") or 0
                if int(cancel_req_status) == int(request_status):
                    response = True

        return response
