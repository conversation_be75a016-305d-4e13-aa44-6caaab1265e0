import json
from datetime import datetime, timed<PERSON><PERSON>
from random import shuffle

from django.conf import settings
from django.db import transaction
from django.db.models import F

from central.logging import logging_request_context, reset_uid
from central.settings.handlers import conf
from handlers.central_1.helper import Central1_Helper
from handlers.models import CentralRequestInfo, IvrInfo
from utills.cache_manager.main import CacheManager
from utills.external_apis import ExternalAPIs
from utills.helpers.helper import Helper
from utills.sqs_manager.main import SQSManager


class Central1:
    def __init__(self):
        pass

    def run(self, args):
        """
        Responsibilities:
                1. Get IVRs to process from cache but ignore paused and exclude ivrs.
                2. Based on Priorities from High to Low, transfer the req from IVR_Qs to PQs.
                3. Store requests in CentralReqInfo and deletes from IVR_Qs.
                4. Delete IVR Key from Cache if msg count is 0.
        Parameters:
                args: (c1_threshold, pq2_free_threshold, pq3_free_threshold)
        Returns:
                None
        """
        c1_threshold = args[0]
        pq2_free_threshold = args[1]
        pq3_free_threshold = args[2]
        ivrs_to_process = args[3]
        single_ivr_threshold = args[4]

        cq_name_url = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )

        for p in sorted(ivrs_to_process.keys()):
            # if c1_threshold < 1:
            # 	settings.API_LOGGER.info("central_1 - handler threshold has exhausted, stopping the process...")
            # 	break
            ivrs_ = ivrs_to_process[p][:]
            if p in conf.CENTRAL_HIGH_PRIORITIES:  # pushing requests to pq2
                if pq2_free_threshold <= 0:
                    settings.API_LOGGER.info(
                        "central_1 - Ignoring priority IVRs because PQ2 threshold not available: {pq2}".format(
                            pq2=pq2_free_threshold
                        )
                    )
                    continue
                # if c1_threshold < pq2_free_threshold: pq2_free_threshold = c1_threshold
                processed_req_count = self.process_ivrs(
                    ivrs_,
                    pq2_free_threshold,
                    cq_name_url[conf.CENTRAL_PQ2],
                    conf.CENTRAL_PQ2_COUNT_KEY,
                    single_ivr_threshold,
                )
                pq2_free_threshold = pq2_free_threshold - processed_req_count
                # c1_threshold = c1_threshold - processed_req_count
            elif p not in conf.CENTRAL_HIGH_PRIORITIES:
                if pq3_free_threshold <= 0:
                    settings.API_LOGGER.info(
                        "central_1 - Ignoring non priority IVRs because PQ3 threshold not available: {pq3}".format(
                            pq3=pq3_free_threshold
                        )
                    )
                    break
                # if c1_threshold < pq3_free_threshold: pq3_free_threshold = c1_threshold
                processed_req_count = self.process_ivrs(
                    ivrs_,
                    pq3_free_threshold,
                    cq_name_url[conf.CENTRAL_PQ3],
                    conf.CENTRAL_PQ3_COUNT_KEY,
                    single_ivr_threshold,
                )
                pq3_free_threshold = pq3_free_threshold - processed_req_count
                # c1_threshold =  c1_threshold - processed_req_count

    def process_ivrs(
        self,
        ivrs,
        threshold,
        pq_url,
        q_count_key,
        single_ivr_threshold,
        old_processed=0,
        ivrs_processed_req_count=None,
    ):
        """
        processing all IVRs of the same priority, after shuffling,
        recursion will be called if the threshold is still not exhausted and IVR keys are present in the cache.
        Parameters:
                IVRs: [ivr1, ivr2], threshold: 50, pq_url: URL
        Returns:
                Recursion, with remaining threshold and remaining IVRs
        Recursion Condition:
                1. If the threshold is exhausted.
                2. IVRs list of input param is empty.
                Note: Removing IVRs from the list if:
                        1. IVR key not found in the cache.
                        2. If rule not found.
                        3. If rule found but can't make a call in current time.
        """
        log_msg = "central_1 - inside process_ivrs - ivrs: {ivrs}, available threshold: {threshold}, old_processed: {op}".format(
            ivrs=ivrs, threshold=threshold, op=old_processed
        )
        Helper.sampling_logger_emitter(
            msg=log_msg, sampling_probability=0.01, is_enable=True
        )
        if not ivrs or threshold < 1:
            processed = old_processed
            log_msg1 = "central_1 - requests processed for one process_ivrs cycle: {processed}, threshold: {t}".format(
                processed=processed, t=threshold
            )
            Helper.sampling_logger_emitter(
                msg=log_msg1, sampling_probability=0.1, is_enable=True
            )
            return processed

        shuffle(ivrs)

        for ivr_id in ivrs:
            with logging_request_context(
                ivr_id=ivr_id,
            ):
                limit = 10
                if threshold < 1:
                    break
                if threshold < 10:
                    limit = threshold  # number of messages that can be processed is < 10

                # restricting requests count for an IVR.
                if not ivrs_processed_req_count:
                    ivrs_processed_req_count = {}
                if ivr_id not in ivrs_processed_req_count:
                    ivrs_processed_req_count[ivr_id] = 0

                if (
                    single_ivr_threshold - ivrs_processed_req_count[ivr_id]
                ) < limit:
                    limit = (
                        single_ivr_threshold - ivrs_processed_req_count[ivr_id]
                    )

                if limit <= 0:
                    ivrs.remove(ivr_id)
                    continue

                ivr_info = IvrInfo.objects.filter(ivr_id=ivr_id).first()
                if ivr_info.ivr_type == IvrInfo.IVR:
                    on_hold_req_count = CentralRequestInfo.objects.filter(
                        ivr_id=ivr_id,
                        is_onhold_req=CentralRequestInfo.ENTER,
                        is_req_completed=CentralRequestInfo.NO,
                        is_enable=CentralRequestInfo.YES,
                    ).count()
                    if (single_ivr_threshold - on_hold_req_count) < limit:
                        limit = single_ivr_threshold - on_hold_req_count

                    if limit <= 0:
                        ivrs.remove(ivr_id)
                        log_msg2 = "ignoring ivr: {ivr_id} because requests({r}) are greater than single_ivr_threshold({sit}) present in OnHoldCirculator".format(
                            ivr_id=ivr_id,
                            r=on_hold_req_count,
                            sit=single_ivr_threshold,
                        )
                        Helper.sampling_logger_emitter(
                            msg=log_msg2,
                            sampling_probability=0.2,
                            is_enable=True,
                        )
                        continue

                queue_name = f"{conf.CACHE_IVR_KEY_NAME}{ivr_id}"

                if not CacheManager.get_value(queue_name):
                    log_msg = "central_1 - ignoring ivr, {queue_name} not found in cache".format(
                        queue_name=queue_name
                    )
                    Helper.sampling_logger_emitter(
                        msg=log_msg, sampling_probability=0.5, is_enable=True
                    )
                    # safe side condition
                    # settings.API_LOGGER.info("central_1 - ignoring ivr, {queue_name} not found in cache".format(queue_name=queue_name))
                    ivrs.remove(ivr_id)
                    continue

                ivr_queue_url = Helper.get_queue_name_url_mapping(
                    "queue_name", queue_name, conf.CENTRAL_IVRQ_GATEWAY_PREFIX
                )

                if not ivr_queue_url.get(queue_name):
                    settings.API_LOGGER.info(
                        "central_1 - couldn't fetch the queue_url with queue_name - {queue_name} from SQS for ivr - {ivr_id}".format(
                            ivr_id=ivr_id, queue_name=queue_name
                        )
                    )
                    continue

                # Rule Validation
                ivr_rule = ExternalAPIs.talk_to_ivr_rule_api(ivr_id)
                if not ivr_rule:
                    ivrs.remove(ivr_id)
                    continue

                # SQS Short polling
                requests = SQSManager.fetch_message_sp(
                    ivr_queue_url[queue_name], limit, False
                )

                if not requests:
                    log_msg_ = "central_1 - no request found in IVR queue for - ivr_id: {ivr_id}".format(
                        ivr_id=ivr_id
                    )
                    Helper.sampling_logger_emitter(
                        msg=log_msg_, sampling_probability=0.1, is_enable=True
                    )
                    # settings.API_LOGGER.info("central_1 - no request found in IVR queue for - ivr_id: {ivr_id}".format(ivr_id=ivr_id))
                    ivrs.remove(ivr_id)
                    continue

            # storing msg to db, sending to PQ and deleting from IVR_Q.
            for req in requests:
                threshold = threshold - 1
                old_processed = old_processed + 1
                ivrs_processed_req_count[ivr_id] += 1
                req_body = json.loads(req["Body"])
                with logging_request_context(
                    ivr_id=req_body["ivr_id"],
                    request_id=req_body["request_id"],
                    company_id=req_body["company_id"],
                ):
                    ivr_info = IvrInfo.objects.filter(
                        ivr_id=req_body["ivr_id"]
                    ).first()

                    # Dial Reverse Case:
                    if "obdv2_call_direction" in ivr_info.common_setting:
                        dial_reverse = 0
                        if (
                            ivr_info.common_setting["obdv2_call_direction"]
                            == "agent"
                        ):
                            dial_reverse = 1

                        req_body["dial_reverse"] = dial_reverse
                        req["Body"] = json.dumps(req_body)

                    is_req_cancelled = Central1_Helper.is_request_cancelled(
                        req_body["ivr_id"], req_body["request_id"]
                    )

                    if not is_req_cancelled:
                        insert_code = Central1.add_central_request_info(req)
                        if insert_code == 2:  # rds failed to write
                            continue

                        if (
                            insert_code == 1
                        ):  # successfully insertion by new request or process flow or onhold circulation ivr timing failed
                            SQSManager.send_message_to_sqs(pq_url, req["Body"])
                            CacheManager.incr_value(q_count_key)
                    else:
                        settings.API_LOGGER.info(
                            "central_1 - request is globally cancelled - request_id: {req_id}, ivr_id: {ivr_id}, req_body: {body}, deleting the request from ivr_queue!".format(
                                req_id=req_body["request_id"],
                                ivr_id=ivr_id,
                                body=req_body,
                            )
                        )
                        pm_response = Helper.make_pm_response(
                            "call", "request is globally cancelled!", ""
                        )
                        Helper.invoke_process_manager(
                            req_body["request_id"],
                            req_body["company_id"],
                            req_body["ivr_id"],
                            pm_response,
                            200,
                            conf.OBD_SERVICES["cancel_service"],
                        )

                    SQSManager.sqs_delete_message(
                        ivr_queue_url[queue_name], req["ReceiptHandle"]
                    )
                    CacheManager.decr_value(queue_name, True)
            reset_uid()

        return self.process_ivrs(
            ivrs,
            threshold,
            pq_url,
            q_count_key,
            single_ivr_threshold,
            old_processed,
            ivrs_processed_req_count,
        )

    @staticmethod
    def add_central_request_info(req):
        """
        Stores the request in CentralReqInfo, if already present then create new and also increase the iteration count of all.
        Parameters:
                req: sqs req_body
        Returns:
                False
        """
        try:
            return_codes = (1, 2, 3, 4)
            # 1=inserted(propagate req), 2=insertion_failed=(process in next execution) 3=duplicate_req(dont propagate req), 4=expired request

            body = json.loads(req["Body"])
            current_iteration = 1

            old_reqs = CentralRequestInfo.objects.filter(
                request_id=body["request_id"]
            ).order_by("added_on")

            if old_reqs.exists():
                last_old_req = old_reqs.last()

                if (
                    last_old_req.is_req_completed == CentralRequestInfo.YES
                    and last_old_req.completion_event_type
                    == CentralRequestInfo.FROM_EXPIRY_MANAGER
                ):
                    # ignoring request as it is expired.
                    return return_codes[3]

                if (
                    last_old_req.is_onhold_req
                    == CentralRequestInfo.IVR_TIMING_FAIL
                ):
                    # Request Failed from ON_HOLD_CIRCULATOR because of rule api
                    last_old_req.is_onhold_req = CentralRequestInfo.NA
                    last_old_req.is_enable = CentralRequestInfo.YES
                    last_old_req.save(
                        update_fields=[
                            "is_onhold_req",
                            "is_enable",
                            "updated_on",
                        ]
                    )

                    settings.API_LOGGER.info(
                        "central_1 - marking is_onhold_req=NA, from IVR_TIMING_FAIL, request_id: {req_id}, ivr_id: {ivr_id}".format(
                            req_id=body["request_id"], ivr_id=body["ivr_id"]
                        )
                    )
                    return return_codes[
                        0
                    ]  # process request without new entry in central_req_info as it is already present

                if last_old_req.is_req_completed == CentralRequestInfo.NO:
                    # delete duplicate request without propagation[logic aws-lambda unexpected invocation]
                    return return_codes[2]

                # new entry because of process_flow
                central_req_update_iteration = old_reqs.invalidated_update(
                    iteration=F("iteration") + 1, updated_on=datetime.now()
                )
                current_iteration = old_reqs.first().iteration

            call_expiry = Helper.get_ivrinfo_common_setting(
                body["ivr_id"], "call_expiry", conf.DEFAULT_CALL_EXPIRY
            )

            with transaction.atomic():
                CentralRequestInfo.objects.create(
                    request_id=body["request_id"],
                    ivr_id=body["ivr_id"],
                    c_id=body["company_id"],
                    request_type=body["type"],
                    user_id=body.get("user_id", None),
                    raw_data=req["Body"],
                    iteration=current_iteration,
                    expired_at=datetime.now() + timedelta(seconds=call_expiry),
                    job_id=body.get("job_id", None),
                )

            settings.API_LOGGER.info(
                "central_1 - inserting request in CentralRequestInfo - request_id: {req_id}, ivr_id: {ivr_id}, raw_data: {rd}".format(
                    req_id=body["request_id"], ivr_id=body["ivr_id"], rd=body
                )
            )
            return return_codes[0]
        except Exception as e:
            settings.API_LOGGER.error(
                "central_1 - Unable to insert request to central_request_info table, e: {e}.".format(
                    e=e
                )
            )
        return return_codes[1]
