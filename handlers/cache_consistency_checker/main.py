from central.settings.handlers import conf
from handlers.models import CentralRequestInfo, OnHoldUserTracing
from utills.cache_manager.main import CacheManager
from utills.shared_cache_manager.main import SharedCacheManager
from utills.sqs_manager.main import SQSManager


class CacheConsistencyChecker:
	def run(self):
		print("Enter 1 to check consistency of IVR Queues.")
		print("Enter 2 to check consistency of Group Queues.")
		print("Enter 3 to check consistency of All Other Queues.")
		print("Enter 4 to check consistency of CentralRequestInfo & OnHoldUserTracing")
		print("Enter any other number to check consistency of All Queues.")
		try:
			input_type = int(input("Please enter a number: "))
			if input_type == 1:
				self.process_type1()
			elif input_type ==  2:
				self.process_type2()
			elif input_type == 3:
				self.process_type3()
			elif input_type == 4:
				self.process_input_4()
			else:
				self.process_type1()
				self.process_type2()
				self.process_type3()
		except ValueError as e:
			print("Please enter a valid number!")
		except Exception as e:
			print("Some Error Occured!, e: {e}".format(e=e))

	def process_type1(self):
		""" Check Consistency of TYPE1 Queues (IVR Queues - QueueName and CacheKeyName is same and NamePrefix is static) """
		names_list = conf.CHECK_LIST.get('TYPE1')
		for name_prefix in names_list:
			queue_urls = SQSManager.get_queue_urls(name_prefix)
			if not queue_urls: continue
			for queue_url in queue_urls:
				name = queue_url.split(name_prefix)[-1]
				self.check_consistency(queue_url, "{np}{n}".format(np=name_prefix, n=name))

	def process_type2(self):
		""" Check Consistency of TYPE2 Queues (Group Queues - QueueName and CacheKeyName is same) """
		names_list = conf.CHECK_LIST.get('TYPE2')
		for queue_name in names_list:
			queue_urls = SQSManager.get_queue_urls(queue_name)
			if not queue_urls: continue
			for queue_url in queue_urls:
				self.check_consistency(queue_url, queue_url, True)

	def process_type3(self):
		""" Check Consistency of TYPE3 Queues (QueueName and CacheKeyName are different) """
		cache_queue_dict = conf.CHECK_LIST.get('TYPE3')
		for cache_key, queue_name in cache_queue_dict.items():
			queue_urls = SQSManager.get_queue_urls(queue_name)
			if not queue_urls or not isinstance(queue_urls, list):
				print("Unable to fetch Queue URL, QueueName: {n}, CacheKey: {c}".format(n=queue_name, c=cache_key))
				continue
			self.check_consistency(queue_urls[0], cache_key)

	def process_input_4(self):
		user_tracing_qs = OnHoldUserTracing.objects.all().values_list('user_id', 'ivr_id')

		if not user_tracing_qs:
			print("No requests found in OnHoldUserTracing")

		for user_trace in user_tracing_qs:
			disabled_req_count = CentralRequestInfo.objects.filter(
				ivr_id=user_trace[1],
				is_req_completed=0,
				is_enable=0,
				is_onhold_req=1,
				user_id=user_trace[0]
			).count()
			updated_user_trace = OnHoldUserTracing.objects.filter(user_id=user_trace[0], ivr_id=user_trace[1]).last()

			print("Checking consistency for ivr_id: {ivr_id}, user_id: {user_id}".format(user_id=user_trace[0], ivr_id=user_trace[1]))

			if not updated_user_trace and disabled_req_count:
				print("No entry found in OnHoldUserTracing for ivr_id: {ivr_id}, user_id: {user_id}, but CentralRequestInfo has {drc} disabled requests.".format(user_id=user_trace[0], ivr_id=user_trace[1], drc=disabled_req_count))

			if updated_user_trace and updated_user_trace.count_disabled_req != disabled_req_count:
				print("Inconsistency in disable req count of CentralRequestInfo({drc1}) and OnHoldUserTracing({drc2}) for ivr_id: {ivr_id}, user_id: {user_id}".format(user_id=user_trace[0], ivr_id=user_trace[1], drc1=disabled_req_count, drc2=updated_user_trace.count_disabled_req))

	def check_consistency(self, queue_url, cache_key, is_shared=False):
		try:
			print("######## Processing - CacheKey: {key}, Queue: {url} #########".format(key=cache_key, url=queue_url))
			cache_msg_count = self.get_cache_count(cache_key, is_shared)
			queue_msg_count = SQSManager.get_queue_attributes(queue_url)['ApproximateNumberOfMessages']
			print("CacheMsgCount: {cc}, QueueMsgCount: {qc}".format(cc=cache_msg_count, qc=queue_msg_count))

			if int(cache_msg_count) != int(queue_msg_count):
				print("-----> Alert: CacheMsgCount and QueueMsgCount are not same, checking once again...")
				cache_msg_count = self.get_cache_count(cache_key, is_shared)
				queue_msg_count = SQSManager.get_queue_attributes(queue_url)['ApproximateNumberOfMessages']
				print("CacheMsgCount: {cc}, QueueMsgCount: {qc}".format(cc=cache_msg_count, qc=queue_msg_count))
		except Exception as e:
			print("-----> Error Occured: {e}".format(e=e))

	def get_cache_count(self, key, is_shared=False):
		if is_shared:
			return SharedCacheManager.get_value(key) or 0
		return CacheManager.get_value(key) or 0