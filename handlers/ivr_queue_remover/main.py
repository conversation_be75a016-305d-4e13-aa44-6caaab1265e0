from datetime import datetime, timed<PERSON>ta

from django.conf import settings

from central.settings.handlers import conf
from handlers.models import CentralRequestInfo, IvrInfo, SQSQueueInfo
from utills.cache_manager.main import CacheManager
from utills.sqs_manager.main import SQSManager


class IVRQueueRemover:
    def run(self):
        """
        Responsibilities:
                1. Delete all those IVR Queues that have not been used for a long time.
        """
        inactive_ivrs = self.get_inactive_ivrs()
        for ivr in inactive_ivrs:
            ivr_queue_name = f"{conf.CACHE_IVR_KEY_NAME}{ivr}"
            if CacheManager.get_value(ivr_queue_name):
                settings.API_LOGGER.error(
                    "ivr_queue_remover - cache key exist for ivr: {ivr}".format(
                        ivr=ivr
                    )
                )
                continue

            sqs_info_obj = SQSQueueInfo.objects.filter(
                queue_name=ivr_queue_name
            )
            if not sqs_info_obj.exists():
                continue

            sqs_info = sqs_info_obj.last()
            try:
                SQSManager.sqs_delete_queue(sqs_info.queue_url)
                settings.API_LOGGER.info(
                    "ivr_queue_remover - deleting ivr_queue: {ivr_queue}".format(
                        ivr_queue=sqs_info.queue_url
                    )
                )
                sqs_info.delete()
            except Exception as e:
                settings.API_LOGGER.error(
                    "ivr_queue_remover - error while deleting ivr_queue: {ivr_queue}, e: {e}".format(
                        ivr_queue=sqs_info.queue_url, e=e
                    )
                )

    def get_inactive_ivrs(self):
        """
        Responsibilities:
                1. Returns list of inactive IVRs
        """
        active_ivrs = (
            CentralRequestInfo.objects.filter(
                added_on__date__gte=datetime.today().date()
                - timedelta(days=int(conf.IVR_QUEUE_INACTIVITY_CHECK_DUR))
            )
            .values_list("ivr_id", flat=True)
            .distinct()
        )

        inactive_ivrs = IvrInfo.objects.exclude(
            ivr_id__in=list(active_ivrs)
        ).values_list("ivr_id", flat=True)
        return list(inactive_ivrs)
