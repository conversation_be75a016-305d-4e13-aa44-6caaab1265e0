from django.conf import settings

from central.settings.handlers import conf
from handlers.models import Group
from utills.cache_manager.main import CacheManager
from utills.helpers.helper import Helper
from utills.sqs_manager.main import SQSManager


class GPQueueAvailabilityChecker:
    @classmethod
    def process(cls):
        """
        Checks the free resources available for all groups, based on current msg count and given thresholds.
        Add these data to redis that will be used by central_2.
        data:
                key:        value
                g1:p_q:     10
                g1:np_q:    20
                g2:p_q:     10
                g2:np_q:    20
                total_priority_free_resources:     20
                total_non_priority_free_resources: 40
        Parameters:
                None
        Returns:
                False
        """

        free_resources = {}
        total_pq = 0
        total_npq = 0
        group_queues_mapping = (
            GPQueueAvailabilityChecker.get_group_assigned_queues()
        )

        for group, data in group_queues_mapping.items():
            free_resources[group] = {}
            for key, value in data.items():
                msg_count = SQSManager.get_queue_attributes(value["url"])[
                    "ApproximateNumberOfMessages"
                ]
                aval_resource = int(value["threshold"]) - int(msg_count)
                if key == conf.GROUPS_NPQ_KEY:  # np_q
                    total_npq = total_npq + aval_resource
                    CacheManager.set_value(
                        f"{group}:np_q", aval_resource, conf.CACHE_GQA_TTL
                    )
                else:
                    total_pq = total_pq + aval_resource
                    CacheManager.set_value(
                        f"{group}:p_q", aval_resource, conf.CACHE_GQA_TTL
                    )

        CacheManager.set_value(
            conf.CACHE_PQ_FREE_RESOURCES, total_pq, conf.CACHE_GQA_TTL
        )
        CacheManager.set_value(
            conf.CACHE_NPQ_FREE_RESOURCES, total_npq, conf.CACHE_GQA_TTL
        )

        settings.API_LOGGER.info(
            "group_queue_availability - sleeping for {sleep}s".format(
                sleep=conf.GQA_SLEEP_DUR
            )
        )
        Helper.exit_handler(conf.GQA_SLEEP_DUR)

    @staticmethod
    def get_group_assigned_queues():
        """
        Returns group mapping with its pq and npq name, url and threshold.
        Parameters:
                None
        Returns:
                dict: {'g1': {'p_q': {'name': 'obd-central-group-pq-gname-g1', 'threshold': 10, 'url': 'https://ap-south-1.queue.amazonaws.com/472952060482/obd-central-group-pq-gname-g1'}, 'np_q': {'name': 'obd-central-group-npq-gname-g1', 'threshold': 20, 'url': 'https://ap-south-1.queue.amazonaws.com/472952060482/obd-central-group-npq-gname-g1'}}}
        """

        group_urls_mapping = {}
        group_queue_names_mapping = {}

        groups = Group.objects.filter(is_enable="1").all()
        # groups = [{'name': 'g1', 'assigned_queues': '{"p_q": { "name": "obd-central-group-pq-gname-g1", "threshold": 10 , "url": "https://sqs.ap-south-1.amazonaws.com/472952060482/obd-central-group-pq-gname-g1"} "np_q": { "name": "obd-central-group-npq-gname-g1", "threshold": 20, "url": "https://sqs.ap-south-1.amazonaws.com/472952060482/obd-central-group-npq-gname-g1" }}'}]

        for group in groups:
            assigned_queues = group.get_assigned_queues
            group_queue_names_mapping[group.name] = {}
            for key, value in assigned_queues.items():
                group_queue_names_mapping[group.name][key] = value
        return group_queue_names_mapping
