import json
import time
import traceback
from datetime import datetime

from django.conf import settings
from django.db import transaction

from central.settings.handlers import conf
from handlers.models import CompanyUsage
from utills.cache_manager.main import CacheManager
from utills.helpers.helper import Helper
from utills.sqs_manager.main import SQSManager


class CompanyUsageCheck:
    @classmethod
    def run(cls):  # noqa
        """
        Pulls request from company_usage_check_queue and checks
        Whether service is activated , dectivated or if a feature is chnaged against a company.
        source_events = [
                1=>Service goes live from demo,
                2=>Service got suspended,
                3=>Service deactivated,
                4=>Service deactivated from suspended state,
                5=>Service resumed,
                6=>Service activated,
                7=>Feature change,
                8=>Package change
        ]
        feature_change(7) = [
                1=>Changes in free units,
                2=>Changes in rental of this feature,
                3=>Feature deactivated,
                4=> Feature activated
        ]
        Parameters:
                None
        Returns:
                False
        """
        try:
            cuc_msg_count = CacheManager.get_value(
                conf.COMAPNY_USAGE_CHECK_COUNT_KEY
            )

            if not cuc_msg_count:
                log_msg = "company_usage_check - cache request count key is None, exiting after sleeping for {dur}s...".format(
                    dur=conf.COMAPNY_USAGE_CHECK_SLEEP_DUR
                )
                Helper.sampling_logger_emitter(log_msg)
                time.sleep(conf.COMAPNY_USAGE_CHECK_SLEEP_DUR)
                return

            cuc_name_url = Helper.get_queue_name_url_mapping(
                "queue_name",
                conf.COMAPNY_USAGE_CHECK_QUEUE_NAME,
                conf.COMAPNY_USAGE_CHECK_GATEWAY_PREFIX,
            )
            cuc_queue_url = cuc_name_url[conf.COMAPNY_USAGE_CHECK_QUEUE_NAME]

            messages = SQSManager.fetch_message_lp(cuc_queue_url, 10, False)

            if not messages:
                log_msg = "company_usage_check - no messages in found in {cuc_queue_url}, exiting sleeping for {dur}s...".format(
                    cuc_queue_url=cuc_queue_url,
                    dur=conf.COMAPNY_USAGE_CHECK_SLEEP_DUR,
                )
                Helper.sampling_logger_emitter(log_msg)
                return

            for message in messages:
                data_ = json.loads(message["Body"])
                data = json.loads(data_["Message"])
                settings.API_LOGGER.info(
                    "company_usage_check - data: {data}".format(data=data_)
                )

                if not data:
                    settings.API_LOGGER.error(
                        "company_usage_check - message found but body is empty in {message}, deleting req...".format(
                            message=message
                        )
                    )
                    SQSManager.sqs_delete_message(
                        cuc_queue_url, message["ReceiptHandle"]
                    )
                    CacheManager.decr_value(
                        conf.COMAPNY_USAGE_CHECK_COUNT_KEY, True
                    )
                    continue

                c_id = data.get("gsn", None)
                source_event = data.get("event", None)
                if source_event is None:
                    settings.API_LOGGER.info(
                        "company_usage_check - c_id: {c_id},  unbale ot get event deleting req: {req} ".format(
                            c_id=c_id, req=data_
                        )
                    )
                    SQSManager.sqs_delete_message(
                        cuc_queue_url, message["ReceiptHandle"]
                    )
                    CacheManager.decr_value(
                        conf.COMAPNY_USAGE_CHECK_COUNT_KEY, True
                    )
                    continue

                source_event = int(source_event)
                settings.API_LOGGER.info(
                    "company_usage_check - c_id: {c_id}, source_event: {source_event}".format(
                        c_id=c_id, source_event=source_event
                    )
                )
                if source_event in [2, 3, 4]:
                    # source_event - deactivate case - [2=>Service got suspended, 3=>Service deactivated, 4=>Service deactivated from suspended state]
                    CompanyUsageCheck.update_or_create_event(c_id, event=0)
                elif source_event == 7:
                    # source_event [7=>Feature change]
                    try:
                        feature_change_event_value = (
                            data.get("feature_changes", None)
                            .get("outgoing_v2", None)
                            .get("event", None)
                        )
                    except Exception:
                        feature_change_event_value = None

                    settings.API_LOGGER.info(
                        "company_usage_check - c_id: {c_id}, feature_change_event_value: {feature_event}".format(
                            c_id=c_id, feature_event=feature_change_event_value
                        )
                    )

                    if isinstance(feature_change_event_value, list) and (
                        3 in feature_change_event_value
                        or "3" in feature_change_event_value
                    ):
                        # feature_change [3=>Feature deactivated]
                        CompanyUsageCheck.update_or_create_event(c_id, event=0)

                    elif isinstance(feature_change_event_value, list):
                        # feature_change [1=>Changes in free units, 2=>Changes in rental of this feature, 4=> Feature activated]
                        CompanyUsageCheck.update_or_create_event(c_id, event=1)
                    # else:
                    # 	settings.API_LOGGER.info("company_usage_check - invalid data - event not found for feature change!, data: {d}, c_id: {c_id}".format(d=data, c_id=c_id))
                else:
                    # source_event [1=>Service goes live from demo, 5=>Service resumed, 6=>Service activated, 8=>Package change]
                    CompanyUsageCheck.update_or_create_event(c_id, event=1)

                settings.API_LOGGER.info(
                    "company_usage_check - deleting from queue, c_id: {c_id}".format(
                        c_id=c_id
                    )
                )
                SQSManager.sqs_delete_message(
                    cuc_queue_url, message["ReceiptHandle"]
                )
                CacheManager.decr_value(
                    conf.COMAPNY_USAGE_CHECK_COUNT_KEY, True
                )

        except Exception as e:
            settings.API_LOGGER.error(
                "company_usage_check: exception occurred: {excp}".format(
                    excp="--".join(
                        [
                            "Error:",
                            str(e),
                            str(traceback.format_exc()),
                            str(traceback.extract_stack()),
                        ]
                    )
                )
            )
            return

    @staticmethod
    def update_or_create_event(c_id, event=0):
        company_usage = CompanyUsage.objects.filter(c_id=c_id)
        if company_usage.exists():
            company_usage.invalidated_update(
                event=event, updated_on=datetime.now()
            )
        else:
            with transaction.atomic():
                CompanyUsage.objects.create(c_id=c_id, event=event)
