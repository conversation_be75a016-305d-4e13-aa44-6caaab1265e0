from typing import Dict

from django.conf import settings

from celery import shared_task

from central.logging import logging_request_context
from central.settings.handlers import conf
from utills.external_apis import True<PERSON><PERSON>r<PERSON>pi
from utills.helpers.common_settings import TrueCallerCommonSettings
from utills.helpers.helper import Helper
from utills.routing_manager.main import RoutingManger


# @shared_task(bind=True)
# def route_completed_request(self, req, res, status, service):
def route_completed_request(req, res, status, service):
    """
    Invokes process flow and process manager asynchronously
    Parameters:
            req: instance of central_request_info
            res: message for response param of PM
            status: status for PM
            service: default service name for PM
    Returns:
            None
    """
    req_dict = req.__dict__
    settings.API_LOGGER.info(
        "Req completed, request_id: {request_id}, ivr_id: {ivr_id}, res: {res}".format(
            request_id=req_dict["request_id"],
            ivr_id=req_dict["ivr_id"],
            res=res,
        )
    )

    try:
        service_name = RoutingManger.main_service_queue_router(req)
    except Exception as e:
        service_name = conf.OBD_SERVICES["cancel_service"]
        status = 500
        settings.API_LOGGER.error(
            "Error invoking main_service_queue_router, request_id: {req_id}, e: {e}.".format(
                req_id=req.request_id, e=e
            )
        )

    if service_name == conf.OBD_SERVICES["cancel_service"]:
        status = 500
        response = Helper.make_pm_response(
            service, "obd_central - process_flow not found!", res
        )
        service = service_name
    elif service_name == conf.OBD_SERVICES["complete_service"]:
        status = 200
        response = Helper.make_pm_response(
            service, "obd_central - process_flow not found!", res
        )
        service = service_name
    else:
        response = Helper.make_pm_response(service, "", res)

    Helper.invoke_process_manager(
        req.request_id, req.c_id, req.ivr_id, response, status, service
    )


def normalize_to_91_format(number: str) -> str:
    number = number.strip().replace(" ", "").replace("-", "")

    # Remove leading '+'
    if number.startswith("+"):
        number = number[1:]

    # Handle different patterns
    if number.startswith("91") and len(number) == 12:
        # Already in correct format
        return number
    elif number.startswith("0") and len(number) == 11:
        # Local format with leading 0 (e.g., 0123456789)
        return "91" + number[1:]
    elif len(number) == 10:
        # Plain 10-digit mobile number
        return "91" + number
    elif number.startswith("91") and len(number) > 12:
        # Too long — probably a mistake
        raise ValueError("Number too long after '91' prefix")
    else:
        raise ValueError(
            f"Invalid or unsupported phone number format: {number}"
        )


@shared_task
def notify_true_caller(request_data: Dict):
    with logging_request_context(
        request_id=request_data.get("request_id"),
    ):
        if not TrueCallerCommonSettings().is_enabled():
            settings.API_LOGGER.info(
                "notify_true_caller- TrueCallerApi is not enabled!!"
            )
            return

        number = request_data.get("number")  # customer
        number_cc = request_data.get("number_cc")  # customer
        did: str = request_data.get(
            "source_number"
        )  # Call will always trigger from DID, hence did is caller_number
        receiver_number = f"{number_cc}{number}"
        if not did:
            settings.API_LOGGER.info("notify_true_caller- did not found!!")
            return
        try:
            did = normalize_to_91_format(did)
        except ValueError as e:
            settings.API_LOGGER.error(
                f"notify_true_caller- Invalid did format: {did}, error: {e}"
            )
            return

        TrueCallerApi().talk_to_dial_assist_api(did, receiver_number)


@shared_task(bind=True)
def notif_group_ivr(self):
    """
    Invokes process flow and process manager asynchronously
    """
    return
    # settings.API_LOGGER.info("notif_group_ivr starting...")
    # for i in range(0, conf.NOTIF_HANDLER_RUN_COUNT):
    # 	GroupIvrinfoRelationHandler.run()
    # settings.API_LOGGER.info("notif_group_ivr ending...")


@shared_task(bind=True)
def notif_company_usage_check(self):
    """
    Invokes process flow and process manager asynchronously
    """
    return
    # settings.API_LOGGER.info("notif_company_usage_check starting...")
    # for i in range(0, conf.NOTIF_HANDLER_RUN_COUNT):
    # 	CompanyUsageCheck.run()
    # settings.API_LOGGER.info("notif_company_usage_check ending...")
