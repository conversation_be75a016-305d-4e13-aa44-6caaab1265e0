import datetime
import glob
import json
import os
import re

from django.conf import settings

from central.settings.handlers import conf
from handlers.models import CentralRequestInfo
from utills.helpers.helper import Helper
from utills.s3_manager.main import S3Manager


class S3Pusher:
    """S3Pusher is responsible for migrating data from DB to S3 bucket."""

    def __init__(self):
        """Instanciate necessary vaiables"""
        settings = Helper.get_threshold(
            conf.DB_CLEANER_ENTITY,
            conf.DB_CLEANER_SETTINGS,
            default_name=conf.DB_CLEANER_SETTINGS_NAME,
        )

        self.bucket = settings["s3_pusher"]["s3_bucket"]
        self.date_obj = self.get_previous_date(
            settings["s3_pusher"]["backup_days"]
        )
        self.grt_files_path = conf.GRT_PULLER_FILE_PATH.rstrip("/")
        self.s3_files_path = conf.S3_PUSHER_FILE_PATH.rstrip("/")

    def run(self):
        """
        Responsibilities:
                1. Reads all grt files which are older then a given date
                2. Adds corresponding data to another file to upload on S3
                3. Deletes records from DB
                4. Uploads file to S3
                5. Removed processed files
        """
        grt_files = self.get_grt_files_to_process()
        settings.API_LOGGER.info(
            "S3Pusher - grt_files to process: {file}".format(file=grt_files)
        )

        for _file in grt_files:
            s3_filename = "{path}/{name}".format(
                path=self.s3_files_path,
                name=_file.split("/")[-1].replace("grt", "central_req_info"),
            )
            settings.API_LOGGER.info(
                "S3Pusher - processing {f1} and {f2}".format(
                    f1=_file, f2=s3_filename
                )
            )
            with open(_file, "r") as grt_file_obj:
                for line in grt_file_obj:
                    try:
                        req_ids = json.loads(line)
                    except Exception as e:
                        settings.API_LOGGER.error(
                            "S3Pusher - error while reading file: {file}, invalid json data: {data} ignoring the files".format(
                                file=_file, data=line
                            )
                        )

                    cri_obj = CentralRequestInfo.objects.filter(
                        request_id__in=req_ids, added_on__lt=self.date_obj
                    ).all()

                    self.write_to_s3_file(s3_filename, cri_obj)

                    cri_obj.delete()

            self.delete_file(_file)  # grt requests file
            try:
                s3_zip_filename = self.make_zip(s3_filename)
                S3Manager.upload(s3_zip_filename, self.bucket)
                self.delete_file(s3_filename)
                self.delete_file(s3_zip_filename)
            except Exception as e:
                settings.API_LOGGER.error(
                    "S3Pusher - ubable to upload file to S3, e: {e}".format(
                        e=e
                    )
                )

    def get_previous_date(self, days):
        """Returns a date_obj which is (curret date - given number of days)"""
        previous_date = datetime.date.today() - datetime.timedelta(
            days=int(days)
        )
        return previous_date

    def get_grt_files_to_process(self):
        """Returns list of files which are less than a given date"""
        grt_files_to_process = []
        pattern = re.compile("[0-9]{2}-[0-9]{2}-[0-9]{4}")

        all_files = glob.glob(
            "{path}/{files}".format(path=self.grt_files_path, files="*.*")
        )

        for _file in all_files:
            if ".zip" in _file:
                continue
            date_str = re.findall(pattern, _file)[-1]
            date_obj = datetime.datetime.strptime(date_str, "%d-%m-%Y").date()
            if date_obj < self.date_obj:
                grt_files_to_process.append(_file)

        return grt_files_to_process

    def write_to_s3_file(self, s3_filename, cri_obj):
        """Writes DB instance to a file"""
        with open(s3_filename, "a") as f:
            for cri in cri_obj:
                f.write(str(cri.__dict__))
                f.write("\n")

    def delete_file(self, _file):
        """Deletes a given file"""
        settings.API_LOGGER.info(
            "S3Pusher - removing file: {file}".format(file=_file)
        )
        os.system("rm {name}".format(name=_file))

    def make_zip(self, source):
        """created zip and returns zip filename"""
        target = "{path}.zip".format(path=os.path.splitext(source)[0])
        command = "zip -j {target} {source}".format(
            source=source, target=target
        )  # j -> ingore junc-parent directory.
        os.system(command)
        return target
