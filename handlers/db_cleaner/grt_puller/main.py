import datetime
import json

from django.conf import settings

from central.settings.handlers import conf
from utills.external_apis import ExternalAPIs
from utills.helpers.helper import Helper


class GRTPuller:
    """G<PERSON><PERSON>ull<PERSON> is responsible for getting completed requests of a given day from GRT and stores them to a file"""

    def __init__(self):
        settings = Helper.get_threshold(
            conf.DB_CLEANER_ENTITY,
            conf.DB_CLEANER_SETTINGS,
            default_name=conf.DB_CLEANER_SETTINGS_NAME,
        )

        self.date = self.get_previous_date(
            settings["grt_puller"]["previous_day"]
        )
        self.filename = "{path}/grt_{date}.{ext}".format(
            path=conf.GRT_PULLER_FILE_PATH.rstrip("/"),
            date=self.date.replace("/", "-"),
            ext="txt",
        )

    def run(self):
        """
        Responsibilities:
                1. gets completed requests from GRT
                2. loop through page_count to call GRT again to get all completed requests of a given date
                3. stores requests in a file
        """

        completed_requests = (
            ExternalAPIs.talk_to_completed_requests_discovery_api(
                date=self.date, page=1
            )
        )
        page_count = completed_requests["page_count"]

        if not completed_requests["req_ids"]:
            settings.API_LOGGER.error(
                "GRTPuller - no requests found for date: {date}".format(
                    date=self.date
                )
            )
            return

        self.process_completed_requests(completed_requests["req_ids"])

        for page in range(2, page_count + 1):
            completed_requests = (
                ExternalAPIs.talk_to_completed_requests_discovery_api(
                    date=self.date, page=page
                )
            )
            self.process_completed_requests(completed_requests["req_ids"])

    def get_previous_date(self, days):
        """Returns a date_str, which is (curret date - given number of days)"""
        previous_date = datetime.date.today() - datetime.timedelta(
            days=int(days)
        )
        return previous_date.strftime("%d/%m/%Y")

    def process_completed_requests(self, req_ids):
        """writes given requests to a file"""
        if not req_ids:
            return

        with open(self.filename, "a") as f:
            json.dump(req_ids, f)
            f.write("\n")
