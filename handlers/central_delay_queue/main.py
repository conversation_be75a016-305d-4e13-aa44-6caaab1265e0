import json
import time

from django.conf import settings

from central.settings.handlers import conf
from utills.cache_manager.main import CacheManager
from utills.helpers.helper import Helper
from utills.sqs_manager.main import SQSManager


class CentralDelayQueue:
    @classmethod
    def process(cls):
        """
        Responsibilities:
                1. CENTRAL_DELAY_Q_PULLING
                2. PUSH_TO_IVRs_Q
                3. INCREMENT_CACHE_COUNTER
                3. DELETE_FROM_CENTRAL_Q
        Parameters:
                None
        Returns:
                False
        """

        delay_q_name_url = Helper.get_queue_name_url_mapping(
            "queue_name",
            conf.CENTRAL_DELAY_QUEUE,
            conf.CENTRAL_DELAY_QUEUE_GATEWAY_PREFIX,
        )

        delay_queue_url = delay_q_name_url[conf.CENTRAL_DELAY_QUEUE]

        delay_msg_count = CacheManager.get_value(conf.CENTRAL_DELAY_COUNT_KEY)

        if not delay_msg_count:
            time.sleep(conf.DELAY_QUEUE_SLEEP_DUR)
            return

        while delay_msg_count > 0:
            # Loop will break if no msg found in delay_queue
            requests = SQSManager.fetch_message_sp(delay_queue_url, 10, False)
            if not requests:
                log_msg = "central_delay_queue - no new request found in delay_queues, exiting after sleeping for {sleep}s...".format(
                    sleep=conf.DELAY_QUEUE_SLEEP_DUR
                )
                Helper.sampling_logger_emitter(log_msg)
                time.sleep(conf.DELAY_QUEUE_SLEEP_DUR)
                break

            for req in requests:
                req_body = json.loads(req["Body"])

                queue_name = f'{conf.CACHE_IVR_KEY_NAME}{req_body["ivr_id"]}'
                ivr_queue_name_url = Helper.get_queue_name_url_mapping(
                    "queue_name", queue_name, conf.CENTRAL_IVRQ_GATEWAY_PREFIX
                )

                try:
                    # sending to ivr queue
                    SQSManager.send_message_to_sqs(
                        ivr_queue_name_url[queue_name], req["Body"]
                    )

                    # increment cache counter
                    CacheManager.incr_value(queue_name)
                except Exception as err:
                    settings.API_LOGGER.info(
                        "central_delay_queue - Unable to send a request to ivr_queue deleting the request - queue_url: {qurl}, request_body: {rb}, e: {e}.".format(
                            qurl=ivr_queue_name_url, rb=req_body, e=err
                        )
                    )

                # deleting  from delay_queue
                SQSManager.sqs_delete_message(
                    delay_queue_url, req["ReceiptHandle"]
                )

                CacheManager.decr_value(conf.CENTRAL_DELAY_COUNT_KEY, True)
                delay_msg_count = delay_msg_count - 1
