import json
import time

from django.conf import settings

from central.logging import logging_request_context
from central.settings.handlers import conf
from handlers.models import (
    CentralRequestInfo,
    OnHoldIvrTracing,
    OnHoldRequestTracing,
    OnHoldUserTracing,
)
from utills.cache_manager.main import CacheManager
from utills.helpers.helper import Helper
from utills.sqs_manager.main import SQSManager


class OnHoldProcessor:
    @classmethod
    def process(cls):
        """
        Responsibilities:
                1. ON_HOLD_QUEUE_PULLING
                2. Update CentralReqInfo: set is_onhold_req = ENTRY
                3. Delete Req from ON_HOLD_Q
                4. Check for max repeat count of same req_id to disable it.
                5. Check for the paused IVRs.
        Parameters:
                None
        Returns:
                False
        """

        threshold = Helper.get_threshold(
            conf.ON_HOLD_ENTITY, conf.ON_HOLD_THRESHOLD
        )
        oh_q_name_url = Helper.get_queue_name_url_mapping(
            "queue_name", conf.ON_HOLD_Q_NAME, conf.ON_HOLD_Q_GATEWAY_PREFIX
        )
        oh_q_url = oh_q_name_url[conf.ON_HOLD_Q_NAME]

        on_hold_msg_count = CacheManager.get_value(conf.ON_HOLD_COUNT_KEY)
        if not on_hold_msg_count:
            log_msg = "on_hold_processor - oh-hold-count cache key not found!, sleeping for {dur}s".format(
                dur=conf.ON_HOLD_SLEEP_DUR
            )
            Helper.sampling_logger_emitter(log_msg)
            time.sleep(conf.ON_HOLD_SLEEP_DUR)
            return

        while on_hold_msg_count >= 0:
            # Loop will break if no msg found in ON_HOLD_Q
            requests = SQSManager.fetch_message_sp(oh_q_url, 10, False)
            if not requests:
                log_msg = "on_hold_processor - no requests in on_hold_queue, sleeping for {dur}s".format(
                    dur=conf.ON_HOLD_SLEEP_DUR
                )
                Helper.sampling_logger_emitter(log_msg)
                time.sleep(conf.ON_HOLD_SLEEP_DUR)
                break

            for req in requests:
                req_body = json.loads(req["Body"])
                settings.API_LOGGER.info(
                    "on_hold_processor - processing request_id: {req_id}, ivr_id: {ivr_id}".format(
                        req_id=req_body["request_id"],
                        ivr_id=req_body["ivr_id"],
                    )
                )
                with logging_request_context(
                    ivr_id=req_body["ivr_id"],
                    request_id=req_body["request_id"],
                    company_id=req_body["company_id"],
                ):
                    OnHoldProcessor.mark_is_onhold_req(
                        req_body
                    )  # Set is_on_hold_req = ENTER

                    SQSManager.sqs_delete_message(
                        oh_q_url, req["ReceiptHandle"]
                    )  # Delete Req from SQS

                    CacheManager.decr_value(conf.ON_HOLD_COUNT_KEY, True)
                    on_hold_msg_count = on_hold_msg_count - 1

                    # Check for paused IVRs
                    ivr_trace = OnHoldProcessor.get_request_from_on_hold_ivr_tracing(
                        req_body["ivr_id"]
                    )  # OnHoldIvrTracing.objects.filter(ivr_id=req_body['ivr_id'])

                    if ivr_trace.exists():
                        ivr_trace = ivr_trace.first()

                        max_ivr_threshold = Helper.get_ivrinfo_common_setting(
                            req_body["ivr_id"],
                            conf.ON_HOLD_MAX_IVR_THRESHOLD,
                            threshold[conf.ON_HOLD_MAX_IVR_THRESHOLD],
                            max_value=threshold[
                                conf.ON_HOLD_MAX_IVR_THRESHOLD
                            ],
                        )

                        if ivr_trace.count_ivr >= max_ivr_threshold:
                            settings.API_LOGGER.info(
                                "on_hold_processor - pausing ivr_id: {ivr_id}".format(
                                    ivr_id=req_body["ivr_id"]
                                )
                            )
                            OnHoldProcessor.pause_ivr(req_body["ivr_id"])

                    # Check for the repeated request
                    req_trace = OnHoldProcessor.get_request_from_on_hold_request_tracing(
                        req_body["request_id"]
                    )  # OnHoldRequestTracing.objects.filter(request_id=req_body['request_id'])

                    if req_trace.exists():
                        req_trace = req_trace.first()
                        if (
                            req_trace.count_req
                            >= threshold[conf.ON_HOLD_MAX_ATTEMPTS]
                        ):
                            settings.API_LOGGER.info(
                                "on_hold_processor - disabling request_id: {request_id}, ivr_id: {ivr_id}".format(
                                    request_id=req_body["request_id"],
                                    ivr_id=req_body["ivr_id"],
                                )
                            )
                            OnHoldProcessor.disable_req(req_body["request_id"])
                            pm_response = Helper.make_pm_response(
                                "call",
                                "obd_central - on-hold: request tracing count has exceeded the threshold",
                                "",
                            )
                            Helper.invoke_process_manager(
                                req_body["request_id"],
                                req_body["company_id"],
                                req_body["ivr_id"],
                                pm_response,
                                500,
                                conf.OBD_SERVICES["cancel_service"],
                            )

    @staticmethod
    def pause_ivr(ivr_id):
        # obd-central-paused-ivrid-{ivr1}
        CacheManager.set_value(
            f"{conf.CACHE_PAUSED_IVR_KEY_NAME}{ivr_id}",
            1,
            conf.CACHE_PAUSED_IVR_TTL,
        )

    @staticmethod
    # @file_cache.cached(timeout=300)
    def get_request_from_on_hold_request_tracing(request_id):
        return OnHoldRequestTracing.objects.filter(
            request_id=request_id
        )  # .nocache()

    @staticmethod
    # @file_cache.cached(timeout=600)
    def get_request_from_on_hold_ivr_tracing(ivr_id):
        return OnHoldIvrTracing.objects.filter(ivr_id=ivr_id)  # .nocache()

    @staticmethod
    def get_request_from_on_hold_user_tracing(user_id, ivr_id):
        return OnHoldUserTracing.objects.filter(user_id=user_id, ivr_id=ivr_id)

    @staticmethod
    def disable_req(request_id):
        try:
            central_req_obj = CentralRequestInfo.objects.filter(
                request_id=request_id
            )
            if central_req_obj.exists():
                central_req = central_req_obj.last()
                central_req.is_enable = CentralRequestInfo.NO
                central_req.is_req_completed = CentralRequestInfo.YES
                central_req.completion_event_type = CentralRequestInfo.NA
                central_req.is_onhold_req = CentralRequestInfo.EXIT
                central_req.save(
                    update_fields=[
                        "is_enable",
                        "is_req_completed",
                        "is_onhold_req",
                        "completion_event_type",
                        "updated_on",
                    ]
                )
            else:
                settings.API_LOGGER.error(
                    "on_hold_processor - request_id: {request_id}, DB data not found in CentralReqInfo".format(
                        request_id=request_id
                    )
                )
        except Exception as e:
            settings.API_LOGGER.error(
                "on_hold_processor - request_id: {request_id}, DB Error: CentralRequestInfo Error: {e}".format(
                    e=e, request_id=request_id
                )
            )

    @staticmethod
    def mark_is_onhold_req(req_data):
        try:
            central_req_obj = CentralRequestInfo.objects.filter(
                request_id=req_data["request_id"]
            ).order_by("added_on")
            if central_req_obj.exists():
                central_req = central_req_obj.last()
                if central_req.user_id and central_req.request_type == 1:
                    user_trace = (
                        OnHoldProcessor.get_request_from_on_hold_user_tracing(
                            central_req.user_id, central_req.ivr_id
                        )
                    )
                    if user_trace.exists():
                        user_trace = user_trace.first()
                        if user_trace.count_user >= 1:
                            central_req.is_enable = CentralRequestInfo.NO

                central_req.is_onhold_req = CentralRequestInfo.ENTER
                central_req.save(
                    update_fields=["is_onhold_req", "is_enable", "updated_on"]
                )
            else:
                settings.API_LOGGER.error(
                    "on_hold_processor - request_id: {request_id}, DB data not found in CentralReqInfo".format(
                        request_id=req_data["request_id"]
                    )
                )

            return central_req_obj.last()
        except Exception as e:
            settings.API_LOGGER.error(
                "on_hold_processor - request_id: {request_id}, CentralRequestInfo Error: {e}".format(
                    e=e, request_id=req_data["request_id"]
                )
            )
