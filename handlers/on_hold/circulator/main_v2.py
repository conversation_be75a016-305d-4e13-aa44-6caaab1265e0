import logging
from typing import Dict, Iterable, Union

from django.conf import settings

from central.logging import logging_cri_context
from handlers.models import CentralRequestInfo
from handlers.on_hold.circulator.main import OnHoldCirculator
from utills.exceptions import GroupUnavailableException
from utills.external_apis import ExternalAPIs
from utills.helpers.helper import Helper
from utills.helpers.pm_helpers import ProcessManagerHelper
from utills.request_handler import RequestHandler
from utills.routing_manager.main import RoutingManger
from utills.shared_cache_manager.main import SharedCacheManager
from utills.udc_helper import UDCHelper

logger = logging.getLogger(__name__)


class OnHoldCirculator_V2(OnHoldCirculator):
    def __init__(self, threshold):
        self.threshold = threshold
        self.request_handler = RequestHandler()
        self.pm_helper = ProcessManagerHelper()

    def run(self, requests: Iterable[CentralRequestInfo]):
        for request in requests:
            cri_obj = CentralRequestInfo.objects.filter(pk=request.pk).last()
            if cri_obj:
                self.process_request(cri_obj)
            else:
                logger.error(
                    f"on_hold_circulator-cri_obj not found in DB, request_id: {request.request_id}"
                )

    def abort_if_job_is_aborted(self, cri_obj: CentralRequestInfo) -> bool:
        if cri_obj.job_id and Helper.check_job_is_aborted(cri_obj.job_id):
            self.mark_request_aborted(cri_obj, cri_obj.body)
            return True
        return False

    def ivr_timing_fail_if_ivr_rule_is_not_valid(
        self, cri_obj: CentralRequestInfo
    ) -> bool:
        ivr_rule = ExternalAPIs.talk_to_ivr_rule_api(cri_obj.ivr_id, False)
        if not ivr_rule:
            # sending request to service router because rule of ivr failed for current_time
            logger.info("on_hold_circulator - marking on hold ivr_timing_fail")
            cri_obj.mark_ivr_timing_failed()
            RoutingManger.dynamic_target_service_queue_push(
                settings.OBD_SERVICES["source_name"], cri_obj
            )
            return True
        return False

    def pre_checks_success(self, cri_obj: CentralRequestInfo) -> bool:
        return not self.abort_if_job_is_aborted(
            cri_obj
        ) and not self.ivr_timing_fail_if_ivr_rule_is_not_valid(cri_obj)

    def process_request(self, cri_obj: CentralRequestInfo):
        with logging_cri_context(cri_obj, is_reset_uid=True):
            if not self.pre_checks_success(cri_obj):
                return
            self.request_handler.setup(cri_obj)

            try:
                self.request_handler.process(
                    self.on_udc_unavailable,
                    self.on_destination_success,
                    self.on_destination_failure,
                )
            except GroupUnavailableException as error:
                if error.cancel_request:
                    self.cancle_request(cri_obj, cri_obj.body)

    def on_udc_unavailable(self, cri_obj):
        self.update_udc_check_availibilty_cnt(cri_obj)

    def on_destination_success(
        self,
        udc_helper: UDCHelper,
        cri_obj: CentralRequestInfo,
        response: Union[Dict, str],
    ):
        udc_helper.set_request_in_cache()
        self.pm_helper.invoke_process_manager_with_api_response(
            cri_obj, response
        )
        self.update_is_on_hold_req_exit(cri_obj)

    def on_destination_failure(
        self,
        cri_obj: CentralRequestInfo,
        response: Union[Dict, str],
        status_code: str,
    ):
        # add entry in shared_cache
        canceled_request_key = "{version}{prefix}{key}".format(
            version=settings.REDIS_VERSION_KEY,
            prefix=settings.CANCELED_REQUEST_KEY,
            key=cri_obj.request_id,
        )
        SharedCacheManager.set_value(
            canceled_request_key, 1, settings.CANCELED_REQUESTS_KEY_TTL
        )

        self.pm_helper.invoke_process_manager_with_api_response(
            cri_obj, response, status_code, success=False
        )
