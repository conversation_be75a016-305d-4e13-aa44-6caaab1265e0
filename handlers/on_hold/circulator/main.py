import json
from datetime import datetime

from django.conf import settings
from django.db.models import F

from central.settings.handlers import conf
from handlers.models import CentralRequestInfo
from utills.exceptions import UDCLockedException, UDCUnavailable
from utills.external_apis import ExternalAPIs
from utills.group_queues_pusher.main import GroupQueuesPusher
from utills.group_queues_routing_manager.main import GroupQueuesRoutingManager
from utills.helpers.helper import Helper
from utills.routing_manager.main import RoutingManger
from utills.udc_helper import UDCHelper


class OnHoldCirculator:
    def __init__(self, threshold):
        self.threshold = threshold
        self.udc_helper: UDCHelper = None

    def set_udc_helper(self, instance: CentralRequestInfo):
        self.udc_helper = UDCHelper(instance)

    def run(self, req_to_process):  # noqa C901
        """
        Responsibilities:
                1. Pick the on_hold requests from CentralRequestInfo.
                        1.1. is_completed = NO, is_enable = YES, is_onhold_request=ENTER.
                        1.2. if onhold_udc_check_availibilty_cnt >=  1, updated_on < now() - ON_HOLD_DUPLICATE_DELAY
                2. For each request check UDC API.
                3. if UDC is True: send to PPQ and mark EXIT on is_onhold_req.
                4. if UDC is False: increment onhold_udc_check_availibilty_cnt by 1.
                5. if onhold_udc_check_availibilty_cnt > ON_HOLD_MAX_USER_CHECK_COUNT, then disable the request.
        Parameters:
                None
        Returns:
                False
        """
        if not req_to_process:
            return

        for req in req_to_process:
            body = json.loads(req.raw_data)

            if req.job_id and Helper.check_job_is_aborted(req.job_id):
                self.mark_request_aborted(req, body)
                continue

            ivr_rule = ExternalAPIs.talk_to_ivr_rule_api(req.ivr_id, False)
            if not ivr_rule:
                # sending request to service router because rule of ivr failed for current_time
                self.update_ivr_timing_fail(req)
                RoutingManger.dynamic_target_service_queue_push(
                    conf.OBD_SERVICES["source_name"], req
                )
                continue

            group = GroupQueuesRoutingManager().assign_group(body)

            if not group:
                settings.API_LOGGER.info(
                    "on_hold_circulator - request_id: {request_id}, ivr_id: {ivr_id}, ignoring requests because of group unavailability!".format(
                        ivr_id=body["ivr_id"], request_id=body["request_id"]
                    )
                )
                continue

            if "is_cancle_request" in group:
                settings.API_LOGGER.info(
                    "on_hold_circulator - request_id: {request_id}, ivr_id: {ivr_id}, canceling request because of group not found!".format(
                        ivr_id=body["ivr_id"], request_id=body["request_id"]
                    )
                )
                self.cancle_request(req, body)
                continue

            toggle_view_only = Helper.get_toggle_view_only_value(req.ivr_id)

            try:
                self.set_udc_helper(req)
                self.udc_helper.udc_checks(user_view_only=toggle_view_only)
            except UDCUnavailable as error:
                self.update_udc_check_availibilty_cnt(req)
                continue

            except UDCLockedException as error:
                settings.API_LOGGER.info(
                    "on_hold_circulator - request_id: {req_id}, ivr_id: {ivr_id}, message: {m}".format(
                        req_id=req.request_id,
                        ivr_id=req.ivr_id,
                        m=error.response.get("message"),
                    ),
                    extra={"status_code": error.response.get("status")},
                )
                continue

            GroupQueuesPusher().push_to_group_queue(
                "on_hold_circulator", body, group, self.udc_helper
            )

            self.update_is_on_hold_req_exit(req)

    def update_udc_check_availibilty_cnt(self, req):
        settings.API_LOGGER.info(
            "on_hold_circulator - udc not available for request_id: {req_id}, ivr_id: {ivr_id}, udc_check_availibilty_cnt: {count}".format(
                req_id=req.request_id,
                ivr_id=req.ivr_id,
                count=req.onhold_udc_check_availibilty_cnt + 1,
            )
        )
        central_req_obj = CentralRequestInfo.objects.filter(id=req.id)
        max_user_check_count = Helper.get_ivrinfo_common_setting(
            req.ivr_id,
            conf.ON_HOLD_MAX_USER_CHECK_COUNT,
            self.threshold[conf.ON_HOLD_MAX_USER_CHECK_COUNT],
            max_value=self.threshold[conf.ON_HOLD_MAX_USER_CHECK_COUNT],
        )

        if (
            req.onhold_udc_check_availibilty_cnt
            >= int(max_user_check_count) - 1
        ):
            # Disable the request.
            settings.API_LOGGER.info(
                "on_hold_circulator - disabling the request_id: {req_id}, ivr_id: {ivr_id},udc_check_availibilty_cnt: {count}".format(
                    req_id=req.request_id,
                    ivr_id=req.ivr_id,
                    count=req.onhold_udc_check_availibilty_cnt + 1,
                )
            )

            if central_req_obj.exists():
                central_req = central_req_obj.last()
                central_req.onhold_udc_check_availibilty_cnt = 0
                central_req.is_enable = CentralRequestInfo.NO
                central_req.is_onhold_req = CentralRequestInfo.EXIT
                central_req.is_req_completed = CentralRequestInfo.YES

                central_req.save(
                    update_fields=[
                        "is_req_completed",
                        "is_onhold_req",
                        "onhold_udc_check_availibilty_cnt",
                        "is_enable",
                        "updated_on",
                    ]
                )

                pm_response = Helper.make_pm_response(
                    "call",
                    "obd_central - on-hold: UDC check count has exceeded threshold",
                    "",
                )
                Helper.invoke_process_manager(
                    req.request_id,
                    req.c_id,
                    req.ivr_id,
                    pm_response,
                    500,
                    conf.OBD_SERVICES["cancel_service"],
                )
        else:
            if central_req_obj.exists():
                central_req_obj.invalidated_update(
                    onhold_udc_check_availibilty_cnt=F(
                        "onhold_udc_check_availibilty_cnt"
                    )
                    + 1,
                    updated_on=datetime.now(),
                )

    def update_is_on_hold_req_exit(self, req):
        settings.API_LOGGER.info(
            "on_hold_circulator - marking on hold exit, request_id: {req_id}, ivr_id: {ivr_id}".format(
                req_id=req.request_id, ivr_id=req.ivr_id
            )
        )
        central_req_obj = CentralRequestInfo.objects.filter(id=req.id)
        if central_req_obj.exists():
            central_req = central_req_obj.last()
            central_req.is_onhold_req = CentralRequestInfo.EXIT
            central_req.save(update_fields=["is_onhold_req", "updated_on"])

        return central_req_obj.last()

    def update_ivr_timing_fail(self, req):
        settings.API_LOGGER.info(
            "on_hold_circulator - marking on hold ivr_timing_fail, request_id: {req_id}, ivr_id: {ivr_id}".format(
                req_id=req.request_id, ivr_id=req.ivr_id
            )
        )
        central_req_obj = CentralRequestInfo.objects.filter(id=req.id)
        if central_req_obj.exists():
            central_req = central_req_obj.last()
            central_req.is_onhold_req = CentralRequestInfo.IVR_TIMING_FAIL
            central_req.onhold_udc_check_availibilty_cnt = 0
            central_req.is_enable = CentralRequestInfo.NO
            central_req.save(
                update_fields=[
                    "is_onhold_req",
                    "onhold_udc_check_availibilty_cnt",
                    "updated_on",
                ]
            )

        return central_req_obj.last()

    def cancle_request(self, req, body):
        central_req_obj = CentralRequestInfo.objects.filter(id=req.id)
        if central_req_obj.exists():
            central_req = central_req_obj.last()
            central_req.is_onhold_req = CentralRequestInfo.EXIT
            central_req.is_req_completed = CentralRequestInfo.YES
            central_req.is_enable = CentralRequestInfo.NO
            central_req.save(
                update_fields=[
                    "is_onhold_req",
                    "is_req_completed",
                    "is_enable",
                    "updated_on",
                ]
            )

        pm_response = Helper.make_pm_response(
            "call", "on_hold_circulator group not found!", ""
        )
        Helper.invoke_process_manager(
            body["request_id"],
            body["company_id"],
            body["ivr_id"],
            pm_response,
            "404",
            conf.OBD_SERVICES["cancel_service"],
        )

    def mark_request_aborted(self, req, body):
        central_req_qs = CentralRequestInfo.objects.filter(id=req.id)
        if central_req_qs.exists():
            central_req = central_req_qs.last()
            central_req.is_onhold_req = CentralRequestInfo.EXIT
            central_req.is_enable = CentralRequestInfo.NO
            central_req.is_req_completed = CentralRequestInfo.YES
            central_req.completion_event_type = (
                CentralRequestInfo.FROM_IVR_PROCESSOR_FAILED
            )
            central_req.updated_on = datetime.now()
            central_req.save(
                update_fields=[
                    "is_onhold_req",
                    "is_enable",
                    "is_req_completed",
                    "completion_event_type",
                    "updated_on",
                ]
            )

        settings.API_LOGGER.info(
            f"Marked request_id:- {body['request_id']} completion_event_type=FROM_IVR_PROCESSOR_FAILED in OnHoldCirculator.mark_request_aborted"
        )

        pm_response = Helper.make_pm_response(
            "call", "request was aborted through central!!!", ""
        )
        Helper.invoke_process_manager(
            body["request_id"],
            body["company_id"],
            body["ivr_id"],
            pm_response,
            "400",
            conf.OBD_SERVICES["cancel_service"],
        )
