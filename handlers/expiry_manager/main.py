import time
from datetime import datetime, timedelta

from django.conf import settings

from central.settings.handlers import conf
from handlers.models import CentralRequestInfo
from utills.helpers.helper import Helper
from utills.shared_cache_manager.main import SharedCacheManager


class ExpiryManager:
    @classmethod
    def run_request_canceled_from_ivr_processor(cls):
        # get requests canceled from IVR_Processor
        pattern_prefix = ":{version}:{key}".format(
            version=conf.REDIS_VERSION_KEY, key=conf.CANCELED_REQUEST_KEY
        )
        canceled_requests = SharedCacheManager.get_keys(
            pattern="{prefix}*".format(prefix=pattern_prefix)
        )
        canceled_request_ids = [
            req.split(pattern_prefix)[-1] for req in canceled_requests
        ]

        if canceled_request_ids:
            settings.API_LOGGER.info(
                "expiry_manager - disabling canceled requests sent from IVR Processor - requets_id: {rids}".format(
                    rids=canceled_request_ids
                )
            )

            central_req_info_obj = CentralRequestInfo.objects.filter(
                request_id__in=canceled_request_ids
            )
            if central_req_info_obj.exists():
                for central_req in central_req_info_obj:
                    central_req.is_req_completed = CentralRequestInfo.YES
                    central_req.is_onhold_req = CentralRequestInfo.NA
                    central_req.completion_event_type = (
                        CentralRequestInfo.FROM_IVR_PROCESSOR_FAILED
                    )
                    central_req.is_enable = CentralRequestInfo.NO
                    central_req.updated_on = datetime.now()
                    central_req.save(
                        update_fields=[
                            "is_req_completed",
                            "is_enable",
                            "completion_event_type",
                            "is_onhold_req",
                            "updated_on",
                        ]
                    )

        # delete processed canceled requests from shared_cache
        SharedCacheManager.delete_many_keys(canceled_requests)

    @classmethod
    def run_expiry_manager(cls, send_pm_event=True):
        # request expiry logic
        requests = CentralRequestInfo.objects.filter(
            is_req_completed=CentralRequestInfo.NO,
            added_on__gte=datetime.now() - timedelta(days=3),
            expired_at__lte=datetime.now(),
        )

        if not requests.exists():
            log_msg = "expiry_manager - no new requests found, exiting after sleeping for {dur}s...".format(
                dur=conf.EXPIRY_MANAGER_SLEEP_DUR
            )
            Helper.sampling_logger_emitter(
                log_msg, sampling_probability=1, is_enable=True
            )
            time.sleep(conf.EXPIRY_MANAGER_SLEEP_DUR)
            return

        log_msg = "expiry_manager - total request manually expired-{total_count}...".format(
            total_count=requests.count()
        )
        Helper.sampling_logger_emitter(
            log_msg, sampling_probability=1, is_enable=True
        )

        if requests.count() > 500:
            settings.API_LOGGER.error(
                "expiry_manager({time}) more than 500 requests getting cancelled manually".format(
                    time=datetime.now()
                )
            )
            # return

        for request in requests:
            request.is_req_completed = CentralRequestInfo.YES
            request.is_enable = CentralRequestInfo.NO
            request.completion_event_type = (
                CentralRequestInfo.FROM_EXPIRY_MANAGER
            )
            request.event_response = CentralRequestInfo.NOT_RESPONDED

            if request.is_onhold_req in [CentralRequestInfo.ENTER]:
                # expiring on_hold requests, EXPIRED means its  entry will get deleted from user_tracing and ivr_tracing tables.
                request.is_onhold_req = CentralRequestInfo.EXPIRED
            else:
                request.is_onhold_req = CentralRequestInfo.NA

            request.updated_on = datetime.now()
            request.save(
                update_fields=[
                    "is_req_completed",
                    "is_enable",
                    "completion_event_type",
                    "event_response",
                    "is_onhold_req",
                    "updated_on",
                ]
            )

            if send_pm_event:
                response = Helper.make_pm_response(
                    conf.OBD_SERVICES["expiry_manager"],
                    "request expired!",
                    "request got expired!",
                )
                Helper.invoke_process_manager(
                    request.request_id,
                    request.c_id,
                    request.ivr_id,
                    response,
                    "500",
                    conf.OBD_SERVICES["cancel_service"],
                )

            log_msg = "expiry_manager - manually expiring-request_id {request_id}, time_of_manual_expiring {time}...".format(
                request_id=request.id, time=datetime.now()
            )
            Helper.sampling_logger_emitter(
                log_msg, sampling_probability=1, is_enable=True
            )

    @classmethod
    def process(cls):
        """
        Responsibilities:
                1. Picks from CentralRequestInfo where is_completed = NO and expired_at < now()
                2. Set is_completed = YES and completion_event_type = FROM_EXPIRY_MANAGER
        Parameters:
                None
        Returns:
                False
        """
        try:
            cls.run_request_canceled_from_ivr_processor()
            cls.run_expiry_manager()
        except Exception as e:
            settings.API_LOGGER.error(
                "Error-expiry_manager({time}) - e={e}".format(
                    e=e, time=datetime.now()
                )
            )
