import factory
from factory.django import DjangoModelFactory
from factory.fuzzy import <PERSON><PERSON><PERSON><PERSON>ger

from handlers.models import Group, GroupIvrInfoRelationship, Number, Server
from test_suite.factories.ivr_info import IvrInfoFactory


class GroupFactory(DjangoModelFactory):
    kam_group_id = FuzzyInteger(1, 100)

    class Meta:
        model = Group


class ServerFactory(DjangoModelFactory):
    class Meta:
        model = Server


class NumberFactory(DjangoModelFactory):
    group = factory.SubFactory(GroupFactory)

    class Meta:
        model = Number


class GroupIvrInfoFactory(DjangoModelFactory):
    group = factory.SubFactory(GroupFactory)
    number = factory.SubFactory(NumberFactory)
    ivr_info = factory.SubFactory(IvrInfoFactory)

    class Meta:
        model = GroupIvrInfoRelationship
