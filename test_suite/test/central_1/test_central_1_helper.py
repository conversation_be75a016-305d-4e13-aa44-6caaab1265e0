from math import ceil
from unittest.mock import Mock, patch

from django.test import tag

from central.settings.handlers import conf
from handlers.central_1.helper import Central1_Helper
from handlers.models import SQSQueueInfo
from test_suite.test.base import BaseTestCase
from test_suite.test.data.central_1.data import (
    CENTRAL_1_IVRS,
    EXCLUDED_IVR_IDS,
    IVR_INFO_DATA,
    PAUSED_IVR_IDS,
    SQS_QUEUE_INFO_DATA,
)
from utills.cache_manager.main import CacheManager
from utills.helpers.helper import Helper


@tag("central_1")
class Central1HelperTestCase(BaseTestCase):
    IVR_DATA = IVR_INFO_DATA

    CACHE_KEY_EXPIRY_TTL = 10

    def setUp(self):
        super().setUp()
        self.cleanup_central_1_cache()

    def test_get_priority_ivr_mapping_to_process(self):
        expected_ivr_ids_mapping = {}
        to_be_deleted_keys = []

        for ivr_id in PAUSED_IVR_IDS:
            paused_ivr_key = f"{conf.CACHE_PAUSED_IVR_KEY_NAME}{ivr_id}"
            CacheManager.set_value(
                paused_ivr_key, True, expiry=self.CACHE_KEY_EXPIRY_TTL
            )
            to_be_deleted_keys.append(paused_ivr_key)

        for ivr_id in EXCLUDED_IVR_IDS:
            exclude_ivr_key = f"{conf.CACHE_EXCLUDE_IVR_KEY_NAME}{ivr_id}"
            CacheManager.set_value(
                exclude_ivr_key, True, expiry=self.CACHE_KEY_EXPIRY_TTL
            )
            to_be_deleted_keys.append(exclude_ivr_key)

        for data in self.IVR_DATA:
            ivr_id = data["ivr_id"]
            ivr_priority = data["ivr_priority"]
            cache_key = f"{conf.CACHE_IVR_KEY_NAME}{ivr_id}"
            CacheManager.set_value(
                cache_key, 10, expiry=self.CACHE_KEY_EXPIRY_TTL
            )
            to_be_deleted_keys.append(cache_key)

            expected_ivr_ids_mapping[
                ivr_priority
            ] = expected_ivr_ids_mapping.get(ivr_priority, []) + [ivr_id]

        actual_ivr_ids_mapping = (
            Central1_Helper.get_priority_ivr_mapping_to_process()
        )

        for k, v in expected_ivr_ids_mapping.items():
            self.assertIn(
                k,
                actual_ivr_ids_mapping.keys(),
                f"Expected ivr_priority {k} not found in actual ivr_ids_mapping - {actual_ivr_ids_mapping}",
            )

            actual_ls = sorted(actual_ivr_ids_mapping[k])
            v = sorted(v)

            self.assertEqual(
                v,
                actual_ls,
                f"Wrong ivr_ids mapping received, Expected: {v}, Actual: {actual_ls}",
            )

        for cache_key in to_be_deleted_keys:
            CacheManager.delete_key(cache_key)

    def test_get_thresholds(self):
        for data in SQS_QUEUE_INFO_DATA:
            queue_obj = SQSQueueInfo.objects.create(**data)

        # Threshold of central_1 and PQs.
        threshold = Helper.get_threshold(
            conf.CENTRAL_1_ENTITY, conf.CENTRAL_1_THRESHOLD
        )

        expected_c1_threshold = int(threshold[conf.CENTRAL_1])
        expected_max_workers = int(threshold[conf.CENTRAL_1_MAX_WORKERS])
        default_pq2_threshold = int(threshold[conf.CENTRAL_PQ2])
        default_pq3_threshold = int(threshold[conf.CENTRAL_PQ3])
        default_single_ivr_threshold = int(
            threshold[conf.SINGLE_IVR_THRESHOLD_KEY]
        )

        expected_pq2_threshold = ceil(
            default_pq2_threshold / expected_max_workers
        )
        expected_pq3_threshold = ceil(
            default_pq3_threshold / expected_max_workers
        )
        expected_single_ivr_thread_threshold = ceil(
            default_single_ivr_threshold / expected_max_workers
        )

        expected_data = (
            expected_c1_threshold,
            expected_max_workers,
            expected_pq2_threshold,
            expected_pq3_threshold,
            expected_single_ivr_thread_threshold,
        )

        with patch(
            "utills.sqs_manager.main.SQSManager.get_queue_attributes"
        ) as sqs_manager_mock_obj:
            sqs_manager_mock_obj.return_value = {
                "ApproximateNumberOfMessages": 0
            }

            actual_data = Central1_Helper.get_thresholds()

            for i, data in enumerate(actual_data):
                self.assertEqual(
                    data,
                    expected_data[i],
                    f"Expected: {expected_data[i]}, Actual: {data}",
                )

    def test_is_request_cancelled(self):
        ivr_id = CENTRAL_1_IVRS[0]
        request_id = "random_request_id"

        with patch(
            "utills.external_apis.ExternalAPIs.talk_to_cancellation_api"
        ) as cancellation_api_mock:
            response_mock = Mock()
            response_mock.get.return_value = [
                {"request_status": conf.CANCELLED_REQUEST_STATUS}
            ]

            cancellation_api_mock.return_value = response_mock

            with patch(
                "central.settings.handlers.conf.CANCELLATION_SERVICE_CHECK_ENABLED",
                return_value=1,
            ):
                res = Central1_Helper.is_request_cancelled(ivr_id, request_id)
                self.assertTrue(res, f"Expected: True, Actual: {res}")

                response_mock.get.return_value = [{"request_status": "500"}]
                res = Central1_Helper.is_request_cancelled(ivr_id, request_id)

                self.assertEqual(
                    res,
                    response_mock,
                    f"Expected - {response_mock}, Actual: {res}",
                )

                cancellation_api_mock.return_value = None

                res = Central1_Helper.is_request_cancelled(ivr_id, request_id)

                self.assertIsNone(res, f"Expected None, Actual: {res}")
