import json
from datetime import datetime
from typing import Dict
from unittest.mock import patch

from django.test import override_settings, tag

from central.settings.handlers import conf
from handlers.central_1.helper import Central1_Helper
from handlers.central_1.main import Central1
from handlers.models import CentralRequestInfo, IvrInfo, SQSQueueInfo
from handlers.on_hold.circulator.main import OnHoldCirculator
from test_suite.test.base import BaseTestCase
from test_suite.test.data.central_1.data import (
    CENTRAL_1_IVRS,
    CENTRAL_1_PROCESS_IVRS_REQUESTS,
    CENTRAL_1_RUN_DATA,
    CENTRAL_1_TEST_EXPIRED_REQUEST_DATA,
    CENTRAL_1_TEST_IVR_TIMING_FAILED_REQUEST,
    CENTRAL_1_TEST_NEW_REQUEST_DATA,
    IVR_INFO_DATA,
    SQS_QUEUE_INFO_DATA,
)
from utills.cache_manager.main import CacheManager
from utills.helpers.helper import Helper


@tag("central_1")
@override_settings(CACHEOPS_ENABLED=False)
class Central1TestCase(BaseTestCase):
    IVR_DATA = IVR_INFO_DATA

    RETURN_CODES = (1, 2, 3, 4)
    INSERTED_RETURN_CODE = RETURN_CODES[0]
    INSERTION_FAILED_RETURN_CODE = RETURN_CODES[1]
    DUPLICATE_RETURN_CODE = RETURN_CODES[2]
    EXPIRED_RETURN_CODE = RETURN_CODES[3]

    IVR_CACHE_KEY_EXPIRY = 60

    @classmethod
    def setUpTestData(cls):
        for data in SQS_QUEUE_INFO_DATA:
            SQSQueueInfo.objects.create(**data)

        super().setUpTestData()

    def setUp(self):
        super().setUp()
        self.cleanup_central_1_cache()
        self.start_common_patches_for_central_1()

    def tearDown(self):
        super().tearDown()
        self.stop_common_patches_for_central_1()

    def start_common_patches_for_central_1(self):
        self.sqs_manager_get_q_attr_patch = patch(
            "utills.sqs_manager.main.SQSManager.get_queue_attributes",
            return_value={"ApproximateNumberOfMessages": 0},
        )
        self.sqs_manager_get_q_attr_patch.start()

        self.sqs_send_message_patch = patch(
            "utills.sqs_manager.main.SQSManager.send_message_to_sqs"
        )
        self.sqs_send_message_mock = self.sqs_send_message_patch.start()

        self.sqs_delete_message_patch = patch(
            "utills.sqs_manager.main.SQSManager.sqs_delete_message"
        )
        self.sqs_delete_message_mock = self.sqs_delete_message_patch.start()

        self.sqs_fetch_message_patch = patch(
            "utills.sqs_manager.main.SQSManager.fetch_message_sp"
        )
        self.fetch_msg_mock = self.sqs_fetch_message_patch.start()
        self.fetch_msg_mock.side_effect = (
            lambda url, limit, is_name: self.mock_fetch_message_sp(
                url, limit, is_name
            )
        )

        self.shuffle_patch = patch("handlers.central_1.main.shuffle")
        self.shuffle_patch.start()

        self.sqs_fifo_pusher_patch = patch(
            "utills.sqs_manager.main.SQSManager.sqs_fifo_pusher"
        )
        self.sqs_fifo_pusher_patch.start()

        self.cancellation_enable_patch = patch(
            "central.settings.handlers.conf.CANCELLATION_SERVICE_CHECK_ENABLED",
            0,
        )
        self.cancellation_enable_patch.start()

    def stop_common_patches_for_central_1(self):
        self.sqs_manager_get_q_attr_patch.stop()
        self.sqs_send_message_patch.stop()
        self.sqs_delete_message_patch.stop()
        self.sqs_fetch_message_patch.stop()
        self.shuffle_patch.stop()
        self.sqs_fifo_pusher_patch.stop()
        self.cancellation_enable_patch.stop()

    def expire_cri_requests(self, request_obj: CentralRequestInfo):
        request_obj.is_req_completed = CentralRequestInfo.YES
        request_obj.is_enable = CentralRequestInfo.NO
        request_obj.completion_event_type = (
            CentralRequestInfo.FROM_EXPIRY_MANAGER
        )
        request_obj.event_response = CentralRequestInfo.NOT_RESPONDED

        if request_obj.is_onhold_req in [CentralRequestInfo.ENTER]:
            # expiring on_hold requests, EXPIRED means its  entry will get deleted from user_tracing and ivr_tracing tables.
            request_obj.is_onhold_req = CentralRequestInfo.EXPIRED
        else:
            request_obj.is_onhold_req = CentralRequestInfo.NA

        request_obj.updated_on = datetime.now()
        request_obj.save(
            update_fields=[
                "is_req_completed",
                "is_enable",
                "completion_event_type",
                "event_response",
                "is_onhold_req",
                "updated_on",
            ]
        )

    def _run_process_ivrs(self):
        (
            c1_threshold,
            max_workers,
            pq2_thread_threshold,
            pq3_thread_threshold,
            single_ivr_threshold,
        ) = Central1_Helper.get_thresholds()

        queue_url = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )

        ivr_ls = []

        for ivr_id in CENTRAL_1_IVRS:
            ivr_ls.append(ivr_id)
            cache_key = f"{conf.CACHE_IVR_KEY_NAME}{ivr_id}"
            CacheManager.set_value(
                cache_key, 3, expiry=self.IVR_CACHE_KEY_EXPIRY
            )

        ivr_ls = sorted(ivr_ls)

        Central1().process_ivrs(
            ivr_ls,
            pq2_thread_threshold,
            queue_url,
            conf.CENTRAL_PQ2_COUNT_KEY,
            single_ivr_threshold,
        )

    def _verify_sqs_send_message_mock_args_list(self, all_requests: Dict):
        arg_ls = self.sqs_send_message_mock.call_args_list

        for arg in arg_ls:
            json_data = json.loads(arg[0][1])
            request_id = json_data.get("request_id")
            ivr_id = json_data.get("ivr_id")

            self.assertTrue(
                all_requests.get(request_id),
                f"sqs_send_message_mock was not called with request_id - {request_id}",
            )
            req_data = all_requests.get(request_id)

            ivr_obj = IvrInfo.objects.filter(ivr_id=ivr_id)

            self.assertTrue(
                ivr_obj.exists(), f"ivr_id {ivr_id} not found in ivr table"
            )
            ivr_obj = ivr_obj.first()

            if ivr_obj.common_setting["obdv2_call_direction"] == "agent":
                req_data["dial_reverse"] = 1

            self.assertEqual(
                req_data,
                json_data,
                f"The sent data and the args sent to sqs_send_message_mock is different. actual_data- {json_data}, expected_data - {req_data}",
            )

    def _test_successfully_ran_process_ivrs_method(self):
        request_count = 0
        all_requests = {}
        for data in CENTRAL_1_PROCESS_IVRS_REQUESTS.values():
            # Check whether created requests are there in DB.
            for req in data:
                cri_obj = CentralRequestInfo.objects.filter(
                    ivr_id=req.get("ivr_id")
                )
                self.assertTrue(
                    cri_obj.exists(),
                    f"Central Request Info object not created for {req.get('ivr_id')}",
                )

                request_count += 1
                all_requests[req.get("request_id")] = req

        # Check whether PQ2 and PQ3 count is equal to request_count in cache
        value = (CacheManager.get_value(conf.CENTRAL_PQ2_COUNT_KEY) or 0) + (
            CacheManager.get_value(conf.CENTRAL_PQ3_COUNT_KEY) or 0
        )
        CacheManager.delete_key(conf.CENTRAL_PQ2_COUNT_KEY)
        CacheManager.delete_key(conf.CENTRAL_PQ3_COUNT_KEY)

        self.assertEqual(
            value,
            request_count,
            f"Central PQ2 + PQ3 queue Count is not equals to request_count - {request_count}, actual_count - {value}",
        )

        # Check whether sqs_send_message has been called for each request
        self.assertEqual(
            self.sqs_send_message_mock.call_count,
            request_count,
            f"SQS send message not called for {request_count} times",
        )

        self._verify_sqs_send_message_mock_args_list(all_requests)
        self.assertEqual(
            self.sqs_delete_message_mock.call_count,
            request_count,
            f"SQS delete message not called for {request_count} times",
        )

        for ivr_id in CENTRAL_1_IVRS:
            # Check whether ivr cache key has been deleted
            cache_key = f"{conf.CACHE_IVR_KEY_NAME}{ivr_id}"
            val = CacheManager.get_value(cache_key)
            if val:
                CacheManager.delete_key(cache_key)

            self.assertIsNone(val, f"IVR cache key not deleted for {ivr_id}")

    def _test_ivr_rule_api_failed_process_ivrs_method(self):
        for data in CENTRAL_1_PROCESS_IVRS_REQUESTS.values():
            # Check whether created requests are there in DB.
            for req in data:
                cri_obj = CentralRequestInfo.objects.filter(
                    ivr_id=req.get("ivr_id")
                )
                self.assertFalse(
                    cri_obj.exists(),
                    f"Central Request Info object exists for {req.get('ivr_id')}",
                )

        # Check whether PQ2 and PQ3 count is equal to request_count in cache
        value = (CacheManager.get_value(conf.CENTRAL_PQ2_COUNT_KEY) or 0) + (
            CacheManager.get_value(conf.CENTRAL_PQ3_COUNT_KEY) or 0
        )
        CacheManager.delete_key(conf.CENTRAL_PQ2_COUNT_KEY)
        CacheManager.delete_key(conf.CENTRAL_PQ3_COUNT_KEY)

        self.assertEqual(
            value,
            0,
            f"Central PQ2 + PQ3 Count key is not 0, actual_count - {value}",
        )

        # Check whether sqs_send_message has been called for each request
        self.assertEqual(
            self.sqs_send_message_mock.call_count,
            0,
            f"SQS send message was called for {self.sqs_send_message_mock.call_count} times",
        )

        self.assertEqual(
            self.sqs_delete_message_mock.call_count,
            0,
            f"SQS delete message was called for {self.sqs_delete_message_mock.call_count} times",
        )

        for ivr_id in CENTRAL_1_IVRS:
            # Check whether ivr cache key has been deleted
            cache_key = f"{conf.CACHE_IVR_KEY_NAME}{ivr_id}"
            val = CacheManager.get_value(cache_key)
            if val:
                CacheManager.delete_key(cache_key)

            self.assertIsNotNone(val, f"IVR cache key is None for {ivr_id}")

    def mock_fetch_message_sp(self, url, limit, is_name):
        ls = []
        req = {}
        for data in CENTRAL_1_PROCESS_IVRS_REQUESTS.get(url, []):
            req = {}
            req["Body"] = json.dumps(data)
            req["ReceiptHandle"] = "test_receipt_handle"
            ls.append(req)

        return ls

    # ---––––-------- Test Cases Starts Here ------------------

    def test_add_central_request_info_expired_requests(self):
        for data in CENTRAL_1_TEST_EXPIRED_REQUEST_DATA:
            cri_obj, json_body = self.create_cri_object_from_dict(data)
            self.expire_cri_requests(cri_obj)
            return_code = Central1.add_central_request_info(json_body)
            self.assertEqual(
                return_code,
                self.EXPIRED_RETURN_CODE,
                f"Wrong returned code for expired requests, Expected {self.EXPIRED_RETURN_CODE} but got {return_code}",
            )

    def test_add_central_request_info_ivr_timing_failed_requests(self):
        for data in CENTRAL_1_TEST_IVR_TIMING_FAILED_REQUEST:
            cri_obj, json_body = self.create_cri_object_from_dict(data)
            OnHoldCirculator({}).update_ivr_timing_fail(cri_obj)

            return_code = Central1.add_central_request_info(json_body)

            self.assertEqual(
                return_code,
                self.INSERTED_RETURN_CODE,
                f"Wrong returned code for IVR Timing failed requests, Expected {self.INSERTED_RETURN_CODE} but got {return_code}",
            )

            cri_obj.refresh_from_db()

            self.assertEqual(
                cri_obj.is_onhold_req,
                CentralRequestInfo.NA,
                f"Wrong is_onhold_req for IVR Timing failed requests, Expected {CentralRequestInfo.NA} but got {cri_obj.is_onhold_req}",
            )
            self.assertEqual(
                cri_obj.is_enable,
                CentralRequestInfo.YES,
                f"Wrong is_enable for IVR Timing failed requests, Expected {CentralRequestInfo.YES} but got {cri_obj.is_enable}",
            )

    def test_add_central_request_info_duplicated_requests(self):
        for data in CENTRAL_1_TEST_NEW_REQUEST_DATA:
            cri_obj, json_body = self.create_cri_object_from_dict(data)
            return_code = Central1.add_central_request_info(json_body)
            self.assertEqual(
                return_code,
                self.DUPLICATE_RETURN_CODE,
                f"Wrong returned code for duplicated requests, Expected {self.DUPLICATE_RETURN_CODE} but got {return_code}",
            )

    def test_add_central_request_info_new_requests(self):
        for data in CENTRAL_1_TEST_NEW_REQUEST_DATA:
            json_body = {}
            json_body["Body"] = json.dumps(data)
            return_code = Central1.add_central_request_info(json_body)
            self.assertEqual(
                return_code,
                self.INSERTED_RETURN_CODE,
                f"Wrong returned code for New requests, Expected {self.INSERTED_RETURN_CODE} but got {return_code}",
            )

    def test_process_ivrs_ivr_rule_api_available(self):
        with patch(
            "utills.external_apis.ExternalAPIs.talk_to_ivr_rule_api",
            return_value=True,
        ):
            self._run_process_ivrs()
            self._test_successfully_ran_process_ivrs_method()

    def test_process_ivrs_ivr_rule_api_unavailable(self):
        with patch(
            "utills.external_apis.ExternalAPIs.talk_to_ivr_rule_api",
            return_value=False,
        ):
            self._run_process_ivrs()
            self._test_ivr_rule_api_failed_process_ivrs_method()

    def test_run(self):
        """
        This test case will execute the central_1.run method and test whether everything is working as expected.

        """
        (
            c1_threshold,
            max_workers,
            pq2_thread_threshold,
            pq3_thread_threshold,
            single_ivr_threshold,
        ) = Central1_Helper.get_thresholds()

        with patch(
            "utills.external_apis.ExternalAPIs.talk_to_ivr_rule_api",
            return_value=True,
        ):
            ivrs_to_process = CENTRAL_1_RUN_DATA
            for ivr_id in CENTRAL_1_IVRS:
                cache_key = f"{conf.CACHE_IVR_KEY_NAME}{ivr_id}"
                CacheManager.set_value(
                    cache_key, 3, expiry=self.IVR_CACHE_KEY_EXPIRY
                )

            Central1().run(
                (
                    c1_threshold,
                    pq2_thread_threshold,
                    pq3_thread_threshold,
                    ivrs_to_process,
                    single_ivr_threshold,
                )
            )
            self._test_successfully_ran_process_ivrs_method()

    # def test_central_1(self):
    #     """
    #     This test case will execute the whole central_1 management command
    #     """
    #     with patch('utills.external_apis.ExternalAPIs.talk_to_ivr_rule_api', return_value=True):
    #         for ivr_id in CENTRAL_1_IVRS:
    #             cache_key = f'{conf.CACHE_IVR_KEY_NAME}{ivr_id}'
    #             CacheManager.set_value(cache_key, 3 , expiry = self.IVR_CACHE_KEY_EXPIRY)

    #         self.run_management_command()
    #         self._test_successfully_ran_process_ivrs_method()
