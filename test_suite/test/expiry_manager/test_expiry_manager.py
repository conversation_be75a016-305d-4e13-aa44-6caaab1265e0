from unittest.mock import patch

from central.settings.handlers import conf
from django.test import override_settings, tag
from handlers.expiry_manager.main import ExpiryManager
from handlers.models import CentralRequestInfo, IvrInfo, SQSQueueInfo
from test_suite.test.base import BaseTestCase
from test_suite.test.data.expiry_manager.data import \
    (EXPIRY_MANAGER_DUMMY_REQUESTS,
    EXPIRY_MANAGER_IVR_INFO_DATA)
from utills.shared_cache_manager.main import SharedCacheManager
from utills.shared_cache_manager.conf import SHARED_REDIS_CON
from test_suite.test.data.utills.data import SQS_MANAGER_QUEUE_URL

from datetime import datetime, timedelta

@tag('expiry_manager')
@override_settings(CACHEOPS_ENABLED=False)
class ExpiryManagerTestCase(BaseTestCase):
    IVR_DATA = EXPIRY_MANAGER_IVR_INFO_DATA
    REQUESTS_DATA = EXPIRY_MANAGER_DUMMY_REQUESTS
    SHARED_CACHE_TTL = 10


    def setUp(self):
        super().setUp()

        self.expiry_sleep_dur_patch = patch(
            'central.settings.handlers.conf.EXPIRY_MANAGER_SLEEP_DUR', 0)
        self.expiry_sleep_dur_mock = self.expiry_sleep_dur_patch.start()

        self.sqs_fifo_pusher_patch = patch('utills.sqs_manager.main.SQSManager.sqs_fifo_pusher')
        self.sqs_fifo_pusher_mock = self.sqs_fifo_pusher_patch.start()
        
        self.sqs_get_q_url_patch = patch(
            'utills.sqs_manager.main.SQSManager.get_queue_urls',
            return_value=[SQS_MANAGER_QUEUE_URL]
        )
        self.sqs_get_q_url_mock = self.sqs_get_q_url_patch.start()

        self.setup_requests()

    def tearDown(self):
        super().tearDown()
        self.expiry_sleep_dur_patch.stop()
        self.sqs_fifo_pusher_patch.stop()
        self.sqs_get_q_url_patch.stop()
        

    def get_shared_cache_key_pattern(self):
        pattern_prefix = ":{version}:{key}".format(
            version=conf.REDIS_VERSION_KEY, key=conf.CANCELED_REQUEST_KEY)
        return pattern_prefix

    def setup_shared_cache_key(self):
        pattern_prefix = self.get_shared_cache_key_pattern()
        qs =  CentralRequestInfo.objects.all() # no need to add any filter, as the qs is being filtered by request in the function
        request_ids= []

        for obj in qs:
            SharedCacheManager.set_value(
                f"{pattern_prefix}{obj.request_id}", "true", self.SHARED_CACHE_TTL)

            request_ids.append(obj.request_id)

        return qs.count(), request_ids


    def test_run_request_canceled_from_ivr_processor(self):
        SHARED_REDIS_CON.flushdb()
        
        key_count, request_ids = self.setup_shared_cache_key()
        ExpiryManager.run_request_canceled_from_ivr_processor()
        keys = SharedCacheManager.get_keys(self.get_shared_cache_key_pattern())

        qs = CentralRequestInfo.objects.filter(
            is_req_completed=CentralRequestInfo.YES,
            is_onhold_req = CentralRequestInfo.NA,
            completion_event_type=CentralRequestInfo.FROM_IVR_PROCESSOR_FAILED,
            is_enable=CentralRequestInfo.NO)

        self.assertEqual(qs.count(), key_count, "Count didn't matched")
        
        for request_id in request_ids:
            filter_qs = qs.filter(request_id=request_id)

            self.assertTrue(filter_qs.exists(), f"Request  with request_id - {request_id} wasn't cancelled")

        self.assertEqual(len(keys), 0, "All the keys were not deleted.")

        SHARED_REDIS_CON.flushdb()

    def test_run_expiry_manager(self):
        existing_qs  = CentralRequestInfo.objects.all()
        existing_qs.update(
            added_on= datetime.now() - timedelta(days=2),
            expired_at= datetime.now() - timedelta(days=1))

        ExpiryManager.run_expiry_manager()

        for obj in existing_qs:
            obj.refresh_from_db()
            self.assertEqual(obj.is_enable, CentralRequestInfo.NO)
            self.assertEqual(obj.is_req_completed, CentralRequestInfo.YES)
            self.assertEqual(obj.completion_event_type , CentralRequestInfo.FROM_EXPIRY_MANAGER)
            self.assertEqual(obj.event_response , CentralRequestInfo.NOT_RESPONDED)

            self.assertTrue(obj.is_onhold_req in (CentralRequestInfo.EXPIRED,CentralRequestInfo.NA ))

        self.assertEqual(self.sqs_fifo_pusher_mock.call_count,
                        existing_qs.count())
