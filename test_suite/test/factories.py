from uuid import uuid4

import factory
from factory.django import DjangoModelFactory
from factory.fuzzy import FuzzyInteger

from handlers.models import CentralRequestInfo, IvrInfo


class CentralRequestInfoFactory(DjangoModelFactory):
    c_id = factory.LazyAttribute(lambda _: uuid4())
    ivr_id = factory.LazyAttribute(lambda _: uuid4())
    user_id = factory.LazyAttribute(lambda _: uuid4())
    request_type = FuzzyInteger(1, 3)

    class Meta:
        model = CentralRequestInfo


class IvrInfoFactory(DjangoModelFactory):
    class Meta:
        model = IvrInfo
