import json
from typing import Dict

from django.conf import settings
from django.test import override_settings
from rest_framework import status

import pytest
from moto import mock_aws

from handlers.models import CentralRequestInfo, Group, IvrInfo, Number
from test_suite.factories.groups import (
    GroupFactory,
    GroupIvrInfoFactory,
    NumberFactory,
)
from test_suite.test.factories import IvrInfoFactory
from utills.cache_manager.main import CacheManager
from utills.exceptions import GroupUnavailableException, UDCLockedException
from utills.helpers.helper import Helper
from utills.request_handler import RequestHandler
from utills.shared_cache_manager.main import SharedCacheManager
from utills.udc_helper import UDCHelper


@pytest.mark.unittest
@pytest.mark.django_db
def test_request_handler_setup(load_json, create_cri_object):
    request = load_json("central_request_info/type_1.json")
    cri_obj = create_cri_object(request)
    handler = RequestHandler()
    handler.setup(cri_obj)
    assert handler.udc_helper
    assert handler.group_handler
    assert handler.request == cri_obj


@pytest.mark.unittest
@pytest.mark.django_db
def test_request_handler_check_udc(
    load_json, create_cri_object, mock_udc_apis
):
    request = load_json("central_request_info/type_1.json")
    udc_response = load_json(
        "external_apis/udc/user_view_only_enabled_success.json",
    )

    cri_obj = create_cri_object(request)
    IvrInfoFactory(
        ivr_type=IvrInfo.P2P,
        c_id=cri_obj.c_id,
        ivr_id=cri_obj.ivr_id,
        common_setting={},
        company_display_number=request["company_display_number"],
    )

    Helper.get_ivrinfo_common_setting(
        cri_obj.ivr_id,
        settings.UDC_API_TOGGLE_KEY,
        1,
    )

    udc_response["data"]["user_data"]["uuid"] = cri_obj.user_id
    udc_response["data"]["user_data"]["company"] = cri_obj.c_id
    udc_response["data"]["channel_data"] = 5

    mock_udc_apis(
        cri_obj,
        {"response": udc_response, "status_code": status.HTTP_200_OK},
    )

    handler = RequestHandler()
    handler.setup(cri_obj)
    assert handler.check_udc()


@pytest.mark.unittest
@pytest.mark.django_db
def test_request_handler_check_udc_not_available(
    load_json, create_cri_object, mock_udc_apis
):
    request = load_json("central_request_info/type_1.json")
    udc_response = load_json(
        "external_apis/udc/user_view_only_enabled_success.json",
    )

    cri_obj = create_cri_object(request)
    IvrInfoFactory(
        ivr_type=IvrInfo.P2P,
        c_id=cri_obj.c_id,
        ivr_id=cri_obj.ivr_id,
        common_setting={},
        company_display_number=request["company_display_number"],
    )

    Helper.get_ivrinfo_common_setting(
        cri_obj.ivr_id,
        settings.UDC_API_TOGGLE_KEY,
        1,
    )

    udc_response["data"]["user_data"]["uuid"] = cri_obj.user_id
    udc_response["data"]["user_data"]["company"] = cri_obj.c_id
    udc_response["data"]["channel_data"] = 5

    mock_udc_apis(
        cri_obj,
        {"response": udc_response, "status_code": status.HTTP_400_BAD_REQUEST},
    )

    handler = RequestHandler()
    handler.setup(cri_obj)
    assert not handler.check_udc()


@pytest.mark.unittest
@pytest.mark.django_db
def test_request_handler_check_udc_locked(
    load_json, create_cri_object, mock_udc_apis
):
    request = load_json("central_request_info/type_1.json")
    cri_obj = create_cri_object(request)
    IvrInfoFactory(
        ivr_type=IvrInfo.P2P,
        c_id=cri_obj.c_id,
        ivr_id=cri_obj.ivr_id,
        common_setting={},
        company_display_number=request["company_display_number"],
    )

    cache_key = "{prefix}{key}".format(
        prefix=settings.CACHE_UDC_LOCKED_USERS_KEY_NAME,
        key=cri_obj.user_id,
    )
    CacheManager.set_value(cache_key, 1, 2)

    handler = RequestHandler()
    handler.setup(cri_obj)
    with pytest.raises(
        UDCLockedException,
        match=f"UDC is locked for request_id - {cri_obj.request_id}",
    ):
        handler.check_udc()


@pytest.mark.unittest
@pytest.mark.django_db
def test_request_handler_get_group_data_if_only_aws_group_mapped(
    load_json, create_cri_object
):
    request = load_json("central_request_info/type_1.json")
    cri_obj = create_cri_object(request)

    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    group_1 = GroupFactory(settings={"group_type": Group.AWS_GROUP_TYPE})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    handler = RequestHandler()
    handler.setup(cri_obj)
    assert handler.get_group_data() == {
        "source_number": "9999999990",
        "is_aws": True,
        "ivr_type": ivr.ivr_type,
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_request_handler_get_group_data_if_only_aws_group_not_mapped(
    load_json, create_cri_object
):
    request = load_json("central_request_info/type_1.json")
    cri_obj = create_cri_object(request)

    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    group = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(22)),
        kam_group_id=22,
    )
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999999",
        group=group,
    )

    group_2 = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(23)),
        kam_group_id=23,
    )
    number_2 = NumberFactory(
        is_fix_did=Number.NO,
        number="99999999998",
        group=group_2,
    )
    group_3 = GroupFactory(
        is_enable=Group.NO,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(24)),
        kam_group_id=24,
    )
    number_3 = NumberFactory(
        is_fix_did=Number.YES,
        number="99999999997",
        group=group_2,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_2, group=group_2)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_3, group=group_3)

    handler = RequestHandler()
    handler.setup(cri_obj)
    assert handler.get_group_data() == {
        "url": group.get_assigned_queues[handler.group_handler.pq_key]["url"],
        "name": group.get_assigned_queues[handler.group_handler.pq_key][
            "name"
        ],
        "ivr_settings": ivr.common_setting,
        "ivr_type": ivr.ivr_type,
        "source_number": number_1.number,
        "group_settings": group.settings,
        "is_aws": False,
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@override_settings(
    GROUP_QUEUE_THRESHOLD={
        settings.GROUPS_PQ_KEY: -10,
        settings.GROUPS_NPQ_KEY: -30,
    }
)
def test_request_handler_get_group_data_if_group_not_available(
    load_json, create_cri_object
):
    request = load_json("central_request_info/type_1.json")
    cri_obj = create_cri_object(request)

    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    handler = RequestHandler()

    assigned_queues = Helper.get_group_assigned_queues(22)
    assigned_queues["p_q"]["threshold"] = -10
    assigned_queues["np_q"]["threshold"] = -10

    group = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(assigned_queues),
        kam_group_id=22,
    )
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999999",
        group=group,
    )

    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group)

    handler.setup(cri_obj)
    with pytest.raises(GroupUnavailableException) as exc_info:
        handler.get_group_data()
    assert not exc_info.value.cancel_request


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_request_handler_get_group_data_if_is_cancel_group(
    load_json, create_cri_object
):
    request = load_json("central_request_info/type_1.json")

    request["obd_v2_settings"]["failover"] = False
    cri_obj = create_cri_object(request)
    IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    handler = RequestHandler()

    assigned_queues = Helper.get_group_assigned_queues(22)
    assigned_queues["p_q"]["threshold"] = -10
    assigned_queues["np_q"]["threshold"] = -10

    handler.setup(cri_obj)
    with pytest.raises(GroupUnavailableException) as exc_info:
        handler.get_group_data()
    assert exc_info.value.cancel_request


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture,ivr_type,ivr_settings",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 0, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 1, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
    ],
)
def test_request_handler_process_if_only_aws_group_mapped(
    cri_request_fixture,
    udc_fixture,
    ivr_type,
    ivr_settings,
    load_json,
    create_cri_object,
    mock_udc_apis,
    mock_obd_internal_api,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
):
    # Mock the TrueCaller APIs
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()
    request = load_json(cri_request_fixture)
    udc_response = load_json(udc_fixture)

    expected_res = load_json(
        "external_apis/obd_internal_api/success_response.json"
    )

    cri_obj = create_cri_object(request)

    ivr = IvrInfoFactory(
        ivr_id=cri_obj.ivr_id, common_setting=ivr_settings, ivr_type=ivr_type
    )
    group_1 = GroupFactory(settings={"group_type": Group.AWS_GROUP_TYPE})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    if udc_response["data"].get("user_data"):
        udc_response["data"]["user_data"]["uuid"] = cri_obj.user_id
        udc_response["data"]["user_data"]["company"] = cri_obj.c_id

    udc_response["data"]["channel_data"] = 5

    mock_udc_apis(
        cri_obj,
        {"response": udc_response, "status_code": status.HTTP_200_OK},
        user_view_only=int(
            not ivr.common_setting.get(settings.BOOK_USER_TOGGLE_KEY)
        ),
        user_is_avail=ivr.common_setting.get(settings.UDC_API_TOGGLE_KEY),
    )

    mock_res = mock_obd_internal_api(api_response=expected_res)

    handler = RequestHandler()
    handler.setup(cri_obj)

    success_callback_called = {"value": False}

    def on_udc_unavailable(request_obj: CentralRequestInfo):
        assert not request_obj

    def on_failure(
        request_obj: CentralRequestInfo, response: Dict, status_code: str
    ):
        assert not request_obj
        assert not response
        assert not status_code

    def on_success(
        udc_helper: UDCHelper, request_obj: CentralRequestInfo, response: Dict
    ):
        success_callback_called["value"] = True

        assert udc_helper == handler.udc_helper
        assert request_obj == cri_obj
        assert response == expected_res

    handler.process(on_udc_unavailable, on_success, on_failure)

    assert success_callback_called["value"]

    assert mock_res.call_count == 1

    assert token_resp.call_count == 1
    assert resp.call_count == 1


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture,ivr_type,ivr_settings",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 0, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 1, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
    ],
)
def test_request_handler_process_if_only_non_aws_group_mapped(
    cri_request_fixture,
    udc_fixture,
    ivr_type,
    ivr_settings,
    load_json,
    create_cri_object,
    mock_udc_apis,
    patch_sqs,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
):
    # Mock the TrueCaller APIs
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()
    request = load_json(cri_request_fixture)
    udc_response = load_json(udc_fixture)

    cri_obj = create_cri_object(request)

    assigned_queues = Helper.get_group_assigned_queues(22)

    ivr = IvrInfoFactory(
        ivr_id=cri_obj.ivr_id,
        common_setting=ivr_settings,
        ivr_type=ivr_type,
        ivr_priority=1,
    )
    group_1 = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(assigned_queues),
        kam_group_id=22,
        settings={"group_type": Group.PHY_GROUP_TYPE},
    )
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    if udc_response["data"].get("user_data"):
        udc_response["data"]["user_data"]["uuid"] = cri_obj.user_id
        udc_response["data"]["user_data"]["company"] = cri_obj.c_id

    udc_response["data"]["channel_data"] = 5

    mock_udc_apis(
        cri_obj,
        {"response": udc_response, "status_code": status.HTTP_200_OK},
        user_view_only=int(
            not ivr.common_setting.get(settings.BOOK_USER_TOGGLE_KEY)
        ),
        user_is_avail=ivr.common_setting.get(settings.UDC_API_TOGGLE_KEY),
    )

    handler = RequestHandler()
    handler.setup(cri_obj)

    success_callback_called = {"value": False}

    def on_udc_unavailable(request_obj: CentralRequestInfo):
        assert not request_obj

    def on_failure(
        request_obj: CentralRequestInfo, response: Dict, status_code: str
    ):
        assert not request_obj
        assert not response
        assert not status_code

    def on_success(
        udc_helper: UDCHelper, request_obj: CentralRequestInfo, response: Dict
    ):
        success_callback_called["value"] = True

        assert udc_helper == handler.udc_helper
        assert request_obj == cri_obj
        assert response == "success"

    handler.process(on_udc_unavailable, on_success, on_failure)

    assert success_callback_called["value"]

    message_received = patch_sqs.receive_message(
        QueueUrl=assigned_queues["p_q"]["url"],
    )

    assert (
        json.loads(message_received["Messages"][0]["Body"]).get("request_id")
        == cri_obj.request_id
    )
    shared_cache_key = ":{prefix}:{value}".format(
        prefix=settings.REDIS_VERSION_KEY,
        value=settings.GROUP_MESSGAE_COUNT_KEY.format(
            q_url=assigned_queues["p_q"]["url"]
        ),
    )
    assert SharedCacheManager.get_value(shared_cache_key) == "1"

    assert token_resp.call_count == 1
    assert resp.call_count == 1


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture,ivr_type,ivr_settings",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_not_available.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 0, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_not_available.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_not_available.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 1, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
    ],
)
def test_request_handler_process_udc_unavailable(
    cri_request_fixture,
    udc_fixture,
    ivr_type,
    ivr_settings,
    load_json,
    create_cri_object,
    mock_udc_apis,
    mock_obd_internal_api,
):
    request = load_json(cri_request_fixture)
    udc_response = load_json(udc_fixture)
    expected_res = load_json(
        "external_apis/obd_internal_api/success_response.json"
    )
    cri_obj = create_cri_object(request)

    ivr = IvrInfoFactory(
        ivr_id=cri_obj.ivr_id, common_setting=ivr_settings, ivr_type=ivr_type
    )
    group_1 = GroupFactory(settings={"group_type": Group.AWS_GROUP_TYPE})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    mock_udc_apis(
        cri_obj,
        {"response": udc_response, "status_code": status.HTTP_400_BAD_REQUEST},
        user_view_only=int(
            not ivr.common_setting.get(settings.BOOK_USER_TOGGLE_KEY)
        ),
        user_is_avail=ivr.common_setting.get(settings.UDC_API_TOGGLE_KEY),
    )

    mock_res = mock_obd_internal_api(api_response=expected_res)

    handler = RequestHandler()
    handler.setup(cri_obj)

    udc_unavailable_callback_called = {"value": False}

    def on_udc_unavailable(request_obj: CentralRequestInfo):
        udc_unavailable_callback_called["value"] = True
        assert request_obj == cri_obj

    def on_failure(
        request_obj: CentralRequestInfo, response: Dict, status_code: str
    ):
        assert not request_obj
        assert not response
        assert not status_code

    def on_success(
        udc_helper: UDCHelper, request_obj: CentralRequestInfo, response: Dict
    ):
        assert not udc_helper
        assert not request_obj
        assert not response

    handler.process(on_udc_unavailable, on_success, on_failure)

    assert udc_unavailable_callback_called["value"]
    assert mock_res.call_count == 0


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture,ivr_type,ivr_settings",
    [
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 1, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
    ],
)
def test_request_handler_process_udc_locked(
    cri_request_fixture,
    udc_fixture,
    ivr_type,
    ivr_settings,
    load_json,
    create_cri_object,
    mock_udc_apis,
    mock_obd_internal_api,
):
    request = load_json(cri_request_fixture)
    udc_response = load_json(udc_fixture)
    expected_res = load_json(
        "external_apis/obd_internal_api/success_response.json"
    )
    cri_obj = create_cri_object(request)

    ivr = IvrInfoFactory(
        ivr_id=cri_obj.ivr_id, common_setting=ivr_settings, ivr_type=ivr_type
    )
    group_1 = GroupFactory(settings={"group_type": Group.AWS_GROUP_TYPE})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    if udc_response["data"].get("user_data"):
        udc_response["data"]["user_data"]["uuid"] = cri_obj.user_id
        udc_response["data"]["user_data"]["company"] = cri_obj.c_id

    udc_response["data"]["channel_data"] = 5

    mock_udc_apis(
        cri_obj,
        {"response": udc_response, "status_code": status.HTTP_400_BAD_REQUEST},
        user_view_only=int(
            not ivr.common_setting.get(settings.BOOK_USER_TOGGLE_KEY)
        ),
        user_is_avail=ivr.common_setting.get(settings.UDC_API_TOGGLE_KEY),
    )

    mock_res = mock_obd_internal_api(api_response=expected_res)

    CacheManager.set_value(
        "{prefix}{key}".format(
            prefix=settings.CACHE_UDC_LOCKED_USERS_KEY_NAME,
            key=cri_obj.user_id,
        ),
        1,
    )

    handler = RequestHandler()
    handler.setup(cri_obj)

    def on_udc_unavailable(request_obj: CentralRequestInfo):
        assert not request_obj

    def on_failure(
        request_obj: CentralRequestInfo, response: Dict, status_code: str
    ):
        assert not request_obj
        assert not response
        assert not status_code

    def on_success(
        udc_helper: UDCHelper, request_obj: CentralRequestInfo, response: Dict
    ):
        assert not udc_helper
        assert not request_obj
        assert not response

    handler.process(on_udc_unavailable, on_success, on_failure)

    assert mock_res.call_count == 0
