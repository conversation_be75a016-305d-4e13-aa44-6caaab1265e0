import json

from django.conf import settings
from rest_framework import status

import pytest
from moto import mock_aws

from handlers.models import CentralRequestInfo, Group, IvrInfo, Number
from handlers.on_hold.circulator.main_v2 import OnHoldCirculator_V2
from handlers.on_hold.processor.main import OnHoldProcessor
from test_suite.factories.groups import (
    GroupFactory,
    GroupIvrInfoFactory,
    NumberFactory,
)
from test_suite.test.factories import IvrInfoFactory
from utills.cache_manager.main import CacheManager
from utills.helpers.helper import Helper
from utills.shared_cache_manager.main import SharedCacheManager


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_on_hold_circulator_v2_on_destination_failure(
    load_json, create_cri_object, mock_create_queue
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )
    obd_api_res = load_json("external_apis/obd_internal_api/400_response.json")

    cri_obj = create_cri_object(request_body)

    sqs_client, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )
    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)
    handler.on_destination_failure(
        cri_obj, obd_api_res, str(status.HTTP_400_BAD_REQUEST)
    )

    canceled_request_key = "{version}{prefix}{key}".format(
        version=settings.REDIS_VERSION_KEY,
        prefix=settings.CANCELED_REQUEST_KEY,
        key=cri_obj.request_id,
    )

    assert SharedCacheManager.get_value(canceled_request_key) == "1"

    assert json.loads(
        sqs_client.receive_message(
            QueueUrl=pm_res["QueueUrl"],
        )["Messages"][
            0
        ]["Body"]
    ) == {
        "company_id": cri_obj.c_id,
        "context": "service_invoker",
        "ivr_id": cri_obj.ivr_id,
        "request_id": cri_obj.request_id,
        "response": {
            "emitter": settings.OBD_SERVICES["source_name"],
            "failure_reason": json.dumps(obd_api_res),
            "message": "",
        },
        "response_status": str(status.HTTP_400_BAD_REQUEST),
        "service_name": settings.OBD_SERVICES["cancel_service"],
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_on_hold_circulator_v2_on_destination_success(
    load_json, create_cri_object, mock_create_queue
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )
    obd_api_res = load_json("external_apis/obd_internal_api/400_response.json")

    cri_obj = create_cri_object(request_body)

    sqs_client, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)
    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)
    handler.request_handler.setup(cri_obj)

    handler.on_destination_success(
        handler.request_handler.udc_helper,
        cri_obj,
        obd_api_res,
    )

    assert handler.request_handler.udc_helper.cache.get_value(
        handler.request_handler.udc_helper.cache.key
    )

    assert json.loads(
        sqs_client.receive_message(
            QueueUrl=pm_res["QueueUrl"],
        )["Messages"][
            0
        ]["Body"]
    ) == {
        "company_id": cri_obj.c_id,
        "context": "service_invoker",
        "ivr_id": cri_obj.ivr_id,
        "request_id": cri_obj.request_id,
        "response": {
            "emitter": settings.OBD_SERVICES["source_name"],
            "failure_reason": "",
            "message": json.dumps(obd_api_res),
        },
        "response_status": str(status.HTTP_200_OK),
        "service_name": settings.OBD_SERVICES["source_name"],
    }

    assert CentralRequestInfo.objects.filter(
        pk=cri_obj.pk, is_onhold_req=CentralRequestInfo.EXIT
    ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_on_hold_circulator_v2_on_udc_unavailable(
    load_json,
    create_cri_object,
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )

    cri_obj = create_cri_object(request_body)
    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)
    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)

    handler.request_handler.setup(cri_obj)
    handler.on_udc_unavailable(
        cri_obj,
    )

    assert CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        onhold_udc_check_availibilty_cnt=cri_obj.onhold_udc_check_availibilty_cnt
        + 1,
    ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_on_hold_circulator_v2_abort_if_job_is_aborted(
    load_json, create_cri_object, mock_create_queue
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )

    cri_obj = create_cri_object(request_body, job_id=request_body["job_id"])

    sqs_client, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)
    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)
    handler.request_handler.setup(cri_obj)

    aborted_job_cache_key = (
        f"{settings.JP_JOB_ABORT_CENTRAL_CACHE_KEY}{cri_obj.job_id}"
    )

    CacheManager.set_value(aborted_job_cache_key, True)

    assert handler.abort_if_job_is_aborted(
        cri_obj,
    )

    assert json.loads(
        sqs_client.receive_message(
            QueueUrl=pm_res["QueueUrl"],
        )["Messages"][
            0
        ]["Body"]
    ) == {
        "company_id": cri_obj.c_id,
        "context": "service_invoker",
        "ivr_id": cri_obj.ivr_id,
        "request_id": cri_obj.request_id,
        "response": {
            "emitter": settings.OBD_SERVICES["source_name"],
            "failure_reason": "request was aborted through central!!!",
            "message": "",
        },
        "response_status": str(status.HTTP_400_BAD_REQUEST),
        "service_name": settings.OBD_SERVICES["cancel_service"],
    }

    assert CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        is_onhold_req=CentralRequestInfo.EXIT,
        is_enable=CentralRequestInfo.NO,
        is_req_completed=CentralRequestInfo.YES,
        completion_event_type=(CentralRequestInfo.FROM_IVR_PROCESSOR_FAILED),
    ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_on_hold_circulator_v2_abort_if_job_is_not_aborted(
    load_json, create_cri_object, mock_create_queue
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )

    cri_obj = create_cri_object(request_body, job_id=request_body["job_id"])

    sqs_client, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)
    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)
    handler.request_handler.setup(cri_obj)

    assert not handler.abort_if_job_is_aborted(
        cri_obj,
    )

    assert not sqs_client.receive_message(
        QueueUrl=pm_res["QueueUrl"],
    ).get("Messages")

    assert not CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        is_onhold_req=CentralRequestInfo.EXIT,
        is_enable=CentralRequestInfo.NO,
        is_req_completed=CentralRequestInfo.YES,
        completion_event_type=(CentralRequestInfo.FROM_IVR_PROCESSOR_FAILED),
    ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_on_hold_circulator_v2_ivr_timing_fail_if_ivr_rule_is_not_valid(
    load_json, create_cri_object, mock_create_queue, mock_ivr_rule_api
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )

    cri_obj: CentralRequestInfo = create_cri_object(
        request_body, job_id=request_body["job_id"]
    )
    IvrInfoFactory(ivr_id=cri_obj.ivr_id, c_id=cri_obj.c_id)

    sqs_client, service_router_res = mock_create_queue(
        settings.SERVICE_ROUTER_QUEUE
    )

    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)

    mock_ivr_rule_api(
        cri_obj.ivr_id,
        cri_obj.c_id,
        available=False,
        status_code=status.HTTP_400_BAD_REQUEST,
    )

    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)
    handler.request_handler.setup(cri_obj)

    assert handler.ivr_timing_fail_if_ivr_rule_is_not_valid(
        cri_obj,
    )

    request_body[settings.SERVICE_ROUTER_FLAG] = settings.OBD_SERVICES[
        "source_name"
    ]

    assert (
        json.loads(
            sqs_client.receive_message(
                QueueUrl=service_router_res["QueueUrl"],
            )["Messages"][0]["Body"]
        )
        == request_body
    )

    assert CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        is_onhold_req=CentralRequestInfo.IVR_TIMING_FAIL,
        onhold_udc_check_availibilty_cnt=0,
        is_enable=CentralRequestInfo.NO,
    ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_on_hold_circulator_v2_skip_ivr_timing_fail_if_ivr_rule_is_not_valid(
    load_json, create_cri_object, mock_create_queue, mock_ivr_rule_api
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )

    cri_obj: CentralRequestInfo = create_cri_object(
        request_body, job_id=request_body["job_id"]
    )
    IvrInfoFactory(ivr_id=cri_obj.ivr_id, c_id=cri_obj.c_id)

    sqs_client, service_router_res = mock_create_queue(
        settings.SERVICE_ROUTER_QUEUE
    )

    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)

    mock_ivr_rule_api(
        cri_obj.ivr_id,
        cri_obj.c_id,
    )

    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)
    handler.request_handler.setup(cri_obj)

    assert not handler.ivr_timing_fail_if_ivr_rule_is_not_valid(
        cri_obj,
    )

    request_body[settings.SERVICE_ROUTER_FLAG] = settings.OBD_SERVICES[
        "source_name"
    ]

    assert not sqs_client.receive_message(
        QueueUrl=service_router_res["QueueUrl"],
    ).get("Messages")

    assert not CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        is_onhold_req=CentralRequestInfo.IVR_TIMING_FAIL,
        onhold_udc_check_availibilty_cnt=0,
        is_enable=CentralRequestInfo.NO,
    ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_on_hold_circulator_v2_pre_checks_success(
    load_json, create_cri_object, mock_create_queue, mock_ivr_rule_api
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )

    cri_obj = create_cri_object(request_body, job_id=request_body["job_id"])

    IvrInfoFactory(ivr_id=cri_obj.ivr_id, c_id=cri_obj.c_id)
    mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    mock_create_queue(settings.SERVICE_ROUTER_QUEUE)

    mock_ivr_rule_api(
        cri_obj.ivr_id,
        cri_obj.c_id,
    )

    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)
    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)
    handler.request_handler.setup(cri_obj)

    assert handler.pre_checks_success(
        cri_obj,
    )

    aborted_job_cache_key = (
        f"{settings.JP_JOB_ABORT_CENTRAL_CACHE_KEY}{cri_obj.job_id}"
    )

    CacheManager.set_value(aborted_job_cache_key, True)

    assert not handler.pre_checks_success(
        cri_obj,
    )

    mock_ivr_rule_api(
        cri_obj.ivr_id,
        cri_obj.c_id,
        available=False,
        status_code=status.HTTP_400_BAD_REQUEST,
    )
    assert not handler.pre_checks_success(
        cri_obj,
    )


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_on_hold_circulator_v2_process_request_if_pre_check_fails(
    load_json, create_cri_object, mock_create_queue, mock_ivr_rule_api
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )

    cri_obj: CentralRequestInfo = create_cri_object(
        request_body, job_id=request_body["job_id"]
    )

    IvrInfoFactory(ivr_id=cri_obj.ivr_id, c_id=cri_obj.c_id)
    mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    mock_create_queue(settings.SERVICE_ROUTER_QUEUE)

    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)
    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    mock_ivr_rule_api(
        cri_obj.ivr_id,
        cri_obj.c_id,
        available=False,
        status_code=status.HTTP_400_BAD_REQUEST,
    )

    handler = OnHoldCirculator_V2(threshold)
    handler.process_request(cri_obj)

    cri_obj.refresh_from_db()

    assert CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        is_onhold_req=CentralRequestInfo.IVR_TIMING_FAIL,
        onhold_udc_check_availibilty_cnt=0,
        is_enable=CentralRequestInfo.NO,
    ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture,ivr_type,ivr_settings",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_not_available.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 0, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_not_available.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_not_available.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 1, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
    ],
)
def test_on_hold_circulator_v2_process_request_udc_unavailable(
    cri_request_fixture,
    udc_fixture,
    ivr_type,
    ivr_settings,
    load_json,
    create_cri_object,
    mock_create_queue,
    mock_udc_apis,
    mock_ivr_rule_api,
):
    request_body = load_json(cri_request_fixture)
    expected_udc_res = load_json(udc_fixture)

    cri_obj: CentralRequestInfo = create_cri_object(request_body)

    ivr = IvrInfoFactory(
        ivr_id=cri_obj.ivr_id,
        c_id=cri_obj.c_id,
        common_setting=ivr_settings,
        ivr_type=ivr_type,
    )

    group_1 = GroupFactory(settings={"group_type": Group.AWS_GROUP_TYPE})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    mock_ivr_rule_api(
        cri_obj.ivr_id,
        cri_obj.c_id,
    )
    mock_udc_apis(
        cri_obj,
        {
            "response": expected_udc_res,
            "status_code": status.HTTP_400_BAD_REQUEST,
        },
        user_view_only=int(
            not ivr.common_setting.get(settings.BOOK_USER_TOGGLE_KEY)
        ),
        user_is_avail=ivr.common_setting.get(settings.UDC_API_TOGGLE_KEY),
    )

    sqs_client, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)
    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)

    handler.request_handler.setup(cri_obj)
    handler.process_request(
        cri_obj,
    )

    canceled_request_key = "{version}{prefix}{key}".format(
        version=settings.REDIS_VERSION_KEY,
        prefix=settings.CANCELED_REQUEST_KEY,
        key=cri_obj.request_id,
    )

    assert not SharedCacheManager.get_value(canceled_request_key)

    assert not handler.request_handler.udc_helper.cache.get_value(
        handler.request_handler.udc_helper.cache.key
    )

    assert not sqs_client.receive_message(
        QueueUrl=pm_res["QueueUrl"],
    ).get("Messages")

    assert CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        onhold_udc_check_availibilty_cnt=cri_obj.onhold_udc_check_availibilty_cnt
        + 1,
        is_onhold_req=CentralRequestInfo.ENTER,
    ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture,ivr_type,ivr_settings",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 0, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 1, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
    ],
)
def test_on_hold_circulator_v2_process_request_success(
    cri_request_fixture,
    udc_fixture,
    ivr_type,
    ivr_settings,
    load_json,
    create_cri_object,
    mock_create_queue,
    mock_udc_apis,
    mock_ivr_rule_api,
    mock_obd_internal_api,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
):
    # Mock the TrueCaller APIs
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()
    request_body = load_json(cri_request_fixture)
    expected_udc_res = load_json(udc_fixture)

    expected_obd_res = load_json(
        "external_apis/obd_internal_api/success_response.json"
    )

    cri_obj: CentralRequestInfo = create_cri_object(request_body)

    ivr = IvrInfoFactory(
        ivr_id=cri_obj.ivr_id,
        c_id=cri_obj.c_id,
        common_setting=ivr_settings,
        ivr_type=ivr_type,
    )

    group_1 = GroupFactory(settings={"group_type": Group.AWS_GROUP_TYPE})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    if expected_udc_res["data"].get("user_data"):
        expected_udc_res["data"]["user_data"]["uuid"] = cri_obj.user_id
        expected_udc_res["data"]["user_data"]["company"] = cri_obj.c_id

    expected_udc_res["data"]["channel_data"] = 5

    mock_ivr_rule_api(
        cri_obj.ivr_id,
        cri_obj.c_id,
    )
    mock_udc_apis(
        cri_obj,
        {
            "response": expected_udc_res,
            "status_code": status.HTTP_200_OK,
        },
        user_view_only=int(
            not ivr.common_setting.get(settings.BOOK_USER_TOGGLE_KEY)
        ),
        user_is_avail=ivr.common_setting.get(settings.UDC_API_TOGGLE_KEY),
    )

    mock_res = mock_obd_internal_api(api_response=expected_obd_res)

    sqs_client, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)
    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)

    handler.request_handler.setup(cri_obj)
    handler.process_request(
        cri_obj,
    )

    canceled_request_key = "{version}{prefix}{key}".format(
        version=settings.REDIS_VERSION_KEY,
        prefix=settings.CANCELED_REQUEST_KEY,
        key=cri_obj.request_id,
    )

    assert not SharedCacheManager.get_value(canceled_request_key)

    assert handler.request_handler.udc_helper.cache.get_value(
        handler.request_handler.udc_helper.cache.key
    )

    assert json.loads(
        sqs_client.receive_message(
            QueueUrl=pm_res["QueueUrl"],
        )["Messages"][
            0
        ]["Body"]
    ) == {
        "company_id": cri_obj.c_id,
        "context": "service_invoker",
        "ivr_id": cri_obj.ivr_id,
        "request_id": cri_obj.request_id,
        "response": {
            "emitter": settings.OBD_SERVICES["source_name"],
            "failure_reason": "",
            "message": json.dumps(expected_obd_res),
        },
        "response_status": str(status.HTTP_200_OK),
        "service_name": settings.OBD_SERVICES["source_name"],
    }

    assert CentralRequestInfo.objects.filter(
        pk=cri_obj.pk, is_onhold_req=CentralRequestInfo.EXIT
    ).exists()

    assert mock_res.call_count == 1
    assert token_resp.call_count == 1
    assert resp.call_count == 1


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture,ivr_type,ivr_settings",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 0, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 1, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
    ],
)
def test_on_hold_circulator_v2_process_request_failure(
    cri_request_fixture,
    udc_fixture,
    ivr_type,
    ivr_settings,
    load_json,
    create_cri_object,
    mock_create_queue,
    mock_udc_apis,
    mock_ivr_rule_api,
    mock_obd_internal_api,
):
    request_body = load_json(cri_request_fixture)
    expected_udc_res = load_json(udc_fixture)

    expected_obd_res = load_json(
        "external_apis/obd_internal_api/400_response.json"
    )

    cri_obj: CentralRequestInfo = create_cri_object(request_body)

    ivr = IvrInfoFactory(
        ivr_id=cri_obj.ivr_id,
        c_id=cri_obj.c_id,
        common_setting=ivr_settings,
        ivr_type=ivr_type,
    )

    group_1 = GroupFactory(settings={"group_type": Group.AWS_GROUP_TYPE})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    if expected_udc_res["data"].get("user_data"):
        expected_udc_res["data"]["user_data"]["uuid"] = cri_obj.user_id
        expected_udc_res["data"]["user_data"]["company"] = cri_obj.c_id

    expected_udc_res["data"]["channel_data"] = 5

    mock_ivr_rule_api(
        cri_obj.ivr_id,
        cri_obj.c_id,
    )
    mock_udc_apis(
        cri_obj,
        {
            "response": expected_udc_res,
            "status_code": status.HTTP_200_OK,
        },
        user_view_only=int(
            not ivr.common_setting.get(settings.BOOK_USER_TOGGLE_KEY)
        ),
        user_is_avail=ivr.common_setting.get(settings.UDC_API_TOGGLE_KEY),
    )

    mock_res = mock_obd_internal_api(
        api_response=expected_obd_res, status_code=status.HTTP_400_BAD_REQUEST
    )

    sqs_client, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)
    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)

    handler.request_handler.setup(cri_obj)
    handler.process_request(
        cri_obj,
    )

    assert not handler.request_handler.udc_helper.cache.get_value(
        handler.request_handler.udc_helper.cache.key
    )

    assert mock_res.call_count == 1

    canceled_request_key = "{version}{prefix}{key}".format(
        version=settings.REDIS_VERSION_KEY,
        prefix=settings.CANCELED_REQUEST_KEY,
        key=cri_obj.request_id,
    )

    assert SharedCacheManager.get_value(canceled_request_key) == "1"

    assert json.loads(
        sqs_client.receive_message(
            QueueUrl=pm_res["QueueUrl"],
        )["Messages"][
            0
        ]["Body"]
    ) == {
        "company_id": cri_obj.c_id,
        "context": "service_invoker",
        "ivr_id": cri_obj.ivr_id,
        "request_id": cri_obj.request_id,
        "response": {
            "emitter": settings.OBD_SERVICES["source_name"],
            "failure_reason": json.dumps(expected_obd_res),
            "message": "",
        },
        "response_status": str(status.HTTP_400_BAD_REQUEST),
        "service_name": settings.OBD_SERVICES["cancel_service"],
    }

    assert CentralRequestInfo.objects.filter(
        pk=cri_obj.pk, is_onhold_req=CentralRequestInfo.ENTER
    ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_on_hold_circulator_v2_process_request_if_group_not_available(
    load_json,
    create_cri_object,
    mock_create_queue,
    mock_ivr_rule_api,
):
    request_body = load_json("central_request_info/type_2.json")

    cri_obj: CentralRequestInfo = create_cri_object(request_body)

    IvrInfoFactory(ivr_id=cri_obj.ivr_id, c_id=cri_obj.c_id)

    mock_ivr_rule_api(
        cri_obj.ivr_id,
        cri_obj.c_id,
    )

    sqs_client, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)
    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)

    handler.request_handler.setup(cri_obj)
    handler.process_request(
        cri_obj,
    )

    canceled_request_key = "{version}{prefix}{key}".format(
        version=settings.REDIS_VERSION_KEY,
        prefix=settings.CANCELED_REQUEST_KEY,
        key=cri_obj.request_id,
    )

    assert not SharedCacheManager.get_value(canceled_request_key)

    assert not handler.request_handler.udc_helper.cache.get_value(
        handler.request_handler.udc_helper.cache.key
    )

    assert not sqs_client.receive_message(
        QueueUrl=pm_res["QueueUrl"],
    ).get("Messages")

    assert CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        onhold_udc_check_availibilty_cnt=cri_obj.onhold_udc_check_availibilty_cnt,
        is_onhold_req=CentralRequestInfo.ENTER,
    ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_on_hold_circulator_v2_process_request_if_group_not_available_with_cancel_request(
    load_json,
    create_cri_object,
    mock_create_queue,
    mock_ivr_rule_api,
):
    request_body = load_json("central_request_info/type_2.json")
    request_body["obd_v2_settings"]["failover"] = 0

    cri_obj: CentralRequestInfo = create_cri_object(request_body)

    IvrInfoFactory(ivr_id=cri_obj.ivr_id, c_id=cri_obj.c_id)

    mock_ivr_rule_api(
        cri_obj.ivr_id,
        cri_obj.c_id,
    )

    sqs_client, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    OnHoldProcessor.mark_is_onhold_req(cri_obj.body)
    threshold = Helper.get_threshold(
        settings.ON_HOLD_ENTITY, settings.ON_HOLD_THRESHOLD
    )

    handler = OnHoldCirculator_V2(threshold)

    handler.request_handler.setup(cri_obj)
    handler.process_request(
        cri_obj,
    )

    canceled_request_key = "{version}{prefix}{key}".format(
        version=settings.REDIS_VERSION_KEY,
        prefix=settings.CANCELED_REQUEST_KEY,
        key=cri_obj.request_id,
    )

    assert not SharedCacheManager.get_value(canceled_request_key)

    assert not handler.request_handler.udc_helper.cache.get_value(
        handler.request_handler.udc_helper.cache.key
    )

    assert json.loads(
        sqs_client.receive_message(
            QueueUrl=pm_res["QueueUrl"],
        )["Messages"][
            0
        ]["Body"]
    ) == {
        "request_id": cri_obj.request_id,
        "company_id": cri_obj.c_id,
        "ivr_id": cri_obj.ivr_id,
        "response": {
            "emitter": settings.OBD_SERVICES["source_name"],
            "failure_reason": "on_hold_circulator group not found!",
            "message": "",
        },
        "response_status": str(status.HTTP_404_NOT_FOUND),
        "service_name": settings.OBD_SERVICES["cancel_service"],
        "context": "service_invoker",
    }

    assert CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        is_onhold_req=CentralRequestInfo.EXIT,
        is_req_completed=CentralRequestInfo.YES,
        is_enable=CentralRequestInfo.NO,
    ).exists()
