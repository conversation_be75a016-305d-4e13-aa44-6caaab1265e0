from unittest.mock import patch

from django.core.cache import cache
from django.test import tag

from central.settings.handlers import conf
from handlers.models import CentralRequestInfo, SQSQueueInfo
from handlers.on_hold.circulator.main import OnHoldCirculator
from test_suite.test.base import BaseTestCase
from test_suite.test.data.common_data import (
    DUMMY_GROUP_DATA,
    DUMMY_SUCCESS_UDC_RESPONSE,
)
from test_suite.test.data.on_hold.data import (
    ON_HOLD_DUMMY_REQUESTS,
    ON_HOLD_IVR_DATA,
    ON_HOLD_SQS_QUEUE_INFO_DATA,
)
from utills.helpers.helper import Helper
from utills.shared_cache_manager.main import SharedCacheManager


@tag("on_hold", "on_hold_circulator")
class OnHoldCirculatorTestCase(BaseTestCase):
    IVR_DATA = ON_HOLD_IVR_DATA
    REQUESTS_DATA = ON_HOLD_DUMMY_REQUESTS

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        for data in ON_HOLD_SQS_QUEUE_INFO_DATA:
            SQSQueueInfo.objects.create(**data)

        cls.set_up_groups()

    def setUp(self):
        super().setUp()
        self.setup_requests()
        self.sqs_fifo_pusher_patch = patch(
            "utills.sqs_manager.main.SQSManager.sqs_fifo_pusher"
        )
        self.sqs_fifo_pusher_mock = self.sqs_fifo_pusher_patch.start()

        self.sqs_delete_message_patch = patch(
            "utills.sqs_manager.main.SQSManager.sqs_delete_message"
        )
        self.sqs_delete_message_mock = self.sqs_delete_message_patch.start()

        self.sqs_pusher_patch = patch(
            "utills.sqs_manager.main.SQSManager.sqs_pusher"
        )
        self.sqs_pusher_mock = self.sqs_pusher_patch.start()

        self.sqs_send_message_to_sqs_patch = patch(
            "utills.sqs_manager.main.SQSManager.send_message_to_sqs"
        )
        self.sqs_send_message_to_sqs_mock = (
            self.sqs_send_message_to_sqs_patch.start()
        )

        cache.clear()

    def tearDown(self):
        super().tearDown()
        self.sqs_fifo_pusher_patch.stop()
        self.sqs_delete_message_patch.stop()
        self.sqs_pusher_patch.stop()
        self.sqs_send_message_to_sqs_patch.stop()
        cache.clear()

        shared_cache_key = self.get_shared_cache_key(DUMMY_GROUP_DATA)
        SharedCacheManager.delete_key(shared_cache_key)

    def get_shared_cache_value(self):
        shared_cache_key = self.get_shared_cache_key(DUMMY_GROUP_DATA)
        initial_shared_cache_val = int(
            SharedCacheManager.get_value(shared_cache_key) or 0
        )
        return initial_shared_cache_val

    def get_shared_cache_key(self, group):
        shared_cache_key = ":{prefix}:{value}".format(
            prefix=conf.REDIS_VERSION_KEY,
            value=conf.GROUP_MESSGAE_COUNT_KEY.format(q_url=group["url"]),
        )

        return shared_cache_key

    def test_cancle_request(self):
        for data in self.REQUESTS_DATA:
            obj = CentralRequestInfo.objects.get(request_id=data["request_id"])
            OnHoldCirculator({}).cancle_request(obj, data)
            obj.refresh_from_db()
            self.assertEqual(obj.is_onhold_req, CentralRequestInfo.EXIT)
            self.assertEqual(obj.is_req_completed, CentralRequestInfo.YES)
            self.assertEqual(obj.is_enable, CentralRequestInfo.NO)

            self.sqs_fifo_pusher_mock.assert_called_once()
            self.sqs_fifo_pusher_mock.reset_mock()

    def test_update_ivr_timing_fail(self):
        for data in self.REQUESTS_DATA:
            obj = CentralRequestInfo.objects.get(request_id=data["request_id"])
            returned_obj = OnHoldCirculator({}).update_ivr_timing_fail(obj)
            obj.refresh_from_db()

            self.assertEqual(obj.id, returned_obj.id)
            self.assertEqual(
                obj.is_onhold_req, CentralRequestInfo.IVR_TIMING_FAIL
            )
            self.assertEqual(obj.onhold_udc_check_availibilty_cnt, 0)
            # self.assertEqual(obj.is_enable, CentralRequestInfo.NO) # Ignoring this , as is_enable value gets changed in signal

    def test_update_is_on_hold_req_exit(self):
        for data in self.REQUESTS_DATA:
            obj = CentralRequestInfo.objects.get(request_id=data["request_id"])
            returned_obj = OnHoldCirculator({}).update_is_on_hold_req_exit(obj)

            obj.refresh_from_db()
            self.assertEqual(obj.id, returned_obj.id)
            self.assertEqual(obj.is_onhold_req, CentralRequestInfo.EXIT)

    def test_update_udc_check_availibilty_cnt(self):
        threshold = Helper.get_threshold(
            conf.ON_HOLD_ENTITY, conf.ON_HOLD_THRESHOLD
        )

        #  if udc_check_availibilty_cnt is lower than max user check count

        data = self.REQUESTS_DATA[0]
        obj = CentralRequestInfo.objects.get(request_id=data["request_id"])
        initial_udc_count = obj.onhold_udc_check_availibilty_cnt
        OnHoldCirculator(threshold).update_udc_check_availibilty_cnt(obj)
        obj.refresh_from_db()
        self.assertEqual(
            obj.onhold_udc_check_availibilty_cnt, initial_udc_count + 1
        )

        # if udc_check_availibilty_cnt is greater or equal than max user check count

        data = self.REQUESTS_DATA[1]
        obj = CentralRequestInfo.objects.get(request_id=data["request_id"])

        max_user_check_count = Helper.get_ivrinfo_common_setting(
            obj.ivr_id,
            conf.ON_HOLD_MAX_USER_CHECK_COUNT,
            threshold[conf.ON_HOLD_MAX_USER_CHECK_COUNT],
            max_value=threshold[conf.ON_HOLD_MAX_USER_CHECK_COUNT],
        )

        obj.onhold_udc_check_availibilty_cnt = max_user_check_count
        obj.save()

        OnHoldCirculator(threshold).update_udc_check_availibilty_cnt(obj)
        obj.refresh_from_db()

        self.assertEqual(obj.onhold_udc_check_availibilty_cnt, 0)
        self.assertEqual(obj.is_enable, CentralRequestInfo.NO)
        self.assertEqual(obj.is_onhold_req, CentralRequestInfo.EXIT)
        self.assertEqual(obj.is_req_completed, CentralRequestInfo.YES)

        self.sqs_fifo_pusher_mock.assert_called_once()

    def test_run_ivr_timing_fail(self):
        threshold = Helper.get_threshold(
            conf.ON_HOLD_ENTITY, conf.ON_HOLD_THRESHOLD
        )

        with patch(
            "utills.external_apis.ExternalAPIs.talk_to_ivr_rule_api",
            return_value=False,
        ):
            qs = CentralRequestInfo.objects.all()
            OnHoldCirculator(threshold).run(qs)

            for obj in qs:
                obj.refresh_from_db()
                self.assertEqual(
                    obj.is_onhold_req, CentralRequestInfo.IVR_TIMING_FAIL
                )
                self.assertEqual(obj.onhold_udc_check_availibilty_cnt, 0)

            self.assertEqual(
                self.sqs_pusher_mock.call_count, len(self.REQUESTS_DATA)
            )

    def test_run_with_cancel_request(self):
        threshold = Helper.get_threshold(
            conf.ON_HOLD_ENTITY, conf.ON_HOLD_THRESHOLD
        )

        with patch(
            "utills.external_apis.ExternalAPIs.talk_to_ivr_rule_api",
            return_value=True,
        ):
            with patch(
                "utills.group_queues_routing_manager.main.GroupQueuesRoutingManager.assign_group",
                return_value={"is_cancle_request": "1"},
            ):
                qs = CentralRequestInfo.objects.all()
                OnHoldCirculator(threshold).run(qs)

                for obj in qs:
                    obj.refresh_from_db()
                    self.assertEqual(
                        obj.is_onhold_req, CentralRequestInfo.EXIT
                    )
                    self.assertEqual(
                        obj.is_req_completed, CentralRequestInfo.YES
                    )
                    self.assertEqual(obj.is_enable, CentralRequestInfo.NO)

                self.assertEqual(
                    self.sqs_fifo_pusher_mock.call_count, qs.count()
                )

    def test_run_with_no_udc_response(self):
        threshold = Helper.get_threshold(
            conf.ON_HOLD_ENTITY, conf.ON_HOLD_THRESHOLD
        )

        with patch(
            "utills.external_apis.ExternalAPIs.talk_to_ivr_rule_api",
            return_value=True,
        ):
            with patch(
                "utills.group_queues_routing_manager.main.GroupQueuesRoutingManager.assign_group",
                return_value=DUMMY_GROUP_DATA,
            ):
                with patch(
                    "utills.external_apis.ExternalAPIs.manage_udc_api_check",
                    return_value=None,
                ) as manage_udc_api_check_mock:
                    qs = CentralRequestInfo.objects.all()
                    udc_count_mapping = {}

                    for obj in qs:
                        udc_count_mapping[
                            obj.id
                        ] = obj.onhold_udc_check_availibilty_cnt

                    OnHoldCirculator(threshold).run(qs)

                    for obj in qs:
                        obj.refresh_from_db()
                        manage_udc_api_check_mock.assert_any_call(
                            obj,
                            user_view_only=Helper.get_toggle_view_only_value(
                                obj.ivr_id
                            ),
                        )
                        self.assertEqual(
                            obj.onhold_udc_check_availibilty_cnt,
                            udc_count_mapping.get(obj.id, 0) + 1,
                            f"onhold_udc_check_availibilty_cnt is not equal for req_id - {obj.request_id}",
                        )

    def test_run_with_udc_response(self):
        threshold = Helper.get_threshold(
            conf.ON_HOLD_ENTITY, conf.ON_HOLD_THRESHOLD
        )

        initial_shared_cache_val = self.get_shared_cache_value()

        with patch(
            "utills.external_apis.ExternalAPIs.talk_to_ivr_rule_api",
            return_value=True,
        ):
            with patch(
                "utills.group_queues_routing_manager.main.GroupQueuesRoutingManager.assign_group",
                return_value=DUMMY_GROUP_DATA,
            ):
                with patch(
                    "utills.external_apis.ExternalAPIs.manage_udc_api_check",
                    return_value=DUMMY_SUCCESS_UDC_RESPONSE["data"],
                ) as manage_udc_api_check_mock:
                    qs = CentralRequestInfo.objects.all()
                    udc_count_mapping = {}

                    for obj in qs:
                        udc_count_mapping[
                            obj.id
                        ] = obj.onhold_udc_check_availibilty_cnt

                    OnHoldCirculator(threshold).run(qs)

                    total_count = qs.count()
                    updated_shared_cache_val = self.get_shared_cache_value()
                    for obj in qs:
                        obj.refresh_from_db()
                        manage_udc_api_check_mock.assert_any_call(
                            obj,
                            user_view_only=Helper.get_toggle_view_only_value(
                                obj.ivr_id
                            ),
                        )
                        self.assertEqual(
                            obj.onhold_udc_check_availibilty_cnt,
                            udc_count_mapping.get(obj.id, 0),
                        )

                        self.assertEqual(
                            obj.is_onhold_req, CentralRequestInfo.EXIT
                        )

                    # whether the requests has been sent to group queue
                    self.assertEqual(
                        self.sqs_send_message_to_sqs_mock.call_count,
                        total_count,
                        "SQS send message to sqs call count is not equal to requests_count!!!",
                    )

                    self.assertEqual(
                        initial_shared_cache_val + total_count,
                        updated_shared_cache_val,
                        "Shared cache_val hasn't been incremented",
                    )

                    self.assertEqual(
                        self.sqs_fifo_pusher_mock.call_count,
                        total_count,
                        "sqs_fifo_pusher_mock in Helper.invoke_process_manager hasn't been called for all requests!!!",
                    )
