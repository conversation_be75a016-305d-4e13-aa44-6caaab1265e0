import json
from unittest.mock import patch

from django.core.cache import cache
from django.test import tag

from central.settings.handlers import conf
from handlers.models import CentralRequestInfo, SQSQueueInfo
from handlers.on_hold.processor.main import OnHoldProcessor
from test_suite.test.base import BaseTestCase
from test_suite.test.data.common_data import SQS_QUEUE_INFO_DATA
from test_suite.test.data.on_hold.data import (
    ON_HOLD_DUMMY_REQUESTS,
    ON_HOLD_IVR_DATA,
    ON_HOLD_IVR_IDS,
)
from utills.cache_manager.main import CacheManager


@tag("on_hold_processor", "on_hold")
class OnHoldProcessorTestCase(BaseTestCase):
    # skipping the test case for method whom jobs are just to return querysets.

    IVR_DATA = ON_HOLD_IVR_DATA
    REQUESTS_DATA = ON_HOLD_DUMMY_REQUESTS

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        for data in SQS_QUEUE_INFO_DATA:
            SQSQueueInfo.objects.create(**data)

    def setUp(self):
        super().setUp()
        self.setup_requests()
        self.sqs_fifo_pusher_patch = patch(
            "utills.sqs_manager.main.SQSManager.sqs_fifo_pusher"
        )
        self.sqs_fifo_pusher_mock = self.sqs_fifo_pusher_patch.start()

        self.sqs_delete_message_patch = patch(
            "utills.sqs_manager.main.SQSManager.sqs_delete_message"
        )
        self.sqs_delete_message_mock = self.sqs_delete_message_patch.start()
        cache.clear()

    def tearDown(self):
        super().tearDown()
        self.sqs_fifo_pusher_patch.stop()
        self.sqs_delete_message_patch.stop()
        cache.clear()

    def _test_whether_requests_are_in_on_hold(
        self,
        fetch_message_mock,
        requests_data,
    ):
        self.assertGreaterEqual(
            fetch_message_mock.call_count,
            1,
            "fetch_message_sp should at-least be called  one time",
        )

        self.assertEqual(
            len(requests_data),
            self.sqs_delete_message_mock.call_count,
            "sqs_delete_message_sp should be called for all the requests",
        )

        for json_data in requests_data:
            data = json.loads(json_data["Body"])
            requests = CentralRequestInfo.objects.filter(
                request_id=data["request_id"],
                is_onhold_req=CentralRequestInfo.ENTER,
            )

            self.assertTrue(
                requests.exists(),
                f"On Hold request was not found for request_id - {data['request_id']}",
            )

        value = CacheManager.get_value(conf.ON_HOLD_COUNT_KEY)

        self.assertIsNone(
            value, "On Hold count should get deleted from the cache!!!"
        )

    def _test_whether_requests_are_not_disabled(self):
        for data in self.REQUESTS_DATA:
            requests = CentralRequestInfo.objects.filter(
                request_id=data["request_id"],
                is_enable=CentralRequestInfo.NO,
                is_req_completed=CentralRequestInfo.YES,
                completion_event_type=CentralRequestInfo.NA,
                is_onhold_req=CentralRequestInfo.EXIT,
            )

            self.assertTrue(
                not requests.exists(),
                f"Disabled request was found for request_id - {data['request_id']}",
            )

    def test_mark_in_onhold_req(self):
        for data in self.REQUESTS_DATA:
            OnHoldProcessor.mark_is_onhold_req(data)

        for data in self.REQUESTS_DATA:
            requests = CentralRequestInfo.objects.filter(
                request_id=data["request_id"],
                is_onhold_req=CentralRequestInfo.ENTER,
            )

            self.assertTrue(
                requests.exists(),
                f"On Hold request was not found for request_id - {data['request_id']}",
            )

    def test_disable_req(self):
        for data in self.REQUESTS_DATA:
            OnHoldProcessor.disable_req(data["request_id"])

        for data in self.REQUESTS_DATA:
            requests = CentralRequestInfo.objects.filter(
                request_id=data["request_id"],
                is_enable=CentralRequestInfo.NO,
                is_req_completed=CentralRequestInfo.YES,
                completion_event_type=CentralRequestInfo.NA,
                is_onhold_req=CentralRequestInfo.EXIT,
            )

            self.assertTrue(
                requests.exists(),
                f"Disabled request was not found for request_id - {data['request_id']}",
            )

    def test_pause_ivr(self):
        ivr_id = ON_HOLD_IVR_IDS[0]
        OnHoldProcessor.pause_ivr(ivr_id)

        cache_key = f"{conf.CACHE_PAUSED_IVR_KEY_NAME}{ivr_id}"
        value = CacheManager.get_value(cache_key)

        self.assertIsNotNone(
            value, f"IVR was not paused for ivr_id - {ivr_id}"
        )

    def test_process(self):
        dummy_requests_data = []

        default_max_ivr_threshold = conf.ON_HOLD_THRESHOLD[
            conf.ON_HOLD_MAX_IVR_THRESHOLD
        ]
        max_req_num = min(
            default_max_ivr_threshold - 1, len(self.REQUESTS_DATA)
        )

        for data in self.REQUESTS_DATA[:max_req_num]:
            data_dict = dict()
            data_dict["Body"] = json.dumps(data)
            data_dict["ReceiptHandle"] = "some_Receipt_handle"
            dummy_requests_data.append(data_dict)

        CacheManager.set_value(
            conf.ON_HOLD_COUNT_KEY, len(dummy_requests_data) - 1
        )

        with patch(
            "utills.sqs_manager.main.SQSManager.fetch_message_sp",
            return_value=dummy_requests_data,
        ) as fetch_message_mock:
            OnHoldProcessor.process()
            self._test_whether_requests_are_in_on_hold(
                fetch_message_mock, dummy_requests_data
            )

            paused_ivr_keys = CacheManager.get_keys(
                conf.CACHE_PAUSED_IVR_KEY_NAME
            )

            self.assertEqual(
                len(paused_ivr_keys),
                0,
                "IVR should not be paused for any of the ivr_ids",
            )

            self._test_whether_requests_are_not_disabled()
