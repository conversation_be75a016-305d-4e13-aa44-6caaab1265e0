import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict
from unittest.mock import patch

from django.conf import settings
from django.core.management import call_command
from django.test import TestCase

from central.settings.handlers import conf
from handlers.models import (
    CentralRequestInfo,
    Group,
    GroupIvrInfoRelationship,
    IvrInfo,
    Number,
)
from test_suite.test.data.common_data import DUMMY_GROUPS_OBJ_DATA
from utills.cache_manager.main import CacheManager


class BaseTestCase(TestCase):
    IVR_DATA = list()
    GROUPS_DATA = DUMMY_GROUPS_OBJ_DATA
    MANAGEMENT_COMMAND = ""

    def setUp(self):
        self.patcher = patch("central.settings.API_LOGGER.error")
        self.mock_error_logger = self.patcher.start()
        self.mock_error_logger.side_effect = (
            lambda *args, **kwargs: settings.API_LOGGER.tempinfo(
                *args, **kwargs
            )
        )

        self.logger_patcher = patch(
            "utills.helpers.helper.Helper.get_binary_choice", return_value=0
        )
        self.mock_logger = self.logger_patcher.start()

    def tearDown(self):
        self.patcher.stop()
        self.logger_patcher.stop()

    @classmethod
    def set_up_ivr_info_records(cls):
        for data in cls.IVR_DATA:
            IvrInfo.objects.create(**data)

    # Run this method after setting up IVRs
    @classmethod
    def set_up_groups(cls):
        for data in cls.GROUPS_DATA:
            number_data = (
                data.pop("number_data") if data.get("number_data") else None
            )
            group_ivr_info_relationship_data = (
                data.pop("groupivrinforelationship_data")
                if data.get("groupivrinforelationship_data")
                else None
            )
            group_obj = Group.objects.create(**data)

            number_obj = None
            if number_data:
                number_obj = Number.objects.create(
                    **number_data, group=group_obj
                )

            if group_ivr_info_relationship_data and number_obj:
                ivr_id = group_ivr_info_relationship_data.get(
                    "ivr_info_ivr_id"
                )
                if ivr_id:
                    ivr_obj = IvrInfo.objects.get(ivr_id=ivr_id)
                    GroupIvrInfoRelationship.objects.create(
                        ivr_info=ivr_obj, group=group_obj, number=number_obj
                    )
                else:
                    raise Exception(
                        f"Run this method after creating the IVR with ivr_id - {ivr_id}"
                    )

    @classmethod
    def setUpTestData(cls):
        cls.set_up_ivr_info_records()

    def verify_status_code(
        self,
        status_code: int,
        url: str,
        expected_status_code: int = 200,
        params: Dict = None,
    ):
        msg = f"Response status code in  for url {url} is {status_code}"
        if params:
            msg += f" with params - {params}"

        self.assertEqual(status_code, expected_status_code, msg=msg)

    def verify_response(
        self, response: Dict, url: str, expected_response: Dict
    ):
        self.assertEqual(
            response,
            expected_response,
            msg=f"Response didn't matched  for url {url} with response - {response}",
        )

    def get_ivrs_record(self, filter_by_id=[]):
        if filter_by_id:
            return IvrInfo.objects.filter(id__in=filter_by_id)

        return IvrInfo.objects.all()

    def create_cri_object_from_dict(self, data_dict):
        json_body = {}
        json_body["Body"] = json.dumps(data_dict)
        cri_obj = self.create_cri_requests(json_body)

        return cri_obj, json_body

    def create_cri_requests(
        self, json_body, call_expiry: int = conf.DEFAULT_CALL_EXPIRY
    ):
        body = json.loads(json_body["Body"])
        cri_obj = CentralRequestInfo.objects.create(
            request_id=body["request_id"],
            ivr_id=body["ivr_id"],
            c_id=body["company_id"],
            request_type=body["type"],
            user_id=body.get("user_id", None),
            raw_data=json_body["Body"],
            iteration=0,
            expired_at=datetime.now() + timedelta(seconds=call_expiry),
        )

        return cri_obj

    def run_management_command(self):
        if self.MANAGEMENT_COMMAND:
            call_command(self.MANAGEMENT_COMMAND)
        else:
            raise ValueError(
                "No value found for MANAGEMENT_COMMAND attribute of the class."
            )

    def cleanup_central_1_cache(self):
        cache_keys = CacheManager.get_keys(conf.CACHE_IVR_KEY_NAME)
        cache_keys += conf.CENTRAL_PQ2_COUNT_KEY + conf.CENTRAL_PQ3_COUNT_KEY
        cache_keys += CacheManager.get_keys(conf.CACHE_PAUSED_IVR_KEY_NAME)
        cache_keys += CacheManager.get_keys(conf.CACHE_EXCLUDE_IVR_KEY_NAME)

        for cache_key in cache_keys:
            CacheManager.delete_key(cache_key)

    def setup_requests(self):
        for data in self.REQUESTS_DATA:
            cri_obj, json_data = self.create_cri_object_from_dict(data)
