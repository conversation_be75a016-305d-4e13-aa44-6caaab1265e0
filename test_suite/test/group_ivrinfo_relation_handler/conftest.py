import boto3
import pytest
from moto import mock_aws

from central.settings.handlers import conf
from handlers.group_ivfinfo_relation_handler.main import (
    GroupIvrinfoRelationHandler,
)
from handlers.group_ivfinfo_relation_handler.main_v2 import (
    GroupIvrinfoRelationHandler_V2,
)
from utills.cache_manager.main import CacheManager


@pytest.fixture
def handler_obj(
    setup_cache_for_group_ivr_relation_handler,
    mock_GROUP_IVRINFO_SLEEP_DUR,
):
    setup_cache_for_group_ivr_relation_handler()
    return GroupIvrinfoRelationHandler()


@pytest.fixture
def handler_v2_obj(
    setup_cache_for_group_ivr_relation_handler,
    mock_GROUP_IVRINFO_SLEEP_DUR,
):
    GroupIvrinfoRelationHandler_V2.TOTAL_LOOPS = 1
    setup_cache_for_group_ivr_relation_handler()
    return GroupIvrinfoRelationHandler_V2()


@pytest.fixture()
def mock_group_ivr_info_relation_sqs():
    with mock_aws():
        sqs = boto3.client("sqs", region_name="ap-south-1")
        response = sqs.create_queue(
            QueueName=conf.GROUP_IVRINFO_RELETION_QUEUE,
            Attributes={
                "VisibilityTimeout": "0",
            },
        )
        yield sqs, response


@pytest.fixture()
def mock_GROUP_IVRINFO_SLEEP_DUR():
    original_value = conf.GROUP_IVRINFO_SLEEP_DUR

    conf.GROUP_IVRINFO_SLEEP_DUR = 0
    yield
    conf.GROUP_IVRINFO_SLEEP_DUR = original_value


@pytest.fixture()
def setup_cache_for_group_ivr_relation_handler():
    def wrap(cache_count=10):
        CacheManager.set_value(
            conf.GROUP_IVRINFO_RELETION_COUNT_KEY,
            cache_count,
        )

    yield wrap
