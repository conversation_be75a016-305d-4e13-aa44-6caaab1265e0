from copy import deepcopy
from unittest.mock import patch

from django.core.cache import cache
from django.test import tag

from central.settings.handlers import conf
from handlers.group_ivfinfo_relation_handler.main import (
    GroupIvrinfoRelationHandler,
)
from handlers.models import (
    Group,
    GroupIvrInfoRelationship,
    GroupServerRelationship,
    IvrInfo,
    Number,
    Server,
    SQSQueueInfo,
)
from test_suite.test.base import BaseTestCase
from test_suite.test.data.group_ivr_info_relation_handler.data import (
    GROUP_IVR_EXTERNAL_KAMAILIO_API_DATA,
    GROUP_IVR_FIX_DID_DUMMY_DATA,
    GROUP_IVR_INFO_DATA,
    GROUP_IVR_INFO_DUMMY_GROUP_DATA,
    GROUP_IVR_MAP_DID_EVENT,
    GROUP_IVRINFO_SQS_QUEUE_INFO_DATA,
)
from utills.cache_manager.main import CacheManager


@tag("group_ivr_info_relation_handler")
class GroupIvrInfoRelationHandler(BaseTestCase):
    IVR_DATA = GROUP_IVR_INFO_DATA
    GROUPS_DATA = GROUP_IVR_INFO_DUMMY_GROUP_DATA
    GROUP_IVRINFO_RELETION_COUNT_KEY_VALUE = 10

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        for data in GROUP_IVRINFO_SQS_QUEUE_INFO_DATA:
            SQSQueueInfo.objects.create(**data)

        cls.set_up_groups()

    def setUp(self):
        super().setUp()
        cache.clear()

        CacheManager.set_value(
            conf.GROUP_IVRINFO_RELETION_COUNT_KEY,
            self.GROUP_IVRINFO_RELETION_COUNT_KEY_VALUE,
        )

        self.sqs_delete_message_patch = patch(
            "utills.sqs_manager.main.SQSManager.sqs_delete_message"
        )
        self.sqs_delete_message_mock = self.sqs_delete_message_patch.start()

        self.external_api_kamilio_patch = patch(
            "utills.external_apis.ExternalAPIs.talk_to_kamailio_api",
            return_value=deepcopy(
                GROUP_IVR_EXTERNAL_KAMAILIO_API_DATA["result"]
            ),
        )
        self.external_api_kamilio_mock = (
            self.external_api_kamilio_patch.start()
        )

        self.external_api_retrieve_did_patch = patch(
            "utills.external_apis.ExternalAPIs.talk_to_retrieve_did_api"
        )
        self.external_api_retrieve_did_mock = (
            self.external_api_retrieve_did_patch.start()
        )

        self.sqs_create_queue_patch = patch(
            "utills.helpers.helper.SQSManager.create_queue",
            side_effect=self.get_group_queues(),
        )

        self.sqs_create_queue_mock = self.sqs_create_queue_patch.start()

        self.group_ivrinfo_sleep_dur_patch = patch(
            "central.settings.handlers.conf.GROUP_IVRINFO_SLEEP_DUR", 0
        )
        self.group_ivrinfo_sleep_dur_mock = (
            self.group_ivrinfo_sleep_dur_patch.start()
        )

    def tearDown(self):
        super().tearDown()
        cache.clear()

        self.sqs_delete_message_patch.stop()
        self.external_api_kamilio_patch.stop()
        self.external_api_retrieve_did_patch.stop()
        self.sqs_create_queue_patch.stop()
        self.group_ivrinfo_sleep_dur_patch.stop()

    def get_group_queues(self):
        return SQSQueueInfo.objects.filter(
            gateway_prefix=conf.GROUPS_GATEWAY
        ).values_list("queue_url", flat=True)

    def _test_delete_request(
        self, queue_url, receipt_handle, count_key_value=0
    ):
        count_key_value = (
            count_key_value or self.GROUP_IVRINFO_RELETION_COUNT_KEY_VALUE
        )
        self.sqs_delete_message_mock.assert_called_once_with(
            queue_url, receipt_handle
        )

        value = CacheManager.get_value(conf.GROUP_IVRINFO_RELETION_COUNT_KEY)

        self.assertEqual(value, count_key_value - 1)

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_delete_request(self, run_mock):
        handler_obj = GroupIvrinfoRelationHandler()
        queue_url = handler_obj.get_queue_url()
        receipt_handle = "some_receipt_handle"

        handler_obj.delete_request(receipt_handle)
        self._test_delete_request(queue_url, receipt_handle)

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_get_number_obj(self, run_mock):
        handler_obj = GroupIvrinfoRelationHandler()
        group = Group.objects.first()

        self.assertIsNotNone(group)

        number_obj = handler_obj.get_number_obj(
            GROUP_IVR_FIX_DID_DUMMY_DATA["data"], group.id
        )

        self.assertIsNotNone(number_obj)

        self.assertEqual(number_obj.is_fix_did, Number.YES)

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_get_group_obj_with_existing_group(self, run_mock):
        handler_obj = GroupIvrinfoRelationHandler()
        data = GROUP_IVR_FIX_DID_DUMMY_DATA["data"]
        group_to_be_found = Group.objects.filter(
            group_alias=data["group_alias"]
        ).first()

        founded_group = handler_obj.get_group_obj(data)

        self.assertIsNotNone(founded_group)
        self.assertEqual(group_to_be_found.id, founded_group.id)

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_get_group_obj_without_existing_group(self, run_mock):
        handler_obj = GroupIvrinfoRelationHandler()
        # Deleting Existing Groups
        Group.objects.all().delete()
        data = GROUP_IVR_FIX_DID_DUMMY_DATA["data"]
        founded_group = handler_obj.get_group_obj(data)

        self.assertIsNotNone(founded_group)

        self.assertEqual(str(founded_group.kam_group_id), data["kam_group_id"])

        # Whether servers and group_server_relation were created or not
        for server in GROUP_IVR_EXTERNAL_KAMAILIO_API_DATA["result"][
            data["kam_group_id"]
        ]["servers"]:
            server_qs = Server.objects.filter(name=server)

            self.assertTrue(server_qs.exists())

            group_server_relation_obj = GroupServerRelationship.objects.filter(
                group_id=founded_group.id, server_id=server_qs.first().id
            )

            self.assertTrue(group_server_relation_obj.exists())

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_is_valid_data(self, run_mock):
        handler_obj = GroupIvrinfoRelationHandler()
        # If data is valid, the returned value should be true
        is_valid = handler_obj.is_valid_data(GROUP_IVR_MAP_DID_EVENT["data"])

        self.assertTrue(is_valid)

        event_copy_data = deepcopy(GROUP_IVR_MAP_DID_EVENT["data"])

        for k, v in handler_obj.data_fields.items():
            event_copy_data.pop(v)

        # If data is invalid, the returned value should be false
        is_valid = handler_obj.is_valid_data(event_copy_data)

        self.assertFalse(is_valid)

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_is_discardable(self, run_mock):
        handler_obj = GroupIvrinfoRelationHandler()
        # If data is not discardable, the returned value should be False

        is_discardable = handler_obj.is_discardable(GROUP_IVR_MAP_DID_EVENT)

        self.assertFalse(is_discardable)

        event_copy_data = GROUP_IVR_MAP_DID_EVENT

        event_copy_data["event"]["action"] = handler_obj.actions[
            "discard_actions"
        ][0]

        # If data is discardable, the returned value should be True
        is_discardable = handler_obj.is_discardable(event_copy_data)

        self.assertTrue(is_discardable)

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_get_queue_url(self, run_mock):
        handler_obj = GroupIvrinfoRelationHandler()
        expected_queue_url = None

        for queue_data in GROUP_IVRINFO_SQS_QUEUE_INFO_DATA:
            if (
                queue_data["gateway_prefix"]
                == conf.GROUP_IVRINFO_RELETION_QUEUE_GATEWAY_PREFIX
            ):
                expected_queue_url = queue_data["queue_url"]

        queue_url = handler_obj.get_queue_url()

        self.assertEqual(expected_queue_url, queue_url)

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_is_cache_key_exists(self, run_mock):
        handler_obj = GroupIvrinfoRelationHandler()

        cache_exists = handler_obj.is_cache_key_exists()
        self.assertTrue(cache_exists)

        cache.clear()
        cache_exists = handler_obj.is_cache_key_exists()
        self.assertFalse(cache_exists)

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_unmap_dids_success_response(self, run_mock):
        handler_obj = GroupIvrinfoRelationHandler()
        receipt_handle = "some_receipt_handle"

        data = GROUP_IVR_FIX_DID_DUMMY_DATA["data"]
        c_id = self.IVR_DATA[0]["c_id"]
        fix_did = GROUP_IVR_MAP_DID_EVENT["data"]["did"]

        self.external_api_retrieve_did_mock.return_value = (data, "200")
        handler_obj.unmap_dids(
            c_id, fix_did, receipt_handle, GROUP_IVR_MAP_DID_EVENT
        )

        ivrs_to_ignored = [ivr["id"] for ivr in data["ivrs"]]

        qs = GroupIvrInfoRelationship.objects.filter(
            ivr_info__c_id=c_id, number__number=fix_did
        ).exclude(ivr_info__ivr_id__in=ivrs_to_ignored)

        self.assertFalse(qs.exists())

        queue_url = handler_obj.get_queue_url()
        self._test_delete_request(queue_url, receipt_handle)

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_unmap_dids_failed_response(self, run_mock):
        handler_obj = GroupIvrinfoRelationHandler()
        receipt_handle = "some_receipt_handle"

        data = GROUP_IVR_FIX_DID_DUMMY_DATA["data"]
        c_id = self.IVR_DATA[0]["c_id"]
        fix_did = GROUP_IVR_MAP_DID_EVENT["data"]["did"]

        self.external_api_retrieve_did_mock.return_value = ({}, "404")
        handler_obj.unmap_dids(
            c_id, fix_did, receipt_handle, GROUP_IVR_MAP_DID_EVENT
        )

        group_qs = GroupIvrInfoRelationship.objects.filter(
            ivr_info__c_id=c_id, number__number=fix_did
        )

        self.assertFalse(group_qs.exists())

        number_qs = Number.objects.filter(number=fix_did)

        self.assertFalse(number_qs.exists())

        queue_url = handler_obj.get_queue_url()
        self._test_delete_request(queue_url, receipt_handle)

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_map_dids_failed_response(self, run_mock):
        # Case 1: if not fixdid_data

        # Deleting existing ones,as the job of map_did method is to create the group_ivr_info_relationship
        GroupIvrInfoRelationship.objects.all().delete()

        handler_obj = GroupIvrinfoRelationHandler()
        receipt_handle = "some_receipt_handle"

        c_id = self.IVR_DATA[0]["c_id"]
        fix_did = GROUP_IVR_MAP_DID_EVENT["data"]["did"]

        self.external_api_retrieve_did_mock.return_value = ({}, "404")

        handler_obj.map_dids(
            c_id, fix_did, receipt_handle, GROUP_IVR_MAP_DID_EVENT
        )

        queue_url = handler_obj.get_queue_url()
        self._test_delete_request(queue_url, receipt_handle)

        # this shouldn't exist as any existing record were deleted initially.
        group_ivr_qs = GroupIvrInfoRelationship.objects.all()

        self.assertFalse(group_ivr_qs.exists())

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_unmap_did_is_deletable_event(self, run_mock):
        handler_obj = GroupIvrinfoRelationHandler()
        receipt_handle = "some_receipt_handle"
        invalid_res_type_res = deepcopy(GROUP_IVR_FIX_DID_DUMMY_DATA["data"])
        invalid_res_type_res["type"] = conf.FIX_DID_OUTGOING_TYPE + 2

        invalid_version_res = deepcopy(GROUP_IVR_FIX_DID_DUMMY_DATA["data"])
        invalid_version_res["version"] = conf.FIX_DID_VERSION + "2"
        for i, data in enumerate(
            [
                invalid_res_type_res,
                invalid_version_res,
            ]
        ):
            c_id = self.IVR_DATA[0]["c_id"]
            fix_did = GROUP_IVR_MAP_DID_EVENT["data"]["did"]

            self.external_api_retrieve_did_mock.return_value = (
                data,
                "200",
            )
            handler_obj.unmap_dids(
                c_id, fix_did, receipt_handle, GROUP_IVR_MAP_DID_EVENT
            )

            group_qs = GroupIvrInfoRelationship.objects.filter(
                ivr_info__c_id=c_id, number__number=fix_did
            )

            self.assertFalse(group_qs.exists())

            number_qs = Number.objects.filter(number=fix_did)

            self.assertFalse(number_qs.exists())

            queue_url = handler_obj.get_queue_url()
            self._test_delete_request(
                queue_url,
                receipt_handle,
                self.GROUP_IVRINFO_RELETION_COUNT_KEY_VALUE - i,
            )
            self.sqs_delete_message_mock.reset_mock()

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_map_dids_deletable_event(self, run_mock):
        # Deleting existing ones, as the job of map_did method is to create the GROUP_IVR_INFO_RELATIONSHIP
        GroupIvrInfoRelationship.objects.all().delete()

        handler_obj = GroupIvrinfoRelationHandler()
        receipt_handle = "some_receipt_handle"

        empty_kam_group_id_res = deepcopy(GROUP_IVR_FIX_DID_DUMMY_DATA["data"])
        empty_kam_group_id_res["kam_group_id"] = ""

        empty_ivr_res = deepcopy(GROUP_IVR_FIX_DID_DUMMY_DATA["data"])
        empty_ivr_res["ivrs"] = []

        invalid_ivr_id_res = deepcopy(GROUP_IVR_FIX_DID_DUMMY_DATA["data"])
        invalid_ivr_id_res["ivrs"] = [{"id": "Xyz"}]

        invalid_res_type_res = deepcopy(GROUP_IVR_FIX_DID_DUMMY_DATA["data"])
        invalid_res_type_res["type"] = conf.FIX_DID_OUTGOING_TYPE + 2

        invalid_version_res = deepcopy(GROUP_IVR_FIX_DID_DUMMY_DATA["data"])
        invalid_version_res["version"] = conf.FIX_DID_VERSION + "2"

        for i, data in enumerate(
            [
                empty_kam_group_id_res,
                empty_ivr_res,
                invalid_ivr_id_res,
                invalid_res_type_res,
                invalid_version_res,
            ]
        ):
            c_id = self.IVR_DATA[0]["c_id"]
            fix_did = GROUP_IVR_MAP_DID_EVENT["data"]["did"]

            self.external_api_retrieve_did_mock.return_value = (data, "200")

            handler_obj.map_dids(
                c_id, fix_did, receipt_handle, GROUP_IVR_MAP_DID_EVENT
            )

            queue_url = handler_obj.get_queue_url()

            self._test_delete_request(
                queue_url,
                receipt_handle,
                self.GROUP_IVRINFO_RELETION_COUNT_KEY_VALUE - i,
            )
            self.sqs_delete_message_mock.reset_mock()

            # this shouldn't exist as any existing record were deleted initially.
            group_ivr_qs = GroupIvrInfoRelationship.objects.all()

            self.assertFalse(
                group_ivr_qs.exists(), f"Object exists for data- {data} "
            )

    # Manually returning None from get_group_obj method to test the case
    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.get_group_obj",
        return_value=None,
    )
    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_map_dids_group_obj_not_found(self, run_mock, get_group_obj_mock):
        # Case 4: If group obj not found

        # Deleting existing ones, as the job of map_did method is to create the GROUP_IVR_INFO_RELATIONSHIP
        GroupIvrInfoRelationship.objects.all().delete()

        handler_obj = GroupIvrinfoRelationHandler()
        receipt_handle = "some_receipt_handle"

        data = deepcopy(GROUP_IVR_FIX_DID_DUMMY_DATA["data"])

        c_id = self.IVR_DATA[0]["c_id"]
        fix_did = GROUP_IVR_MAP_DID_EVENT["data"]["did"]

        self.external_api_retrieve_did_mock.return_value = (data, "200")

        handler_obj.map_dids(
            c_id, fix_did, receipt_handle, GROUP_IVR_MAP_DID_EVENT
        )

        # Checking whether the message was deleted from queue or cache value was decreased.
        count_key_value = self.GROUP_IVRINFO_RELETION_COUNT_KEY_VALUE
        self.assertEqual(self.sqs_delete_message_mock.call_count, 0)

        value = CacheManager.get_value(conf.GROUP_IVRINFO_RELETION_COUNT_KEY)

        self.assertEqual(value, count_key_value)

        # this shouldn't exist as any existing record were deleted initially.
        group_ivr_qs = GroupIvrInfoRelationship.objects.all()

        self.assertFalse(group_ivr_qs.exists())

    @patch(
        "handlers.group_ivfinfo_relation_handler.main.GroupIvrinfoRelationHandler.run"
    )
    def test_map_dids(self, run_mock):
        # Case 5: Successfully Ran

        # Deleting existing ones, as the job of map_did method is to create the GROUP_IVR_INFO_RELATIONSHIP
        GroupIvrInfoRelationship.objects.all().delete()

        handler_obj = GroupIvrinfoRelationHandler()
        receipt_handle = "some_receipt_handle"

        data = deepcopy(GROUP_IVR_FIX_DID_DUMMY_DATA["data"])

        c_id = self.IVR_DATA[0]["c_id"]
        fix_did = GROUP_IVR_MAP_DID_EVENT["data"]["did"]

        self.external_api_retrieve_did_mock.return_value = (data, "200")

        handler_obj.map_dids(
            c_id, fix_did, receipt_handle, GROUP_IVR_MAP_DID_EVENT
        )

        group_obj = handler_obj.get_group_obj(data)
        number_obj = handler_obj.get_number_obj(data, group_obj.id)

        ivrinfo_obj = IvrInfo.objects.filter(
            ivr_id=data["ivrs"][0]["id"], c_id=c_id
        ).first()

        group_ivr_relation = GroupIvrInfoRelationship.objects.filter(
            group_id=group_obj.id,
            ivr_info_id=ivrinfo_obj.id,
            number_id=number_obj.id,
        )

        self.assertTrue(group_ivr_relation.exists())
