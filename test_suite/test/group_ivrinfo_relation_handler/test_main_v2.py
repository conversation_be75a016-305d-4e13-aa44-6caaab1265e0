import json

from django.conf import settings
from django.test.utils import override_settings
from rest_framework import status

import pytest
from moto import mock_aws

from handlers.group_ivfinfo_relation_handler.main_v2 import (
    GroupIvrinfoRelationHandler_V2,
)
from handlers.models import (
    Group,
    GroupIvrInfoRelationship,
    Number,
    SQSQueueInfo,
)
from test_suite.factories.groups import GroupFactory, NumberFactory
from test_suite.factories.ivr_info import IvrInfoFactory
from utills.cache_manager.main import CacheManager
from utills.sqs_manager.sqs_event import FixDidSqsEvent


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_v2_get_queue_url(
    mock_group_ivr_info_relation_sqs, handler_v2_obj
):
    SQSQueueInfo.objects.all().delete()
    _, res = mock_group_ivr_info_relation_sqs
    assert handler_v2_obj.get_queue_url() == res["QueueUrl"]


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_v2_cache_key_exists(
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    assert handler_v2_obj.cache_key_exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_v2_cache_key_not_exists(
    mock_group_ivr_info_relation_sqs,
):
    handler_v2_obj = GroupIvrinfoRelationHandler_V2()
    assert not handler_v2_obj.cache_key_exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_v2_delete_request_from_sqs_and_decr_cache(
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    sqs_client, res = mock_group_ivr_info_relation_sqs
    sqs_client.send_message(
        QueueUrl=res["QueueUrl"],
        MessageBody=json.dumps({"data": {"some": "Text"}}),
    )

    messages = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    event = FixDidSqsEvent(messages["Messages"][0])

    handler_v2_obj.delete_request_from_sqs_and_decr_cache(event)

    messages = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    assert not messages.get("Messages")

    assert (
        CacheManager.get_value(settings.GROUP_IVRINFO_RELETION_COUNT_KEY) == 9
    )


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_v2_get_group_obj_if_not_found_in_db(
    load_json,
    mock_kam_group_servers_api,
    mock_fix_did_pilot_list_api,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    fix_did_response = load_json("external_apis/fix_did/fix_did_detail.json")
    kam_api_response = load_json("external_apis/kamailio/group_servers.json")
    mock_kam_group_servers_api(api_response=kam_api_response)

    kam_group_id = list(kam_api_response["result"].keys())[0]
    group_data = kam_api_response["result"][kam_group_id]

    fix_did_response["data"]["kam_group_id"] = kam_group_id

    group_data.pop("servers")
    group_data["group_type"] = Group.AWS_GROUP_TYPE

    mock_fix_did_pilot_list_api(
        fix_did_response["data"]["pilot_number"],
        server_group=Group.AWS_GROUP_TYPE,
    )
    group = handler_v2_obj.get_group_obj(fix_did_response["data"])

    assert group

    assert Group.objects.filter(
        assigned_queues={},
        name=group_data.pop("name"),
        group_alias=group_data.pop("group_alias"),
        region=group_data.pop("region"),
        is_enable=Group.YES,
        is_default=Group.NO,
        kam_group_id=kam_group_id,
        settings=group_data,
        pk=group.pk,
        group_priority=fix_did_response["data"]["group_priority"],
    ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_v2_get_group_obj_if_found_in_db(
    load_json,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    fix_did_response = load_json("external_apis/fix_did/fix_did_detail.json")

    group = GroupFactory(kam_group_id=fix_did_response["data"]["kam_group_id"])
    group_fetched = handler_v2_obj.get_group_obj(fix_did_response["data"])
    assert group_fetched.pk == group.pk


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_v2_get_group_data_from_kamailio(
    load_json,
    mock_kam_group_servers_api,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    fix_did_response = load_json("external_apis/fix_did/fix_did_detail.json")
    kam_api_response = load_json("external_apis/kamailio/group_servers.json")
    mock_kam_group_servers_api(api_response=kam_api_response)
    group_data = handler_v2_obj.get_group_data_from_kamailio(
        fix_did_response["data"]["kam_group_id"]
    )

    assert group_data == kam_api_response["result"]


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_v2_get_group_data_from_kamailio_failed(
    load_json,
    mock_kam_group_servers_api,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    fix_did_response = load_json("external_apis/fix_did/fix_did_detail.json")

    mock_kam_group_servers_api(status_code=status.HTTP_400_BAD_REQUEST)
    group_data = handler_v2_obj.get_group_data_from_kamailio(
        fix_did_response["data"]["kam_group_id"]
    )

    assert not group_data


@pytest.mark.unittest
@pytest.mark.django_db
@override_settings(GROUP_IVRINFO_SLEEP_DUR=0)
@mock_aws
def test_gir_handler_run_no_cache_key_exists(
    load_json,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    CacheManager.delete_key(settings.GROUP_IVRINFO_RELETION_COUNT_KEY)
    handler_v2_obj.run()
    assert not GroupIvrInfoRelationship.objects.all().exists()
    assert not Number.objects.all().exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_run_no_message_in_sqs(
    load_json,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    handler_v2_obj.run()
    assert not GroupIvrInfoRelationship.objects.all().exists()
    assert not Number.objects.all().exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_run_book_event(
    load_json,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    fix_did_book_event = load_json("sqs/fix_did_book_did.json")

    sqs_client, sqs_res = mock_group_ivr_info_relation_sqs
    sqs_client.send_message(
        QueueUrl=sqs_res["QueueUrl"],
        MessageBody=json.dumps(fix_did_book_event),
    )
    handler_v2_obj.run()

    messages = sqs_client.receive_message(
        QueueUrl=sqs_res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    assert not messages.get("Messages")
    assert (
        CacheManager.get_value(settings.GROUP_IVRINFO_RELETION_COUNT_KEY) == 9
    )

    assert not GroupIvrInfoRelationship.objects.all().exists()
    assert not Number.objects.all().exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_run_invalid_event(
    load_json,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    fix_did_event = load_json("sqs/fix_did_book_did.json")
    fix_did_event["event"]["action"] = "test"

    sqs_client, sqs_res = mock_group_ivr_info_relation_sqs
    sqs_client.send_message(
        QueueUrl=sqs_res["QueueUrl"],
        MessageBody=json.dumps(fix_did_event),
    )
    handler_v2_obj.run()

    messages = sqs_client.receive_message(
        QueueUrl=sqs_res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    assert not messages.get("Messages")
    assert (
        CacheManager.get_value(settings.GROUP_IVRINFO_RELETION_COUNT_KEY) == 9
    )

    assert not GroupIvrInfoRelationship.objects.all().exists()
    assert not Number.objects.all().exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_run_invalid_map_event(
    load_json,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    fix_did_event = load_json("sqs/fix_did_map_did.json")
    del fix_did_event["data"]["did"]

    sqs_client, sqs_res = mock_group_ivr_info_relation_sqs
    sqs_client.send_message(
        QueueUrl=sqs_res["QueueUrl"],
        MessageBody=json.dumps(fix_did_event),
    )
    handler_v2_obj.run()

    messages = sqs_client.receive_message(
        QueueUrl=sqs_res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    assert not messages.get("Messages")
    assert (
        CacheManager.get_value(settings.GROUP_IVRINFO_RELETION_COUNT_KEY) == 9
    )

    assert not GroupIvrInfoRelationship.objects.all().exists()
    assert not Number.objects.all().exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "payload,exception",
    [
        ("'test", json.JSONDecodeError),
        ('"test"', TypeError),
        ('{"some": "test"}', KeyError),
    ],
)
def test_gir_handler_run_json_error(
    payload,
    exception,
    load_json,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    sqs_client, sqs_res = mock_group_ivr_info_relation_sqs
    sqs_client.send_message(
        QueueUrl=sqs_res["QueueUrl"],
        MessageBody=payload,
    )

    with pytest.raises(exception):
        handler_v2_obj.run()

    messages = sqs_client.receive_message(
        QueueUrl=sqs_res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    assert not messages.get("Messages")
    assert (
        CacheManager.get_value(settings.GROUP_IVRINFO_RELETION_COUNT_KEY) == 9
    )

    assert not GroupIvrInfoRelationship.objects.all().exists()
    assert not Number.objects.all().exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_run_with_map_event(
    load_json,
    mock_fix_did_detail_api,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    ivr = IvrInfoFactory()

    fix_did_map_event = load_json("sqs/fix_did_map_did.json")
    fix_did_detail_response = load_json(
        "external_apis/fix_did/fix_did_detail.json"
    )

    fix_did_map_event["data"]["company_id"] = ivr.c_id

    group: Group = GroupFactory(
        kam_group_id=fix_did_detail_response["data"]["kam_group_id"],
        is_enable=Group.YES,
        is_default=Group.NO,
    )

    mock_fix_did_detail_api(
        ivr.c_id,
        fix_did_map_event["data"]["did"],
        ivr.ivr_id,
    )

    sqs_client, sqs_res = mock_group_ivr_info_relation_sqs
    sqs_client.send_message(
        QueueUrl=sqs_res["QueueUrl"], MessageBody=json.dumps(fix_did_map_event)
    )

    handler_v2_obj.run()

    messages = sqs_client.receive_message(
        QueueUrl=sqs_res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    assert not messages.get("Messages")
    assert (
        CacheManager.get_value(settings.GROUP_IVRINFO_RELETION_COUNT_KEY) == 9
    )

    for did in fix_did_map_event["data"]["did"]:
        number = Number.objects.filter(
            number=did,
            number_priority=fix_did_detail_response["data"]["number_priority"],
            group_id=group.pk,
            is_fix_did=Number.YES,
        ).first()

        assert number

        assert GroupIvrInfoRelationship.objects.filter(
            group_id=group.pk,
            ivr_info_id=ivr.pk,
            number_id=number.pk,
        ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_run_with_map_event_with_empty_dids(
    load_json,
    mock_fix_did_detail_api,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    ivr = IvrInfoFactory()

    fix_did_map_event = load_json("sqs/fix_did_map_did.json")
    fix_did_detail_response = load_json(
        "external_apis/fix_did/fix_did_detail.json"
    )

    fix_did_map_event["data"]["company_id"] = ivr.c_id
    fix_did_map_event["data"]["did"] = []

    group: Group = GroupFactory(
        kam_group_id=fix_did_detail_response["data"]["kam_group_id"],
        is_enable=Group.YES,
        is_default=Group.NO,
    )

    mock_fix_did_detail_api(
        ivr.c_id,
        fix_did_map_event["data"]["did"],
        ivr.ivr_id,
    )

    sqs_client, sqs_res = mock_group_ivr_info_relation_sqs
    sqs_client.send_message(
        QueueUrl=sqs_res["QueueUrl"], MessageBody=json.dumps(fix_did_map_event)
    )

    handler_v2_obj.run()

    messages = sqs_client.receive_message(
        QueueUrl=sqs_res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    assert not messages.get("Messages")
    assert (
        CacheManager.get_value(settings.GROUP_IVRINFO_RELETION_COUNT_KEY) == 9
    )

    assert not Number.objects.all().exists()
    assert not GroupIvrInfoRelationship.objects.all().exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_run_with_map_event_did_detail_not_found(
    load_json,
    mock_fix_did_detail_api,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    ivr = IvrInfoFactory()

    fix_did_map_event = load_json("sqs/fix_did_map_did.json")
    fix_did_detail_response = load_json(
        "external_apis/fix_did/fix_did_detail.json"
    )

    fix_did_map_event["data"]["company_id"] = ivr.c_id

    mock_fix_did_detail_api(
        ivr.c_id,
        fix_did_map_event["data"]["did"],
        ivr.ivr_id,
        status_code=status.HTTP_404_NOT_FOUND,
    )

    sqs_client, sqs_res = mock_group_ivr_info_relation_sqs
    sqs_client.send_message(
        QueueUrl=sqs_res["QueueUrl"], MessageBody=json.dumps(fix_did_map_event)
    )

    handler_v2_obj.run()

    messages = sqs_client.receive_message(
        QueueUrl=sqs_res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    assert not messages.get("Messages")
    assert (
        CacheManager.get_value(settings.GROUP_IVRINFO_RELETION_COUNT_KEY) == 9
    )

    for did in fix_did_map_event["data"]["did"]:
        number = Number.objects.filter(
            number=did,
            is_fix_did=Number.YES,
        ).first()

        assert not number

        assert not GroupIvrInfoRelationship.objects.all().exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_run_with_map_event_non_outgoing_did(
    load_json,
    mock_fix_did_detail_api,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    ivr = IvrInfoFactory()

    fix_did_map_event = load_json("sqs/fix_did_map_did.json")
    fix_did_detail_response = load_json(
        "external_apis/fix_did/fix_did_detail.json"
    )

    fix_did_map_event["data"]["company_id"] = ivr.c_id
    fix_did_detail_response["data"]["type"] = 10

    mock_fix_did_detail_api(
        ivr.c_id,
        fix_did_map_event["data"]["did"],
        ivr.ivr_id,
        fix_did_response=fix_did_detail_response,
    )

    sqs_client, sqs_res = mock_group_ivr_info_relation_sqs
    sqs_client.send_message(
        QueueUrl=sqs_res["QueueUrl"], MessageBody=json.dumps(fix_did_map_event)
    )

    handler_v2_obj.run()

    messages = sqs_client.receive_message(
        QueueUrl=sqs_res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    assert not messages.get("Messages")
    assert (
        CacheManager.get_value(settings.GROUP_IVRINFO_RELETION_COUNT_KEY) == 9
    )

    for did in fix_did_map_event["data"]["did"]:
        number = Number.objects.filter(
            number=did,
            is_fix_did=Number.YES,
        ).first()

        assert not number

        assert not GroupIvrInfoRelationship.objects.all().exists()


@pytest.mark.unittest
@pytest.mark.django_db
@pytest.mark.parametrize(
    "key", ["ivrs", "kam_group_id", "group_priority", "pilot_number"]
)
@mock_aws
def test_gir_handler_run_with_map_event_required_fields_missing_in_did_detail(
    key,
    load_json,
    mock_fix_did_detail_api,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    ivr = IvrInfoFactory()

    fix_did_map_event = load_json("sqs/fix_did_map_did.json")
    fix_did_detail_response = load_json(
        "external_apis/fix_did/fix_did_detail.json"
    )
    kam_group_id = fix_did_detail_response["data"]["kam_group_id"]
    fix_did_detail_response["data"][key] = []
    fix_did_map_event["data"]["company_id"] = ivr.c_id

    mock_fix_did_detail_api(
        ivr.c_id,
        fix_did_map_event["data"]["did"],
        ivr.ivr_id,
        fix_did_response=fix_did_detail_response,
    )

    group: Group = GroupFactory(
        kam_group_id=kam_group_id,
        is_enable=Group.YES,
        is_default=Group.NO,
    )

    sqs_client, sqs_res = mock_group_ivr_info_relation_sqs
    sqs_client.send_message(
        QueueUrl=sqs_res["QueueUrl"], MessageBody=json.dumps(fix_did_map_event)
    )

    handler_v2_obj.run()

    messages = sqs_client.receive_message(
        QueueUrl=sqs_res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    assert not messages.get("Messages")
    assert (
        CacheManager.get_value(settings.GROUP_IVRINFO_RELETION_COUNT_KEY) == 9
    )

    for did in fix_did_map_event["data"]["did"]:
        number = Number.objects.filter(
            number=did, is_fix_did=Number.YES, group_id=group.pk
        ).first()

        assert not number

        assert not GroupIvrInfoRelationship.objects.all().exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_run_with_map_event_no_ivr_in_db(
    load_json,
    mock_fix_did_detail_api,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    ivr = IvrInfoFactory()

    fix_did_map_event = load_json("sqs/fix_did_map_did.json")
    fix_did_detail_response = load_json(
        "external_apis/fix_did/fix_did_detail.json"
    )
    fix_did_map_event["data"]["company_id"] = ivr.c_id

    mock_fix_did_detail_api(
        ivr.c_id,
        fix_did_map_event["data"]["did"],
        "123",
        fix_did_response=fix_did_detail_response,
    )

    group: Group = GroupFactory(
        kam_group_id=fix_did_detail_response["data"]["kam_group_id"],
        is_enable=Group.YES,
        is_default=Group.NO,
    )

    sqs_client, sqs_res = mock_group_ivr_info_relation_sqs
    sqs_client.send_message(
        QueueUrl=sqs_res["QueueUrl"], MessageBody=json.dumps(fix_did_map_event)
    )

    handler_v2_obj.run()

    messages = sqs_client.receive_message(
        QueueUrl=sqs_res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    assert not messages.get("Messages")
    assert (
        CacheManager.get_value(settings.GROUP_IVRINFO_RELETION_COUNT_KEY) == 9
    )

    for did in fix_did_map_event["data"]["did"]:
        number = Number.objects.filter(
            number=did, is_fix_did=Number.YES, group_id=group.pk
        ).first()

        assert number

        assert not GroupIvrInfoRelationship.objects.all().exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gir_handler_run_with_unmap_event(
    load_json,
    mock_fix_did_detail_api,
    mock_group_ivr_info_relation_sqs,
    handler_v2_obj,
):
    ivr = IvrInfoFactory()

    fix_did_event = load_json("sqs/fix_did_unmap_did.json")
    fix_did_detail_response = load_json(
        "external_apis/fix_did/fix_did_detail.json"
    )

    fix_did_detail_response["data"]["ivrs"] = []

    fix_did_event["data"]["company_id"] = ivr.c_id

    group: Group = GroupFactory(
        kam_group_id=fix_did_detail_response["data"]["kam_group_id"],
        is_enable=Group.YES,
        is_default=Group.NO,
    )
    for did in fix_did_event["data"]["did"]:
        number = NumberFactory(number=did, group=group, is_fix_did=Number.YES)
        GroupIvrInfoRelationship.objects.create(
            group_id=group.id, ivr_info_id=ivr.id, number_id=number.id
        )

    mock_fix_did_detail_api(
        ivr.c_id,
        fix_did_event["data"]["did"],
        ivr.ivr_id,
        fix_did_response=fix_did_detail_response,
    )

    sqs_client, sqs_res = mock_group_ivr_info_relation_sqs
    sqs_client.send_message(
        QueueUrl=sqs_res["QueueUrl"], MessageBody=json.dumps(fix_did_event)
    )

    handler_v2_obj.run()

    messages = sqs_client.receive_message(
        QueueUrl=sqs_res["QueueUrl"],
        WaitTimeSeconds=0,
        MaxNumberOfMessages=10,
    )
    assert not messages.get("Messages")
    assert (
        CacheManager.get_value(settings.GROUP_IVRINFO_RELETION_COUNT_KEY) == 9
    )

    for did in fix_did_event["data"]["did"]:
        number = Number.objects.filter(
            number=did,
            number_priority=fix_did_detail_response["data"]["number_priority"],
            group_id=group.pk,
            is_fix_did=Number.YES,
        ).first()

        assert not number

        assert not GroupIvrInfoRelationship.objects.filter(
            group_id=group.pk,
            ivr_info_id=ivr.pk,
        ).exists()
