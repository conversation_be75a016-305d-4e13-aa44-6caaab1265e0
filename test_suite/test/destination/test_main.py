import json
from copy import deepcopy
from typing import Dict

from django.conf import settings
from rest_framework import status

import pytest
from moto import mock_aws

from handlers.models import CentralRequestInfo, Group, IvrInfo, Number
from test_suite.factories.groups import GroupFactory, NumberFactory
from test_suite.test.factories import IvrInfoFactory
from utills.destination.group_destination import GroupDestination
from utills.exceptions import DestinationResponseException
from utills.helpers.helper import Helper
from utills.shared_cache_manager.main import SharedCacheManager


def get_aws_group_data(ivr: IvrInfo):
    return {
        "source_number": "9999999990",
        "is_aws": True,
        "ivr_type": ivr.ivr_type,
    }


def get_non_aws_group_data(ivr: IvrInfo):
    group = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(22)),
        kam_group_id=22,
        settings={"group_type": Group.PHY_GROUP_TYPE},
    )
    number = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999999",
        group=group,
    )

    return {
        "url": group.get_assigned_queues["p_q"]["url"],
        "name": group.get_assigned_queues["p_q"]["name"],
        "ivr_settings": ivr.common_setting,
        "ivr_type": ivr.ivr_type,
        "source_number": number.number,
        "group_settings": group.settings,
        "is_aws": False,
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "get_group_data",
    [get_aws_group_data, get_non_aws_group_data],
)
def test_group_destination_prepare_payload_for_type_1(
    get_group_data, load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_1.json")
    udc_response = load_json(
        "external_apis/udc/user_view_only_enabled_success.json",
    )

    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    ivr = IvrInfoFactory(
        ivr_id=cri_obj.ivr_id, common_setting={"book_user": False}
    )

    group_data = get_group_data(ivr)

    expected_payload = deepcopy(cri_request)
    expected_payload.update(
        {
            "group_settings": group_data.get("group_settings", {}),
            "ivr_settings": group_data.get("ivr_settings", {}),
            "source_number": group_data["source_number"],
            "ivr_type": group_data["ivr_type"],
            "number_2_cc": udc_response["data"]["user_data"][
                "contact_country"
            ],
            "number_2": udc_response["data"]["user_data"]["contact"],
        }
    )

    destination = GroupDestination()
    prepared_payload = destination.prepare_payload(
        cri_obj.body, group_data, udc_response["data"]
    )
    assert prepared_payload == expected_payload


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "get_group_data",
    [get_aws_group_data, get_non_aws_group_data],
)
def test_group_destination_prepare_payload_for_type_2(
    get_group_data, load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_2.json")
    udc_response = load_json(
        "external_apis/udc/user_view_only_enabled_success.json",
    )

    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    group_data = get_group_data(ivr)

    expected_payload = deepcopy(cri_request)
    expected_payload.update(
        {
            "group_settings": group_data.get("group_settings", {}),
            "ivr_settings": group_data.get("ivr_settings", {}),
            "source_number": group_data["source_number"],
            "ivr_type": group_data["ivr_type"],
            "number_2_cc": udc_response["data"]["user_data"][
                "contact_country"
            ],
            "number_2": udc_response["data"]["user_data"]["contact"],
            "type": "1",
        }
    )

    destination = GroupDestination()
    prepared_payload = destination.prepare_payload(
        cri_obj.body, group_data, udc_response["data"]
    )
    assert prepared_payload == expected_payload


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "get_group_data",
    [get_aws_group_data, get_non_aws_group_data],
)
def test_group_destination_prepare_payload_for_type_2_without_user_data(
    get_group_data, load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_2.json")
    udc_response = load_json(
        "external_apis/udc/channel_success.json",
    )

    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    group_data = get_group_data(ivr)

    expected_payload = deepcopy(cri_request)
    expected_payload.update(
        {
            "group_settings": group_data.get("group_settings", {}),
            "ivr_settings": group_data.get("ivr_settings", {}),
            "source_number": group_data["source_number"],
            "ivr_type": group_data["ivr_type"],
        }
    )

    destination = GroupDestination()
    prepared_payload = destination.prepare_payload(
        cri_obj.body, group_data, udc_response["data"]
    )
    assert prepared_payload == expected_payload


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/user_view_only_enabled_success.json",
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
        ),
    ],
)
def test_group_destination_push_to_api(
    cri_request_fixture,
    udc_fixture,
    load_json,
    create_cri_object,
    mock_obd_internal_api,
):
    cri_request = load_json(cri_request_fixture)
    udc_response = load_json(udc_fixture)
    expected_res = load_json(
        "external_apis/obd_internal_api/success_response.json"
    )
    mock_res = mock_obd_internal_api()

    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    group_data = get_aws_group_data(ivr)
    destination = GroupDestination()
    prepared_payload = destination.prepare_payload(
        cri_obj.body, group_data, udc_response["data"]
    )

    response = destination.push_to_api(
        prepared_payload,
    )

    assert mock_res.call_count == 1

    assert expected_res == response


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "status_code,response_fixture,retry",
    [
        (
            status.HTTP_400_BAD_REQUEST,
            "external_apis/obd_internal_api/400_response.json",
            False,
        ),
        (
            status.HTTP_404_NOT_FOUND,
            "external_apis/obd_internal_api/404_response.json",
            False,
        ),
        (
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            "external_apis/obd_internal_api/500_response.json",
            True,
        ),
        (
            status.HTTP_408_REQUEST_TIMEOUT,
            "external_apis/obd_internal_api/retry_error.json",
            True,
        ),
        (
            status.HTTP_429_TOO_MANY_REQUESTS,
            "external_apis/obd_internal_api/retry_error.json",
            True,
        ),
        (
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "external_apis/obd_internal_api/retry_error.json",
            True,
        ),
        (
            status.HTTP_412_PRECONDITION_FAILED,
            "external_apis/obd_internal_api/retry_error.json",
            True,
        ),
    ],
    ids=[
        status.HTTP_400_BAD_REQUEST,
        status.HTTP_404_NOT_FOUND,
        status.HTTP_500_INTERNAL_SERVER_ERROR,
        status.HTTP_408_REQUEST_TIMEOUT,
        status.HTTP_429_TOO_MANY_REQUESTS,
        status.HTTP_422_UNPROCESSABLE_ENTITY,
        status.HTTP_412_PRECONDITION_FAILED,
    ],
)
def test_group_destination_push_to_api_failed(
    status_code,
    response_fixture,
    retry,
    load_json,
    create_cri_object,
    mock_obd_internal_api,
):
    cri_request = load_json("central_request_info/type_2.json")
    udc_response = load_json(
        "external_apis/udc/channel_success.json",
    )
    expected_res = load_json(response_fixture)
    mock_res = mock_obd_internal_api(
        api_response=expected_res, status_code=status_code
    )

    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    group_data = get_aws_group_data(ivr)
    destination = GroupDestination()
    prepared_payload = destination.prepare_payload(
        cri_obj.body, group_data, udc_response["data"]
    )

    with pytest.raises(DestinationResponseException) as exc_info:
        destination.push_to_api(
            prepared_payload,
        )
    assert exc_info.value.retry == retry

    assert mock_res.call_count == 1

    if status.is_server_error(status_code):
        assert destination.get_obd_api_avail_cache().is_failed()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_group_destination_push_to_group_queue(
    load_json, create_cri_object, patch_sqs
):
    cri_request = load_json("central_request_info/type_2.json")
    udc_response = load_json(
        "external_apis/udc/channel_success.json",
    )

    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    group_data = get_non_aws_group_data(ivr)

    destination = GroupDestination()
    prepared_payload = destination.prepare_payload(
        cri_obj.body, group_data, udc_response["data"]
    )

    response = destination.push_to_group_queue(
        group_data["url"],
        prepared_payload,
    )

    message_received = patch_sqs.receive_message(
        QueueUrl=group_data["url"],
    )
    assert response == "success"
    assert (
        json.loads(message_received["Messages"][0]["Body"]) == prepared_payload
    )


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_group_destination_push_to_group_queue_client_error(
    load_json, create_cri_object, patch_sqs
):
    cri_request = load_json("central_request_info/type_2.json")
    udc_response = load_json(
        "external_apis/udc/channel_success.json",
    )

    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    group_data = get_non_aws_group_data(ivr)

    destination = GroupDestination()
    prepared_payload = destination.prepare_payload(
        cri_obj.body, group_data, udc_response["data"]
    )

    with pytest.raises(
        DestinationResponseException, match="Push to SQS: abc failed!!"
    ) as exc_info:
        destination.push_to_group_queue(
            "abc",
            prepared_payload,
        )
    assert exc_info.value.retry

    message_received = patch_sqs.receive_message(
        QueueUrl=group_data["url"],
    )
    assert not message_received.get("Messages")


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_group_destination_increment_shared_cache():
    ivr = IvrInfoFactory()
    group_data = get_non_aws_group_data(ivr)
    destination = GroupDestination()

    destination.increment_shared_cache(group_data["url"])
    shared_cache_key = ":{prefix}:{value}".format(
        prefix=settings.REDIS_VERSION_KEY,
        value=settings.GROUP_MESSGAE_COUNT_KEY.format(q_url=group_data["url"]),
    )
    assert SharedCacheManager.get_value(shared_cache_key) == "1"


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/user_view_only_enabled_success.json",
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
        ),
    ],
)
def test_group_destination_push_with_aws_group_success(
    cri_request_fixture,
    udc_fixture,
    load_json,
    create_cri_object,
    mock_obd_internal_api,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
):
    # Mock the TrueCaller APIs
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()
    cri_request = load_json(cri_request_fixture)
    udc_response = load_json(udc_fixture)

    expected_res = load_json(
        "external_apis/obd_internal_api/success_response.json"
    )

    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    group_data = get_aws_group_data(ivr)

    mock_res = mock_obd_internal_api(api_response=expected_res)

    destination = GroupDestination()
    success_callback_called = {"value": False}

    def on_success(request_obj: CentralRequestInfo, response: Dict):
        success_callback_called["value"] = True
        assert request_obj == cri_obj
        assert response == expected_res

    def on_failure(
        request_obj: CentralRequestInfo, response: Dict, status_code: str
    ):
        assert not request_obj
        assert not response
        assert not status_code

    destination.push(
        cri_obj, group_data, udc_response["data"], on_success, on_failure
    )
    assert success_callback_called["value"]

    assert mock_res.call_count == 1
    assert token_resp.call_count == 1
    assert resp.call_count == 1


@pytest.mark.unittest
@pytest.mark.django_db
@pytest.mark.parametrize(
    "status_code,response_fixture,retry",
    [
        (
            status.HTTP_400_BAD_REQUEST,
            "external_apis/obd_internal_api/400_response.json",
            False,
        ),
        (
            status.HTTP_404_NOT_FOUND,
            "external_apis/obd_internal_api/404_response.json",
            False,
        ),
        (
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            "external_apis/obd_internal_api/500_response.json",
            True,
        ),
        (
            status.HTTP_408_REQUEST_TIMEOUT,
            "external_apis/obd_internal_api/retry_error.json",
            True,
        ),
        (
            status.HTTP_429_TOO_MANY_REQUESTS,
            "external_apis/obd_internal_api/retry_error.json",
            True,
        ),
        (
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "external_apis/obd_internal_api/retry_error.json",
            True,
        ),
        (
            status.HTTP_412_PRECONDITION_FAILED,
            "external_apis/obd_internal_api/retry_error.json",
            True,
        ),
    ],
    ids=[
        status.HTTP_400_BAD_REQUEST,
        status.HTTP_404_NOT_FOUND,
        status.HTTP_500_INTERNAL_SERVER_ERROR,
        status.HTTP_408_REQUEST_TIMEOUT,
        status.HTTP_429_TOO_MANY_REQUESTS,
        status.HTTP_422_UNPROCESSABLE_ENTITY,
        status.HTTP_412_PRECONDITION_FAILED,
    ],
)
def test_group_destination_push_with_aws_group_failure(
    status_code,
    response_fixture,
    retry,
    load_json,
    create_cri_object,
    mock_obd_internal_api,
):
    cri_request = load_json("central_request_info/type_1.json")
    udc_response = load_json(
        "external_apis/udc/user_view_only_enabled_success.json"
    )

    expected_res = load_json(response_fixture)

    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    group_data = get_aws_group_data(ivr)

    mock_res = mock_obd_internal_api(
        api_response=expected_res, status_code=status_code
    )

    destination = GroupDestination()

    failure_callback_called = {"value": False}
    retry_called = {"value": False}

    def on_success(request_obj: CentralRequestInfo, response: Dict):
        assert not request_obj
        assert not response

    def on_failure(
        request_obj: CentralRequestInfo, response: Dict, status_code: str
    ):
        failure_callback_called["value"] = True
        assert request_obj == cri_obj
        assert response == expected_res
        assert status_code == status_code

    def on_retry(
        request_obj: CentralRequestInfo,
    ):
        retry_called["value"] = True
        assert request_obj == cri_obj

    destination.push(
        cri_obj,
        group_data,
        udc_response["data"],
        on_success,
        on_failure,
        on_retry=on_retry,
    )
    assert mock_res.call_count == 1

    assert failure_callback_called["value"] != retry

    if retry:
        assert retry_called["value"]

    if status.is_server_error(status_code):
        assert destination.get_obd_api_avail_cache().is_failed()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/user_view_only_enabled_success.json",
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
        ),
    ],
)
def test_group_destination_push_with_non_aws_group_success(
    cri_request_fixture,
    udc_fixture,
    load_json,
    create_cri_object,
    patch_sqs,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
):
    # Mock the TrueCaller APIs
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()
    cri_request = load_json(cri_request_fixture)
    udc_response = load_json(udc_fixture)

    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    group_data = get_non_aws_group_data(ivr)

    destination = GroupDestination()
    success_callback_called = {"value": False}

    def on_success(request_obj: CentralRequestInfo, response: Dict):
        success_callback_called["value"] = True
        assert request_obj == cri_obj
        assert response == "success"

    def on_failure(
        request_obj: CentralRequestInfo, response: Dict, status_code: str
    ):
        assert not request_obj
        assert not response
        assert not status_code

    destination.push(
        cri_obj, group_data, udc_response["data"], on_success, on_failure
    )

    assert success_callback_called["value"]

    message_received = patch_sqs.receive_message(
        QueueUrl=group_data["url"],
    )

    assert json.loads(
        message_received["Messages"][0]["Body"]
    ) == destination.prepare_payload(
        cri_obj.body, group_data, udc_response["data"]
    )

    shared_cache_key = ":{prefix}:{value}".format(
        prefix=settings.REDIS_VERSION_KEY,
        value=settings.GROUP_MESSGAE_COUNT_KEY.format(q_url=group_data["url"]),
    )
    assert SharedCacheManager.get_value(shared_cache_key) == "1"
    assert token_resp.call_count == 1
    assert resp.call_count == 1


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_group_destination_push_with_non_aws_group_failure(
    load_json, create_cri_object, patch_sqs
):
    cri_request = load_json(
        "central_request_info/type_1.json",
    )
    udc_response = load_json(
        "external_apis/udc/user_view_only_enabled_success.json",
    )

    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    group_data = get_non_aws_group_data(ivr)
    group_url = group_data["url"]
    group_data["url"] = "abc"
    destination = GroupDestination()

    failure_callback_called = {"value": False}

    def on_success(request_obj: CentralRequestInfo, response: Dict):
        assert not request_obj
        assert not response

    def on_failure(
        request_obj: CentralRequestInfo, response: Dict, status_code: str
    ):
        failure_callback_called["value"] = True

        assert not request_obj
        assert not response
        assert not status_code

    destination.push(
        cri_obj, group_data, udc_response["data"], on_success, on_failure
    )
    assert not failure_callback_called["value"]

    message_received = patch_sqs.receive_message(
        QueueUrl=group_url,
    )

    assert not message_received.get("Messages")

    shared_cache_key = ":{prefix}:{value}".format(
        prefix=settings.REDIS_VERSION_KEY,
        value=settings.GROUP_MESSGAE_COUNT_KEY.format(q_url=group_data["url"]),
    )
    assert not SharedCacheManager.get_value(shared_cache_key)
