from handlers.central_delay_queue.main import CentralDelayQueue
from test_suite.test.base import BaseTestCase
from test_suite.test.data.central_delay_q.data import body_data, q_data
from central.settings.handlers import conf
from handlers.models import SQSQueueInfo

from django.test import tag
from django.core.cache import cache
from django.conf import settings
from unittest.mock import patch
from copy import deepcopy

import json


@tag('central_delay_q')
class CentralDelayQTestCase(BaseTestCase):
    main_data: dict = deepcopy(body_data)
    
    # queue_name and queue_url fake data 
    q_info_data: list = [
        {conf.CENTRAL_DELAY_QUEUE: 'https://ap-south-1.queue.amazonaws.com/470482/obd-central-pq1-XXX'},
        {f'{conf.CACHE_IVR_KEY_NAME}{main_data["ivr_id"]}': 'https://ap-south-1.queue.amazonaws.com/470482-pq1-XXX'}
    ]
    
    def create_sqs_records(self):
        """ create SQSQueueInfo records in database """
        gateway: dict = {
            conf.CENTRAL_DELAY_QUEUE: conf.CENTRAL_DELAY_QUEUE_GATEWAY_PREFIX,
            f'{conf.CACHE_IVR_KEY_NAME}{self.main_data["ivr_id"]}': conf.CENTRAL_IVRQ_GATEWAY_PREFIX
        }
        
        for data in self.q_info_data:
            SQSQueueInfo.objects.create(
                gateway_prefix=gateway[list(data.keys())[0]],
                queue_name=list(data.keys())[0],
                queue_url=list(data.values())[0],
            )
    
    def setUp(self):
        super().setUp()
        self.create_sqs_records()
        
        # mock `conf.sqs_client.list_queues`
        self.sqs_list_q_patch = patch('utills.sqs_manager.conf.sqs_client.list_queues')
        self.sqs_list_q_mock = self.sqs_list_q_patch.start()
        
        # mock `conf.sqs_client.receive_message`
        self.sqs_rec_msg_patch = patch('utills.sqs_manager.conf.sqs_client.receive_message', return_value=q_data)
        self.sqs_rec_msg_mock = self.sqs_rec_msg_patch.start()
        
        # mock `conf.sqs_client.send_message`
        self.sqs_send_msg_patch = patch('utills.sqs_manager.conf.sqs_client.send_message')
        self.sqs_send_msg_mock = self.sqs_send_msg_patch.start()
        
        # mock `conf.sqs_client.delete_message`
        self.sqs_del_msg_patch = patch('utills.sqs_manager.conf.sqs_client.delete_message')
        self.sqs_del_msg_mock = self.sqs_del_msg_patch.start()
        
        # set cache key count in cache(redis)
        if not cache.get(conf.CENTRAL_DELAY_COUNT_KEY, version=settings.REDIS_VERSION_KEY):
            cache.set(conf.CENTRAL_DELAY_COUNT_KEY, 10, version=settings.REDIS_VERSION_KEY)
        self.delay_count: int = int(cache.get(conf.CENTRAL_DELAY_COUNT_KEY, version=settings.REDIS_VERSION_KEY))
        
        # set ivr cache key count value
        if not cache.get(f'{conf.CACHE_IVR_KEY_NAME}{self.main_data.get("ivr_id")}', version=settings.REDIS_VERSION_KEY):
            cache.set(f'{conf.CACHE_IVR_KEY_NAME}{self.main_data.get("ivr_id")}', 10, version=settings.REDIS_VERSION_KEY)
        self.ivr_q_count: int = int(cache.get(f'{conf.CACHE_IVR_KEY_NAME}{self.main_data.get("ivr_id")}', version=settings.REDIS_VERSION_KEY))
    
    def tearDown(self):
        super().tearDown()
        self.sqs_list_q_patch.stop()
        self.sqs_rec_msg_patch.stop()
        self.sqs_send_msg_patch.stop()
        self.sqs_del_msg_patch.stop()
    
    def test_process(self):
        # calling `CentralDelayQueue.process` method
        CentralDelayQueue.process()
        
        # check `SQSManager.fetch_message_sp` called for fetching requests
        self.sqs_rec_msg_mock.assert_any_call(
            QueueUrl=self.q_info_data[0].get(conf.CENTRAL_DELAY_QUEUE),
			WaitTimeSeconds=False,
			AttributeNames=['All'],
			MaxNumberOfMessages=10
        )
        
        # check number of times `SQSManager.fetch_message_sp` called
        self.assertEqual(
            self.sqs_rec_msg_mock.call_count, self.delay_count, 
            f'Expected number of calling SQSManager.fetch_message_sp method at {self.delay_count}, but actually called at {self.sqs_rec_msg_mock.call_count} times'
        )
        
        # check `SQSManager.send_message_to_sqs` called or not
        self.sqs_send_msg_mock.assert_any_call(
            QueueUrl=self.q_info_data[1].get(f'{conf.CACHE_IVR_KEY_NAME}{self.main_data["ivr_id"]}'),
			MessageBody=json.dumps(self.main_data)
        )
        
        # check number of times `SQSManager.send_message_to_sqs` called
        self.assertEqual(
            self.sqs_send_msg_mock.call_count, self.delay_count,
            f'Expected number of calling SQSManager.send_message_to_sqs method at {self.delay_count}, but actually called at {self.sqs_send_msg_mock.call_count} times'
        )
        
        # check ivr queue cache count increase or not
        ivr_q_count: str = cache.get(f'{conf.CACHE_IVR_KEY_NAME}{self.main_data["ivr_id"]}', version=settings.REDIS_VERSION_KEY)
        self.assertEqual(
            int(ivr_q_count), self.ivr_q_count + self.delay_count,
            f'expected ivr_q_count value is {self.ivr_q_count + 1}, but actual value is {ivr_q_count}'
        )
        
        # check `SQSManager.sqs_delete_message` called or not
        self.sqs_del_msg_mock.assert_any_call(
            QueueUrl=self.q_info_data[0].get(conf.CENTRAL_DELAY_QUEUE),
            ReceiptHandle=q_data['Messages'][0]['ReceiptHandle']
        )
        
        # check number of times `SQSManager.sqs_delete_message` called
        self.assertEqual(
            self.sqs_del_msg_mock.call_count, self.delay_count,
            f'Expected number of calling SQSManager.sqs_delete_message method at {self.delay_count}, but actually called at {self.sqs_del_msg_mock.call_count} times'
        )
        
        # check delay queue cache count decrease or not
        delay_q_count: str = cache.get(conf.CENTRAL_DELAY_COUNT_KEY, version=settings.REDIS_VERSION_KEY)
        self.assertIs(delay_q_count, None,
            f'Expected delay_q_count value is None (because key deleted from cache if value is <= 0), but actual value is {delay_q_count}',
        )
