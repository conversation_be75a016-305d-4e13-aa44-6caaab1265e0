import json
from datetime import datetime

from django.db.models import Sum
from django.test import tag

from handlers.expiry_manager.main import ExpiryManager
from handlers.models import (
    CentralRequestInfo,
    OnHoldIvrTracing,
    OnHoldRequestTracing,
    OnHoldUserTracing,
)
from handlers.on_hold.circulator.main import OnHoldCirculator
from handlers.on_hold.processor.main import OnHoldProcessor
from test_suite.test.base import BaseTestCase
from test_suite.test.data.models_data import (
    IVR_INFO_DATA,
    REQUEST_TYPE_1_DATA,
    REQUEST_TYPE_2_DATA,
)


@tag("on_hold_processor", "on_hold", "central_1")
class CentralRequestInfoSignalTest(BaseTestCase):
    IVR_DATA = IVR_INFO_DATA

    def setUp(self):
        super().setUp()
        self.set_up_central_request_info_records()

    def set_up_central_request_info_records(self):
        for data in (*REQUEST_TYPE_1_DATA, *REQUEST_TYPE_2_DATA):
            CentralRequestInfo.objects.create(
                **data, expired_at=datetime.now()
            )

    def get_central_req_info_on_hold_na(self):
        return CentralRequestInfo.objects.filter(
            is_onhold_req=CentralRequestInfo.NA,
            is_enable=CentralRequestInfo.YES,
            is_req_completed=CentralRequestInfo.NO,
        )

    def get_ohu_total_count_req(self, ohu_values_list):
        count_req = 0

        for x, y in ohu_values_list:
            self.assertEqual(
                x,
                y + 1,
                msg=f"Count_req is not equals to count_disable_req + 1, count_req ={x}, count_disabled_req = {y}",
            )

            count_req += x

        return count_req

    def get_on_hold_counts(self):
        ohr_count = OnHoldRequestTracing.objects.all().count()
        ohu_values_list = OnHoldUserTracing.objects.all().values_list(
            "count_user", "count_disabled_req"
        )
        ohi_count = (
            OnHoldIvrTracing.objects.aggregate(Sum("count_ivr")).get(
                "count_ivr__sum", 0
            )
            or 0
        )

        ohu_count_req = self.get_ohu_total_count_req(ohu_values_list)

        return ohr_count, ohu_count_req, ohi_count

    def test_req_on_hold_enter(self):
        qs = self.get_central_req_info_on_hold_na()
        user_qs = qs.exclude(user_id=None)
        current_qs_len = qs.count()
        user_qs_count = user_qs.count()

        for req in qs:
            raw_data = json.loads(req.raw_data)
            req = OnHoldProcessor.mark_is_onhold_req(raw_data)

            self.assertEqual(
                req.is_onhold_req,
                CentralRequestInfo.ENTER,
                msg="is_onhold_req is not equal to ENTER",
            )

        ohr_count, ohu_count_req, ohi_count = self.get_on_hold_counts()

        self.assertEqual(
            ohr_count,
            current_qs_len,
            msg="On Hold Request Tracing Count is not equal to the added CRI requests",
        )
        self.assertEqual(
            ohu_count_req,
            user_qs_count,
            msg="On Hold User Tracing Count is not equal to the added CRI User requests",
        )
        self.assertEqual(
            ohi_count,
            current_qs_len,
            msg="On Hold Ivr Tracing Count is not equal to the added CRI requests",
        )

    def test_req_on_hold_exit(self):
        qs = self.get_central_req_info_on_hold_na()
        current_qs_count = qs.count()

        for req in qs:
            raw_data = json.loads(req.raw_data)
            req = OnHoldProcessor.mark_is_onhold_req(raw_data)
            req = OnHoldCirculator(50).update_is_on_hold_req_exit(req)
            self.assertEqual(
                req.is_onhold_req,
                CentralRequestInfo.EXIT,
                msg="is_onhold_req is not equal to EXIT.",
            )

        ohr_count, ohu_count_req, ohi_count = self.get_on_hold_counts()

        self.assertEqual(
            ohr_count,
            current_qs_count,
            msg="On Hold Request Tracing Count is not equal to current qs count",
        )
        self.assertEqual(
            ohu_count_req,
            0,
            msg="On Hold User Tracing Count is not equal to 0",
        )
        self.assertEqual(
            ohi_count, 0, msg="On Hold Ivr Tracing Count is not equal to 0"
        )

    def test_req_on_hold_ivr_timing_fail(self):
        qs = self.get_central_req_info_on_hold_na()

        for req in qs:
            raw_data = json.loads(req.raw_data)
            req = OnHoldProcessor.mark_is_onhold_req(raw_data)
            req = OnHoldCirculator(50).update_ivr_timing_fail(req)
            self.assertEqual(
                req.is_onhold_req,
                CentralRequestInfo.IVR_TIMING_FAIL,
                msg="is_onhold_req is not equal to IVR_TIMING_FAIL.",
            )

        ohr_count, ohu_count_req, ohi_count = self.get_on_hold_counts()

        self.assertEqual(
            ohr_count,
            0,
            msg="On Hold Request Tracing Count is not equal to current qs count",
        )
        self.assertEqual(
            ohu_count_req,
            0,
            msg="On Hold User Tracing Count is not equal to 0",
        )
        self.assertEqual(
            ohi_count, 0, msg="On Hold Ivr Tracing Count is not equal to 0"
        )

    def test_req_on_hold_expired(self):
        qs = self.get_central_req_info_on_hold_na()
        current_qs_count = qs.count()
        for req in qs:
            raw_data = json.loads(req.raw_data)
            req = OnHoldProcessor.mark_is_onhold_req(raw_data)

        ExpiryManager.run_expiry_manager(send_pm_event=False)

        expired_count = CentralRequestInfo.objects.filter(
            is_onhold_req__in=(
                CentralRequestInfo.EXPIRED,
                CentralRequestInfo.NA,
            ),
            completion_event_type=CentralRequestInfo.FROM_EXPIRY_MANAGER,
            event_response=CentralRequestInfo.NOT_RESPONDED,
            is_req_completed=CentralRequestInfo.YES,
            is_enable=CentralRequestInfo.NO,
        ).count()

        self.assertEqual(
            current_qs_count,
            expired_count,
            msg="Expired Request count is not equal to CentralRequestInfo count.",
        )

        ohr_count, ohu_count_req, ohi_count = self.get_on_hold_counts()

        self.assertEqual(
            ohr_count, 0, msg="On Hold Request Tracing Count is not equal to 0"
        )
        self.assertEqual(
            ohu_count_req,
            0,
            msg="On Hold User Tracing Count is not equal to 0",
        )
        self.assertEqual(
            ohi_count, 0, msg="On Hold Ivr Tracing Count is not equal to 0"
        )
