from datetime import timed<PERSON><PERSON>
from unittest.mock import patch

from django.test import tag
from django.utils import timezone

from central.settings.handlers import conf
from handlers import models
from handlers.management.commands.data_deletion_handler import (
    get_threshold,
    main,
)
from handlers.models import CentralRequestInfo
from test_suite.test.base import BaseTestCase
from test_suite.test.data.data_deletion_handler.data import get_cri_data


@tag("data_deletion_handler")
class DataDeletionHandlerTestCase(BaseTestCase):
    threshold: dict = (
        {}
    )  # threshold which are getting from `get_threshold` function
    models: list = []  # list of model names which are getting from threshold

    def setUp(self):
        super().setUp()

        # mock `utills.helpers.helper.Helper.get_threshold`
        self.get_threshold_patch = patch(
            "utills.helpers.helper.Helper.get_threshold", return_value={}
        )
        self.get_threshold_mock = self.get_threshold_patch.start()

    def tearDown(self):
        super().tearDown()
        self.get_threshold_patch.stop()

    def create_data(self):
        self.threshold: dict = get_threshold()
        self.models: list = self.threshold.get(conf.DATA_DELETE_MODELS_LIST)

        for model in self.models:
            if model == "CentralRequestInfo":
                for record in get_cri_data(self.threshold):
                    CentralRequestInfo.objects.create(**record)
            else:
                raise

    def test_get_threshold(self):
        # calling `get_threshold` function
        result: dict = get_threshold()

        self.assertDictEqual(result, conf.DATA_DELETION_THRESHOLD)

    def test_main(self):
        # create data in database for testing deletion functionality
        self.create_data()

        # calling `main` function
        main()

        # check record deleted form database or not
        n_days_back_date = timezone.now() - timedelta(
            days=self.threshold[conf.DATA_KEEP_DAYS]
        )

        for model_name in self.models:
            model_instance = getattr(models, model_name)
            self.assertFalse(
                model_instance.objects.filter(
                    added_on__lt=n_days_back_date
                ).exists()
            )
