{"status": "success", "code": 200, "message": "Pilot number list.", "pagination": {"count": 1, "per_page": 10, "total_pages": 1, "current": 1}, "data": [{"id": 139, "servers": [{"id": 52, "asterisk_address": "*************:0000", "ip_address": "*************", "port": 8080, "hostname": "test1.test.info", "server_group": "phy", "created": "2023-11-10 13:40:59", "modified": "2023-11-10 13:40:59", "data_center": 30}], "pilot_number": "**********", "quantity_e1": "100", "telco": "tata_indicom", "ban": "NA", "account_name": "Test PRIVATE LIMITED", "dop": "2022-11-30 00:00:00", "dot": null, "plan_rental": "100", "plan_code": "NA", "order_id": "TEST/2022-23/Service/TEST", "otc": null, "pri_type": "sip", "setup_cost": "0", "billing_date": "2023-02-11 00:00:00", "bgd": 31, "active_trunk": "", "passive_trunk": "", "created": "2023-02-20 15:12:23", "modified": "2023-02-20 18:38:25", "active_flag": 1, "parent_id": 0, "data_center": 0, "server": 47, "country": 99, "state": 1598, "region": "22"}]}