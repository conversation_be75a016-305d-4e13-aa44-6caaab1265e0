from unittest.mock import patch

from django.test import override_settings, tag

from test_suite.test.base import BaseTestCase
from test_suite.test.data.utills.data import (
    SQS_MANAGER_QUEUE_NAME,
    SQS_MANAGER_QUEUE_URL,
)
from utills.sqs_manager.main import SQSManager


@tag("utills")
@override_settings(CACHEOPS_ENABLED=False)
class SQSManagerTestCase(BaseTestCase):
    QUEUE_NAME = SQS_MANAGER_QUEUE_NAME
    QUEUE_URL = SQS_MANAGER_QUEUE_URL

    def setUp(self):
        super().setUp()

        self.sqs_get_queue_url_patch = patch(
            "utills.sqs_manager.conf.sqs_client.get_queue_url",
            return_value={"QueueUrl": self.QUEUE_URL},
        )
        self.sqs_get_queue_url_mock = self.sqs_get_queue_url_patch.start()

        self.sqs_create_q_patch = patch(
            "utills.sqs_manager.conf.sqs_client.create_queue",
            return_value={"QueueUrl": self.QUEUE_URL},
        )
        self.sqs_create_q_mock = self.sqs_create_q_patch.start()

        self.sqs_list_q_patch = patch(
            "utills.sqs_manager.conf.sqs_client.list_queues",
            return_value={
                "QueueUrls": [
                    self.QUEUE_URL,
                ]
            },
        )
        self.sqs_list_q_mock = self.sqs_list_q_patch.start()

        self.sqs_send_message_patch = patch(
            "utills.sqs_manager.conf.sqs_client.send_message"
        )
        self.sqs_send_message_mock = self.sqs_send_message_patch.start()

        self.sqs_send_message_batch_patch = patch(
            "utills.sqs_manager.conf.sqs_client.send_message_batch"
        )
        self.sqs_send_message_batch_mock = (
            self.sqs_send_message_batch_patch.start()
        )

        self.sqs_get_queue_attributes_patch = patch(
            "utills.sqs_manager.conf.sqs_client.get_queue_attributes",
            return_value={"Attributes": {"ApproximateNumberOfMessages": 0}},
        )
        self.sqs_get_queue_attributes_mock = (
            self.sqs_get_queue_attributes_patch.start()
        )

        self.sqs_set_queue_attributes_patch = patch(
            "utills.sqs_manager.conf.sqs_client.set_queue_attributes"
        )
        self.sqs_set_queue_attributes_mock = (
            self.sqs_set_queue_attributes_patch.start()
        )

        self.sqs_receive_message_patch = patch(
            "utills.sqs_manager.conf.sqs_client.receive_message"
        )
        self.sqs_receive_message_mock = self.sqs_receive_message_patch.start()

        self.sqs_delete_message_patch = patch(
            "utills.sqs_manager.conf.sqs_client.delete_message"
        )
        self.sqs_delete_message_mock = self.sqs_delete_message_patch.start()

        self.sqs_delete_queue_patch = patch(
            "utills.sqs_manager.conf.sqs_client.delete_queue"
        )
        self.sqs_delete_queue_mock = self.sqs_delete_queue_patch.start()

    def tearDown(self):
        super().tearDown()
        self.sqs_create_q_patch.stop()
        self.sqs_get_queue_url_patch.stop()
        self.sqs_list_q_patch.stop()
        self.sqs_send_message_patch.stop()
        self.sqs_send_message_batch_patch.stop()
        self.sqs_get_queue_attributes_patch.stop()
        self.sqs_set_queue_attributes_patch.stop()
        self.sqs_receive_message_patch.stop()
        self.sqs_delete_message_patch.stop()
        self.sqs_delete_queue_patch.stop()

    def test_create_queue(self):
        visibility_timeout = 10
        res = SQSManager.create_queue(
            self.QUEUE_NAME, visibility_timeout=visibility_timeout
        )

        self.sqs_create_q_mock.assert_called_once()

        self.assertEqual(res, self.QUEUE_URL, "Queue Url didn't matched")

    @tag("central_1", "central_2", "on_hold_processor", "on_hold_circulator")
    def test_get_queue_url(self):
        queue_url = SQSManager.get_queue_url(self.QUEUE_NAME)
        self.assertEqual(queue_url, self.QUEUE_URL, "Queue Url didn't matched")

    @tag("central_1", "central_2", "on_hold_processor", "on_hold_circulator")
    def test_get_queue_urls(self):
        queue_url = SQSManager.get_queue_urls(self.QUEUE_NAME)[0]
        self.assertEqual(queue_url, self.QUEUE_URL, "Queue Url didn't matched")

    @tag("on_hold_circulator")
    def test_sqs_pusher(self):
        message = "some message"
        res = SQSManager.sqs_pusher(message, self.QUEUE_NAME, is_name=True)

        self.sqs_list_q_mock.assert_called_once_with(
            QueueNamePrefix=self.QUEUE_NAME
        )

        self.sqs_send_message_mock.assert_called_once_with(
            QueueUrl=self.QUEUE_URL, MessageBody=message
        )

    @tag("central_1", "central_2", "on_hold_processor", "on_hold_circulator")
    def test_sqs_fifo_pusher(self):
        message = "some message"
        res = SQSManager.sqs_fifo_pusher(self.QUEUE_URL, message)
        self.sqs_send_message_mock.assert_called_once()

    @tag("central_1", "central_2", "on_hold_circulator")
    def test_send_message_to_sqs(self):
        message = "some message"
        res = SQSManager.send_message_to_sqs(self.QUEUE_URL, message)

        self.sqs_send_message_mock.assert_called_once_with(
            QueueUrl=self.QUEUE_URL, MessageBody=message
        )

    def test_send_message_in_batch(self):
        message = "some message"

        res = SQSManager.send_message_in_batch(message, self.QUEUE_NAME)

        self.sqs_list_q_mock.assert_called_once_with(
            QueueNamePrefix=self.QUEUE_NAME
        )

        self.sqs_send_message_batch_mock.assert_called_once()

    def test_send_message_batch(self):
        message = "some message"

        res = SQSManager.send_message_batch(self.QUEUE_URL, message)
        self.sqs_send_message_batch_mock.assert_called_once()

    def test_get_attributes(self):
        attributes = ["ApproximateNumberOfMessages"]
        res = SQSManager.get_attributes(self.QUEUE_NAME)

        self.sqs_list_q_mock.assert_called_once_with(
            QueueNamePrefix=self.QUEUE_NAME
        )

        self.sqs_get_queue_attributes_mock.assert_called_once_with(
            QueueUrl=self.QUEUE_URL, AttributeNames=attributes
        )

    @tag("central_1", "central_2", "on_hold_circulator")
    def test_get_queue_attributes(self):
        attributes = ["ApproximateNumberOfMessages"]
        res = SQSManager.get_queue_attributes(self.QUEUE_URL)

        self.sqs_get_queue_attributes_mock.assert_called_once_with(
            QueueUrl=self.QUEUE_URL, AttributeNames=attributes
        )

    def test_set_queue_attributes(self):
        attributes = {"VisibilityTimeout": 10}
        res = SQSManager.set_queue_attributes(self.QUEUE_URL, attributes)

        self.sqs_set_queue_attributes_mock.assert_called_once_with(
            QueueUrl=self.QUEUE_URL, Attributes=attributes
        )

    def test_fetch_message_lp(self):
        message = "some_message"
        self.sqs_receive_message_mock.return_value = {
            "Messages": [
                message,
            ]
        }
        res = SQSManager.fetch_message_lp(self.QUEUE_NAME, 1)
        self.sqs_list_q_mock.assert_called_once_with(
            QueueNamePrefix=self.QUEUE_NAME
        )
        self.sqs_receive_message_mock.assert_called_once()

        self.assertEqual(list(res), [message], "Message didn't matched")

    @tag("central_1", "central_2", "on_hold_processor")
    def test_fetch_message_sp(self):
        message = "some_message"
        self.sqs_receive_message_mock.return_value = {
            "Messages": [
                message,
            ]
        }
        res = SQSManager.fetch_message_sp(self.QUEUE_NAME, 1)
        self.sqs_list_q_mock.assert_called_once_with(
            QueueNamePrefix=self.QUEUE_NAME
        )
        self.sqs_receive_message_mock.assert_called_once()

        self.assertEqual(list(res), [message], "Message didn't matched")

    @tag("central_1", "central_2", "on_hold_processor")
    def test_receive_message(self):
        message = "some_message"
        self.sqs_receive_message_mock.return_value = {
            "Messages": [
                message,
            ]
        }
        res = SQSManager.receive_message(self.QUEUE_NAME, 1, 0)

        self.sqs_receive_message_mock.assert_called_once()

        self.assertEqual(list(res), [message], "Message didn't matched")

    @tag("central_1", "central_2", "on_hold_processor")
    def test_sqs_delete_message(self):
        receipt_handle = "some_receipt_handle"
        res = SQSManager.sqs_delete_message(self.QUEUE_URL, receipt_handle)
        self.sqs_delete_message_mock.assert_called_once_with(
            QueueUrl=self.QUEUE_URL, ReceiptHandle=receipt_handle
        )

    @tag("central_1", "central_2")
    def test_sqs_delete_queue(self):
        res = SQSManager.sqs_delete_queue(self.QUEUE_URL)
        self.sqs_delete_queue_mock.assert_called_once_with(
            QueueUrl=self.QUEUE_URL
        )
