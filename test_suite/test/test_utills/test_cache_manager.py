from django.core.cache import cache
from django.test import tag

import pytest

from test_suite.test.base import BaseTestCase
from utills.cache_manager.main import CacheManager, TrueCallerTokenCacheHandler


@tag("utills", "central_1", "central_2")
class CacheManagerTestCase(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        cache.clear()

    def tearDown(self):
        super().tearDown()
        cache.clear()

    def test_get_value(self):
        CacheManager.set_value("test_key", "test_value")
        value = CacheManager.get_value("test_key")
        self.assertEqual(value, "test_value", "Value didn't matched")
        value = CacheManager.get_value("test_key_2")
        self.assertIsNone(value, "Value should be None")

    def test_set_value(self):
        CacheManager.set_value("test_key", "test_value")
        value = CacheManager.get_value("test_key")
        self.assertEqual(value, "test_value", "Value didn't matched")

    def test_incr_value(self):
        CacheManager.incr_value("test_key")
        value = CacheManager.get_value("test_key")
        self.assertEqual(value, 1, "Value didn't matched")

    def test_decr_value(self):
        test_value = 1
        CacheManager.set_value("test_key", test_value)
        CacheManager.decr_value("test_key")
        value = CacheManager.get_value("test_key")
        self.assertEqual(value, test_value - 1, "Value didn't matched")

        CacheManager.decr_value("test_key", delete=True)
        value = CacheManager.get_value("test_key")
        self.assertIsNone(value, "Value is not None!!!")

    def test_delete_key(self):
        CacheManager.set_value("test_key", "test_value")
        CacheManager.delete_key("test_key")
        value = CacheManager.get_value("test_key")
        self.assertIsNone(value, "Value is not None!!!")

    def test_get_keys(self):
        cache_prefix = "test_cache_prefix_"
        n = 10
        for i in range(n):
            CacheManager.set_value(f"{cache_prefix}{i}", "True")

        keys = CacheManager.get_keys(cache_prefix)

        self.assertEqual(len(keys), n, "Keys length didn't matched")

    def test_get_many(self):
        cache_prefix = "test_cache_prefix_"
        n = 10
        for i in range(n):
            CacheManager.set_value(f"{cache_prefix}{i}", "True")

        values = CacheManager.get_many(cache_prefix)

        self.assertEqual(len(values), n, "Keys length didn't matched")

        self.assertTrue(all(values), "Values are not all True")

    def test_is_key_exist(self):
        cache_prefix = "test_cache_prefix_"
        n = 10
        for i in range(n):
            CacheManager.set_value(f"{cache_prefix}{i}", "True")

        self.assertTrue(CacheManager.is_key_exist(n - 1, cache_prefix))
        self.assertFalse(CacheManager.is_key_exist(n, cache_prefix))


# TrueCallerTokenCacheHandler Tests using pytest
class TestTrueCallerTokenCacheHandler:
    """Test cases for TrueCallerTokenCacheHandler"""

    @pytest.fixture
    def cache_handler(self):
        """Fixture to create a TrueCallerTokenCacheHandler instance"""
        return TrueCallerTokenCacheHandler()

    @pytest.fixture
    def clean_cache(self, cache_handler):
        """Fixture to clean cache before and after tests"""
        cache_handler.delete()
        yield
        cache_handler.delete()

    def test_set_and_get_token(self, cache_handler, clean_cache):
        """Test setting and getting a token"""
        test_token = "test_token_123"
        ttl = 3600

        # Set the token
        cache_handler.set(test_token, ttl)

        # Get the token
        retrieved_token = cache_handler.get()

        assert retrieved_token == test_token

    def test_get_nonexistent_token(self, cache_handler, clean_cache):
        """Test getting a token that doesn't exist"""
        # Ensure cache is clean
        cache_handler.delete()

        # Try to get non-existent token
        retrieved_token = cache_handler.get()

        assert retrieved_token is None

    def test_set_token_with_default_ttl(self, cache_handler, clean_cache):
        """Test setting a token with default TTL"""
        test_token = "test_token_default_ttl"

        # Set the token without specifying TTL (should use default 3600)
        cache_handler.set(test_token)

        # Get the token
        retrieved_token = cache_handler.get()

        assert retrieved_token == test_token

    def test_delete_token(self, cache_handler, clean_cache):
        """Test deleting a token"""
        test_token = "test_token_to_delete"

        # Set the token
        cache_handler.set(test_token)

        # Verify it exists
        assert cache_handler.get() == test_token

        # Delete the token
        cache_handler.delete()

        # Verify it's gone
        assert cache_handler.get() is None

    def test_cache_key_consistency(self, cache_handler):
        """Test that the cache key is consistent"""
        assert cache_handler.key == "true_caller_token"

    def test_multiple_cache_handlers_share_same_key(self, clean_cache):
        """Test that multiple instances share the same cache key"""
        handler1 = TrueCallerTokenCacheHandler()
        handler2 = TrueCallerTokenCacheHandler()

        test_token = "shared_token"

        # Set token with first handler
        handler1.set(test_token)

        # Get token with second handler
        retrieved_token = handler2.get()

        assert retrieved_token == test_token

        # Clean up
        handler1.delete()
