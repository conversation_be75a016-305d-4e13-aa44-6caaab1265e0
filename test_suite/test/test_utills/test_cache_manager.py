from django.core.cache import cache
from django.test import tag

from test_suite.test.base import BaseTestCase
from utills.cache_manager.main import CacheManager


@tag("utills", "central_1", "central_2")
class CacheManagerTestCase(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        cache.clear()

    def tearDown(self):
        super().tearDown()
        cache.clear()

    def test_get_value(self):
        CacheManager.set_value("test_key", "test_value")
        value = CacheManager.get_value("test_key")
        self.assertEqual(value, "test_value", "Value didn't matched")
        value = CacheManager.get_value("test_key_2")
        self.assertIsNone(value, "Value should be None")

    def test_set_value(self):
        CacheManager.set_value("test_key", "test_value")
        value = CacheManager.get_value("test_key")
        self.assertEqual(value, "test_value", "Value didn't matched")

    def test_incr_value(self):
        CacheManager.incr_value("test_key")
        value = CacheManager.get_value("test_key")
        self.assertEqual(value, 1, "Value didn't matched")

    def test_decr_value(self):
        test_value = 1
        CacheManager.set_value("test_key", test_value)
        CacheManager.decr_value("test_key")
        value = CacheManager.get_value("test_key")
        self.assertEqual(value, test_value - 1, "Value didn't matched")

        CacheManager.decr_value("test_key", delete=True)
        value = CacheManager.get_value("test_key")
        self.assertIsNone(value, "Value is not None!!!")

    def test_delete_key(self):
        CacheManager.set_value("test_key", "test_value")
        CacheManager.delete_key("test_key")
        value = CacheManager.get_value("test_key")
        self.assertIsNone(value, "Value is not None!!!")

    def test_get_keys(self):
        cache_prefix = "test_cache_prefix_"
        n = 10
        for i in range(n):
            CacheManager.set_value(f"{cache_prefix}{i}", "True")

        keys = CacheManager.get_keys(cache_prefix)

        self.assertEqual(len(keys), n, "Keys length didn't matched")

    def test_get_many(self):
        cache_prefix = "test_cache_prefix_"
        n = 10
        for i in range(n):
            CacheManager.set_value(f"{cache_prefix}{i}", "True")

        values = CacheManager.get_many(cache_prefix)

        self.assertEqual(len(values), n, "Keys length didn't matched")

        self.assertTrue(all(values), "Values are not all True")

    def test_is_key_exist(self):
        cache_prefix = "test_cache_prefix_"
        n = 10
        for i in range(n):
            CacheManager.set_value(f"{cache_prefix}{i}", "True")

        self.assertTrue(CacheManager.is_key_exist(n - 1, cache_prefix))
        self.assertFalse(CacheManager.is_key_exist(n, cache_prefix))
