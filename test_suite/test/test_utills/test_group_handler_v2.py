import json

import pytest
from moto import mock_aws

from handlers.models import CentralRequestInfo, Group, Number
from test_suite.factories.groups import (
    GroupFactory,
    GroupIvrInfoFactory,
    NumberFactory,
)
from test_suite.factories.ivr_info import IvrInfoFactory
from utills.group_queues_routing_manager.main_v2 import Group<PERSON>andler_V2
from utills.helpers.helper import Helper


@pytest.mark.unittest
@pytest.mark.django_db
def test_group_handler_v2_get_source_number_with_only_fix_did(
    load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_1.json")
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    group = GroupFactory()
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="**********",
        number_priority=1,
        group=group,
    )
    number_2 = NumberFactory(
        is_fix_did=Number.YES,
        number="***********",
        number_priority=2,
        group=group,
    )

    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_2, group=group)

    assert handler.get_source_number(group, Number.YES) == number_1.number


@pytest.mark.unittest
@pytest.mark.django_db
def test_group_handler_v2_get_source_number_without_only_fix_did(
    load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_1.json")
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    group = GroupFactory()
    NumberFactory(
        is_fix_did=Number.YES,
        number="**********",
        number_priority=1,
        group=group,
    )
    NumberFactory(
        is_fix_did=Number.YES,
        number="***********",
        number_priority=2,
        group=group,
    )
    number_3 = NumberFactory(
        is_fix_did=Number.NO,
        number="**********7",
        number_priority=4,
        group=group,
    )
    NumberFactory(
        is_fix_did=Number.NO,
        number="**********6",
        number_priority=6,
        group=group,
    )

    assert handler.get_source_number(group, Number.NO) == number_3.number


@pytest.mark.unittest
@pytest.mark.django_db
def test_group_handler_v2_get_source_number_did_not_found(
    load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_1.json")
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    group = GroupFactory()

    assert not handler.get_source_number(group, Number.YES)


@pytest.mark.unittest
@pytest.mark.django_db
def test_group_handler_v2_get_group_data_when_group_is_none(
    load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_1.json")
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    assert not handler.get_group_data(None, "***********")


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_group_handler_v2_get_group_data_when_group_is_not_type_aws(
    load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_1.json")
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    group = GroupFactory(
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(22)),
        kam_group_id=22,
    )
    handler.pq_key = "p_q"
    handler.ivr_info = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    assert handler.get_group_data(group, "***********") == {
        "url": group.get_assigned_queues[handler.pq_key]["url"],
        "name": group.get_assigned_queues[handler.pq_key]["name"],
        "ivr_settings": handler.ivr_info.common_setting,
        "ivr_type": handler.ivr_info.ivr_type,
        "source_number": "***********",
        "group_settings": group.settings,
        "is_aws": False,
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "group_type", [Group.AWS_GROUP_TYPE, Group.PHY_AWS_GROUP_TYPE]
)
def test_group_handler_v2_get_group_data_when_group_is_type_aws(
    group_type, load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_1.json")
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    handler.ivr_info = IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    group = GroupFactory(settings={"group_type": group_type})

    assert handler.get_group_data(group, "***********") == {
        "source_number": "***********",
        "is_aws": True,
        "ivr_type": handler.ivr_info.ivr_type,
    }


@pytest.mark.unittest
@pytest.mark.django_db
def test_group_handler_v2_assign_group_group_not_found(
    load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_1.json")
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    assert not handler.assign_group()


@pytest.mark.unittest
@pytest.mark.django_db
@pytest.mark.parametrize(
    "group_type", [Group.AWS_GROUP_TYPE, Group.PHY_AWS_GROUP_TYPE]
)
def test_group_handler_v2_assign_group_if_aws_group_mapped(
    group_type, load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_1.json")
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    group_1 = GroupFactory(settings={"group_type": group_type})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    assert handler.assign_group() == {
        "source_number": "9999999990",
        "is_aws": True,
        "ivr_type": handler.ivr_info.ivr_type,
    }


@pytest.mark.unittest
@pytest.mark.django_db
@pytest.mark.parametrize(
    "group_type", [Group.AWS_GROUP_TYPE, Group.PHY_AWS_GROUP_TYPE]
)
def test_group_handler_v2_assign_group_if_aws_group_mapped_but_api_is_failing(
    group_type, load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_1.json")
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)
    handler.get_obd_api_avail_cache().set()
    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    group_1 = GroupFactory(settings={"group_type": group_type})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    assert not handler.assign_group()


@pytest.mark.unittest
@pytest.mark.django_db
@pytest.mark.parametrize(
    "group_type", [Group.AWS_GROUP_TYPE, Group.PHY_AWS_GROUP_TYPE]
)
@mock_aws
def test_group_handler_v2_assign_group_if_aws_and_non_aws_group_mapped_but_api_is_failing(
    group_type, load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_1.json")
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)
    handler.get_obd_api_avail_cache().set()
    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    group_1 = GroupFactory(settings={"group_type": group_type})
    assigned_queue = Helper.get_group_assigned_queues(22)
    group_2 = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(assigned_queue),
        kam_group_id=22,
    )
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    number_2 = NumberFactory(
        is_fix_did=Number.YES,
        number="***********",
        group=group_2,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_2, group=group_2)

    assert handler.assign_group() == {
        "url": group_2.get_assigned_queues[handler.pq_key]["url"],
        "name": group_2.get_assigned_queues[handler.pq_key]["name"],
        "ivr_settings": handler.ivr_info.common_setting,
        "ivr_type": handler.ivr_info.ivr_type,
        "source_number": number_2.number,
        "group_settings": group_2.settings,
        "is_aws": False,
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_group_handler_v2_assign_group_if_aws_group_not_mapped(
    load_json, create_cri_object
):
    cri_request = load_json("central_request_info/type_1.json")
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    ivr_2 = IvrInfoFactory()

    group = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(22)),
        kam_group_id=22,
    )
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="**********",
        group=group,
    )

    group_2 = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(23)),
        kam_group_id=23,
    )
    number_2 = NumberFactory(
        is_fix_did=Number.NO,
        number="***********",
        group=group_2,
    )
    group_3 = GroupFactory(
        is_enable=Group.NO,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(24)),
        kam_group_id=24,
    )
    number_3 = NumberFactory(
        is_fix_did=Number.YES,
        number="**********7",
        group=group_2,
    )

    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_2, group=group_2)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_3, group=group_3)
    GroupIvrInfoFactory(ivr_info=ivr_2, number=number_1, group=group)

    assert handler.assign_group() == {
        "url": group.get_assigned_queues[handler.pq_key]["url"],
        "name": group.get_assigned_queues[handler.pq_key]["name"],
        "ivr_settings": handler.ivr_info.common_setting,
        "ivr_type": handler.ivr_info.ivr_type,
        "source_number": number_1.number,
        "group_settings": group.settings,
        "is_aws": False,
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_group_handler_v2_assign_group_if_aws_group_not_mapped_with_caller_id_filter(
    load_json, create_cri_object
):
    number = "9999999997"
    cri_request = load_json("central_request_info/type_1.json")
    cri_request["caller_id"] = number
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    ivr_2 = IvrInfoFactory()

    group = GroupFactory(
        settings={"group_type": Group.AWS_GROUP_TYPE},
        is_enable=Group.YES,
        kam_group_id=22,
    )
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="**********",
        group=group,
    )

    group_2 = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(23)),
        kam_group_id=23,
    )
    number_2 = NumberFactory(
        is_fix_did=Number.NO,
        number="***********",
        group=group_2,
    )
    group_3 = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(24)),
        kam_group_id=24,
    )
    number_3 = NumberFactory(
        is_fix_did=Number.YES,
        number=number,
        group=group_3,
    )

    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_2, group=group_2)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_3, group=group_3)
    GroupIvrInfoFactory(ivr_info=ivr_2, number=number_1, group=group)

    assert handler.assign_group() == {
        "url": group_3.get_assigned_queues[handler.pq_key]["url"],
        "name": group_3.get_assigned_queues[handler.pq_key]["name"],
        "ivr_settings": handler.ivr_info.common_setting,
        "ivr_type": handler.ivr_info.ivr_type,
        "source_number": number_3.number,
        "group_settings": group_3.settings,
        "is_aws": False,
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_group_handler_v2_assign_group_if_aws_group_not_mapped_with_group_id_filter(
    load_json, create_cri_object
):
    kam_group_id = 24
    cri_request = load_json("central_request_info/type_1.json")
    cri_request["group"] = f"C-{kam_group_id}"
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    ivr_2 = IvrInfoFactory()

    group = GroupFactory(
        settings={"group_type": Group.AWS_GROUP_TYPE},
        is_enable=Group.YES,
        kam_group_id=22,
        group_alias="A",
    )
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="**********",
        group=group,
    )

    group_2 = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(23)),
        kam_group_id=23,
        group_alias="B",
    )
    number_2 = NumberFactory(
        is_fix_did=Number.NO,
        number="***********",
        group=group_2,
    )
    group_3 = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(
            Helper.get_group_assigned_queues(kam_group_id)
        ),
        kam_group_id=kam_group_id,
        group_alias="C",
    )
    number_3 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999997",
        group=group_3,
    )

    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_2, group=group_2)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_3, group=group_3)
    GroupIvrInfoFactory(ivr_info=ivr_2, number=number_1, group=group)

    assert handler.assign_group() == {
        "url": group_3.get_assigned_queues[handler.pq_key]["url"],
        "name": group_3.get_assigned_queues[handler.pq_key]["name"],
        "ivr_settings": handler.ivr_info.common_setting,
        "ivr_type": handler.ivr_info.ivr_type,
        "source_number": number_3.number,
        "group_settings": group_3.settings,
        "is_aws": False,
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_group_handler_v2_assign_group_if_aws_group_not_mapped_with_region_filter(
    load_json, create_cri_object
):
    region = "DELHI"
    cri_request = load_json("central_request_info/type_1.json")
    cri_request["region"] = region
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    ivr_2 = IvrInfoFactory()

    group = GroupFactory(
        settings={"group_type": Group.AWS_GROUP_TYPE},
        is_enable=Group.YES,
        kam_group_id=22,
        region="BANGALORE",
    )
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="**********",
        group=group,
    )

    group_2 = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(23)),
        kam_group_id=23,
        region="MUMBAI",
    )
    number_2 = NumberFactory(
        is_fix_did=Number.YES,
        number="***********",
        group=group_2,
    )
    group_3 = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(24)),
        kam_group_id=24,
        region=region,
    )
    number_3 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999997",
        group=group_3,
    )

    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_2, group=group_2)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_3, group=group_3)
    GroupIvrInfoFactory(ivr_info=ivr_2, number=number_1, group=group)

    assert handler.assign_group() == {
        "url": group_3.get_assigned_queues[handler.pq_key]["url"],
        "name": group_3.get_assigned_queues[handler.pq_key]["name"],
        "ivr_settings": handler.ivr_info.common_setting,
        "ivr_type": handler.ivr_info.ivr_type,
        "source_number": number_3.number,
        "group_settings": group_3.settings,
        "is_aws": False,
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_group_handler_v2_assign_group_if_aws_group_not_mapped_with_group_and_region_filter(
    load_json, create_cri_object
):
    region = "DELHI"
    kam_group_id = 24
    cri_request = load_json("central_request_info/type_1.json")
    cri_request["region"] = region
    cri_request["group"] = f"C-{kam_group_id}"
    cri_obj: CentralRequestInfo = create_cri_object(cri_request)
    handler = GroupHandler_V2(cri_obj)

    ivr = IvrInfoFactory(ivr_id=cri_obj.ivr_id)
    ivr_2 = IvrInfoFactory()

    group = GroupFactory(
        settings={"group_type": Group.AWS_GROUP_TYPE},
        is_enable=Group.YES,
        kam_group_id=22,
        region=region,
        group_alias="A",
    )
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="**********",
        group=group,
    )

    group_2 = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(Helper.get_group_assigned_queues(23)),
        kam_group_id=23,
        region="MUMBAI",
        group_alias="B",
    )
    number_2 = NumberFactory(
        is_fix_did=Number.YES,
        number="***********",
        group=group_2,
    )
    group_3 = GroupFactory(
        is_enable=Group.YES,
        assigned_queues=json.dumps(
            Helper.get_group_assigned_queues(kam_group_id)
        ),
        kam_group_id=kam_group_id,
        region=region,
        group_alias="C",
    )
    number_3 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999997",
        group=group_3,
    )

    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_2, group=group_2)
    GroupIvrInfoFactory(ivr_info=ivr, number=number_3, group=group_3)
    GroupIvrInfoFactory(ivr_info=ivr_2, number=number_1, group=group)

    assert handler.assign_group() == {
        "url": group_3.get_assigned_queues[handler.pq_key]["url"],
        "name": group_3.get_assigned_queues[handler.pq_key]["name"],
        "ivr_settings": handler.ivr_info.common_setting,
        "ivr_type": handler.ivr_info.ivr_type,
        "source_number": number_3.number,
        "group_settings": group_3.settings,
        "is_aws": False,
    }
