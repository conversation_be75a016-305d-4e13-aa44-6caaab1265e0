from utills.logger_handler import SlackExceptionHandler
from django.test import tag, override_settings, TestCase

from django.conf import settings
from unittest.mock import patch
from logging import LogRecord
import os


@tag('logger_handler', 'utills', 'commands')
@override_settings(CACHEOPS_ENABLED=False)
class SlackExceptionHandlerTestCase(TestCase):
    slack_exc_obj = SlackExceptionHandler()
    
    def setUp(self) -> None:
        self.requests_post_patch = patch('requests.post')
        self.requests_post_mock = self.requests_post_patch.start()
    
    def tearDown(self) -> None:
        self.requests_post_patch.stop()

    def get_log_record(self, **kwargs) -> LogRecord:
        """ create LogRecord instance """
        
        _pathname: str = f'{os.getcwd()}/process_manager/apps/utils/middleware.py'
        _msg: str = "Invalid URL '': No schema supplied. Perhaps you meant http://?"
        
        name: str = kwargs.pop('name', 'ivr_processor')
        level: int = kwargs.pop('level', 40)
        pathname: str = kwargs.pop('pathname', _pathname)
        lineno: int = kwargs.pop('lineno', 97)
        msg: str = kwargs.pop('msg', _msg)
        args: tuple = kwargs.pop('args', ())
        exc_info = kwargs.pop('exc_info', None)
        func = kwargs.pop('func', None)
        sinfo = kwargs.pop('sinfo', None)
        
        # create LogRecord instance
        obj = LogRecord(name, level, pathname, lineno, msg, args, exc_info, func, sinfo)
        return obj
    
    def test_emit(self):
        record: LogRecord = self.get_log_record()
        
        self.slack_exc_obj.emit(record)
        
        self.requests_post_mock.assert_called_once()
