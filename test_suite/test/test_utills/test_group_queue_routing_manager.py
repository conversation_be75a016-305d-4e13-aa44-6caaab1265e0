from handlers.models import Group, IvrInfo
from test_suite.test.base import BaseTestCase
from django.test import override_settings, tag
from central.settings.handlers import conf
from test_suite.test.data.utills.data import *

from utills.group_queues_routing_manager.main import GroupQueuesRoutingManager
from unittest.mock import patch


@tag('central_2', 'utills', 'on_hold_circulator')
@override_settings(CACHEOPS_ENABLED=False)
class GroupQueueRoutingManagerTestCase(BaseTestCase):
    IVR_DATA = UTILS_IVR_INFO_DATA
    GROUPS_DATA = [GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_1_DATA,
                    GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_2_DATA,
                    GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_3_DATA,
                    GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FAILOVER_DATA,
                    GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_SHARED_BACKUP_DATA]
    maxDiff=None

    def setUp(self):
        super().setUp()
        self.sqs_get_queue_attributes_patch =  patch('utills.sqs_manager.main.SQSManager.get_queue_attributes', return_value={'ApproximateNumberOfMessages': 0})
        self.sqs_get_queue_attributes_mock = self.sqs_get_queue_attributes_patch.start()


    def tearDown(self):
        super().tearDown()
        self.sqs_get_queue_attributes_patch.stop()

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.set_up_groups()

    def test_assign_group_filter_1(self):
        req_data = GQ_ROUTING_MANAGER_FILTER_1_DUMMY_REQUESTS
        response = GroupQueuesRoutingManager().assign_group(req_data)
        self.assertEqual(response, GQ_ROUTING_MANAGER_FILTER_1_EXPECTED_RESPONSE, "Response doesn't matches")


    
    def test_assign_group_filter_2(self):
        req_data = GQ_ROUTING_MANAGER_FILTER_2_DUMMY_REQUESTS
        response = GroupQueuesRoutingManager().assign_group(req_data)
        self.assertEqual(response, GQ_ROUTING_MANAGER_FILTER_2_EXPECTED_RESPONSE, "Response doesn't matches")

    
    def test_assign_group_filter_3(self):
        req_data = GQ_ROUTING_MANAGER_FILTER_3_DUMMY_REQUESTS
        response = GroupQueuesRoutingManager().assign_group(req_data)
        self.assertEqual(response, GQ_ROUTING_MANAGER_FILTER_3_EXPECTED_RESPONSE, "Response doesn't matches")
    
    def test_assign_group_failover(self):
        req_data = GQ_ROUTING_MANAGER_FAILOVER_DUMMY_REQUESTS
        response = GroupQueuesRoutingManager().assign_group(req_data)
        self.assertEqual(
            response, GQ_ROUTING_MANAGER_FAILOVER_EXPECTED_RESPONSE, "Response doesn't matches")
    
    def test_assign_group_shared_backup(self):
        req_data = GQ_ROUTING_MANAGER_SHARED_BACKUP_DUMMY_REQUESTS
        response = GroupQueuesRoutingManager().assign_group(req_data)
        self.assertEqual(
            response, GQ_ROUTING_MANAGER_SHARED_BACKUP_EXPECTED_RESPONSE, "Response doesn't matches")
