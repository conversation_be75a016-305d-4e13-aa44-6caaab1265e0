

from django.test import tag
from test_suite.test.base import BaseTestCase
from test_suite.test.data.utills.data import *
from utills.cache_manager.main import CacheManager
from utills.shared_cache_manager.conf import SHARED_REDIS_CON
from utills.shared_cache_manager.main import SharedCacheManager


@tag('utills', 'central_2')
class SharedCacheManagerTestCase(BaseTestCase):
    
    @classmethod
    def setUpTestData(cls):
        SHARED_REDIS_CON.flushdb()

    def tearDown(self):
        super().tearDown()
        SHARED_REDIS_CON.flushdb()

    def test_get_value(self):
        SharedCacheManager.set_value(
            'test_key', 'test_value', 10)
        value = SharedCacheManager.get_value('test_key')
        self.assertEqual(value, 'test_value', "Value didn't matched")
        value= SharedCacheManager.get_value('test_key_2')
        self.assertIsNone(value, "Value should be None")

    def test_get_keys(self):
        cache_prefix = "test_cache_prefix_"
        n = 10
        for i in range(n):
            SharedCacheManager.set_value(f"{cache_prefix}{i}", "true",10)
        
        keys = SharedCacheManager.get_keys(f"{cache_prefix}*")

        self.assertEqual(len(keys), n, "Keys length didn't matched")


    def test_set_value(self):
        SharedCacheManager.set_value(
            'test_key', 'test_value',10)
        value = SharedCacheManager.get_value('test_key')
        self.assertEqual(value, 'test_value', "Value didn't matched")

    def test_delete_key(self):
        SharedCacheManager.set_value(
            'test_key', 'test_value',10)
        SharedCacheManager.delete_key('test_key')
        value = SharedCacheManager.get_value('test_key')
        self.assertIsNone(value, "Value is not None!!!")

    def test_delete_many_keys(self):
        cache_prefix = "test_cache_prefix_"
        n = 10
        for i in range(n):
            SharedCacheManager.set_value(f"{cache_prefix}{i}", "true", 10)
        
        keys = SharedCacheManager.get_keys(cache_prefix)

        SharedCacheManager.delete_many_keys(keys)
        after_delete_keys = SharedCacheManager.get_keys(cache_prefix)

        self.assertEqual(len(after_delete_keys), 0)

        

    def test_incr_value(self):
        SharedCacheManager.incr_value('test_key')
        value = SharedCacheManager.get_value('test_key')
        self.assertEqual(value, '1', "Value didn't matched")

    def test_decr_value(self):
        test_value =1
        SharedCacheManager.set_value(
            'test_key', test_value, 10)
        SharedCacheManager.decr_value('test_key')
        value = SharedCacheManager.get_value('test_key')
        self.assertEqual(int(value), test_value-1, "Value didn't matched")

        CacheManager.decr_value('test_key', delete=True)
        value = CacheManager.get_value('test_key')
        self.assertIsNone(value, "Value is not None!!!")
