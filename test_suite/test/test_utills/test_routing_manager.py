import json
from unittest.mock import Mock, patch

from django.test import tag

from central.settings.handlers import conf
from handlers.models import (
    ApiResponseFailure,
    CentralRequestInfo,
    SQSQueueInfo,
)
from test_suite.test.base import BaseTestCase
from test_suite.test.data.utills.routing_manager_data import (
    call_routing_info_called_with,
    expected_call_routing_info_response,
    expected_data_push_to_q,
    expected_response_of_dynamic_api_call_dynamic_args,
    expected_response_of_dynamic_api_call_get_method,
    expected_response_of_dynamic_api_call_post_method,
    get_cri_data,
    get_dynamic_api_call_data,
    get_dynamic_api_call_response,
    get_dynamic_api_call_response_get_method,
    get_dynamic_api_call_response_post_method,
    get_pf_route_api_response,
    get_pf_routing_info_repponse,
)
from utills.helpers.helper import ApiInstanceMocker
from utills.routing_manager.main import RoutingManger


@tag("routing_manager", "group_server_consistancy")
class RoutingManagerTestCase(BaseTestCase):
    q_url: str = "https://ap-southeast-1.queue.amazonaws.com/************/obd_v2_testing_environment"
    kam_api_name: str = conf.ROUTING_INFO_URL_NAMES[
        "kam_group_servers_route_name"
    ]
    kam_api_payload: dict = {
        "token": conf.SECRET_TOKENS["kam_group_servers_token"],
        "kam_group_id": "",
        "is_obd": 1,
    }
    instance_for_kam_api: ApiInstanceMocker = ApiInstanceMocker(
        **kam_api_payload
    )
    cri_objects: list = []  # list of instances of CentralRequestInfo model
    cri_instance = None  # instance of CentralRequestInfo model

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

        # create `CentralRequestInfo` records
        for record in get_cri_data():
            cri_instance = CentralRequestInfo.objects.create(**record)
            cls.cri_objects.append(cri_instance)

        # set `cri_instance` value
        cls.cri_instance = cls.cri_objects[0] if cls.cri_objects else None

    def setUp(self):
        super().setUp()

        # variables for mock `call_routing_info` method (if required)
        self.call_routing_info_patch_path: str = (
            "utills.routing_manager.main.RoutingManger.call_routing_info"
        )
        self.call_routing_info_patch = None
        self.call_routing_info_mock = None

        # variables for mock `dynamic_api_call` method (if required)
        self.dynamic_api_call_patch_path: str = (
            "utills.routing_manager.main.RoutingManger.dynamic_api_call"
        )
        self.dynamic_api_call_patch = None
        self.dynamic_api_call_mock = None

        # variables for mock `get_routing_info` method (if required)
        self.get_routing_info_patch_path: str = (
            "utills.routing_manager.main.RoutingManger.get_routing_info"
        )
        self.get_routing_info_patch = None
        self.get_routing_info_mock = None

        # variables for mock `get_route_process_flow` method (if required)
        self.get_route_process_flow_patch_path: str = (
            "utills.routing_manager.main.RoutingManger.get_route_process_flow"
        )
        self.get_route_process_flow_patch = None
        self.get_route_process_flow_mock = None

        # variables for mock `requests.request` method (if required)
        self.requests_request_patch_path: str = (
            "utills.routing_manager.main.requests.request"
        )
        self.requests_request_patch = None
        self.requests_request_mock = None

        # variables for mock `conf.sqs_client.list_queues` method (if required)
        self.sqs_list_queues_patch_path: str = (
            "utills.sqs_manager.conf.sqs_client.list_queues"
        )
        self.sqs_list_queues_patch = None
        self.sqs_list_queues_mock = None

        # variables for mock `conf.sqs_client.send_message` method (if required)
        self.sqs_send_msg_patch_path: str = (
            "utills.sqs_manager.conf.sqs_client.send_message"
        )
        self.sqs_send_msg_patch = None
        self.sqs_send_msg_mock = None

    def tearDown(self):
        super().tearDown()

        patch_vars: tuple = (
            self.call_routing_info_patch,
            self.dynamic_api_call_patch,
            self.requests_request_patch,
            self.sqs_send_msg_patch,
            self.get_route_process_flow_patch,
            self.get_routing_info_patch,
            self.sqs_list_queues_patch,
        )

        for patched in patch_vars:
            if patched:
                patched.stop()

    def create_sqs_q_info_data(self, data: list):
        for record in data:
            SQSQueueInfo.objects.create(**record)

    # ###################################### get_routing_info ##########################################
    def test_get_routing_info_success_if_dynamic_args(self):
        self.dynamic_api_call_patch = patch(
            self.dynamic_api_call_patch_path,
            return_value=(get_dynamic_api_call_response(), "200"),
        )
        self.dynamic_api_call_mock = self.dynamic_api_call_patch.start()

        # calling `get_routing_info` method
        result: dict = RoutingManger.get_routing_info(
            self.instance_for_kam_api, self.kam_api_name
        )

        self.assertDictEqual(
            result,
            expected_response_of_dynamic_api_call_dynamic_args(),
            f"Expected response is {expected_response_of_dynamic_api_call_dynamic_args()}, but actual response is {result}",
        )

    def test_get_routing_info_success_if_method_is_get(self):
        self.dynamic_api_call_patch = patch(
            self.dynamic_api_call_patch_path,
            return_value=(get_dynamic_api_call_response_get_method(), "200"),
        )
        self.dynamic_api_call_mock = self.dynamic_api_call_patch.start()

        # calling `get_routing_info` method
        result: dict = RoutingManger.get_routing_info(
            self.instance_for_kam_api, self.kam_api_name
        )

        self.assertDictEqual(
            result,
            expected_response_of_dynamic_api_call_get_method(),
            f"Expected response is {expected_response_of_dynamic_api_call_get_method()}, but actual response is {result}",
        )

    def test_get_routing_info_success_if_method_is_post(self):
        self.dynamic_api_call_patch = patch(
            self.dynamic_api_call_patch_path,
            return_value=(get_dynamic_api_call_response_post_method(), "200"),
        )
        self.dynamic_api_call_mock = self.dynamic_api_call_patch.start()

        # calling `get_routing_info` method
        result: dict = RoutingManger.get_routing_info(
            self.instance_for_kam_api, self.kam_api_name
        )

        self.assertDictEqual(
            result,
            expected_response_of_dynamic_api_call_post_method(),
            f"Expected response is {expected_response_of_dynamic_api_call_post_method()}, but actual response is {result}",
        )

    def test_get_routing_info_fail(self):
        self.call_routing_info_patch = patch(
            self.call_routing_info_patch_path, return_value=None
        )
        self.call_routing_info_mock = self.call_routing_info_patch.start()

        # calling `get_routing_info` method
        result: str = RoutingManger.get_routing_info(
            self.instance_for_kam_api, self.kam_api_name
        )

        self.assertIsNone(
            result,
            f"Expected return value of get_routing_info is None but actually return {result}",
        )

    # ###################################### call_routing_info #########################################
    def test_call_routing_info_success(self):
        self.dynamic_api_call_patch = patch(
            self.dynamic_api_call_patch_path,
            return_value=(get_dynamic_api_call_response_post_method(), "200"),
        )
        self.dynamic_api_call_mock = self.dynamic_api_call_patch.start()

        # call `call_routing_info` method
        result: dict = RoutingManger.call_routing_info(
            "req_id", "kam_group_servers"
        )

        self.dynamic_api_call_mock.assert_called_once_with(
            *("req_id", call_routing_info_called_with())
        )

        self.assertDictEqual(
            result,
            expected_call_routing_info_response(),
            f"Expected response is {expected_call_routing_info_response()}, but actual return value is {result}",
        )

    def test_call_routing_info_failed(self):
        self.dynamic_api_call_patch = patch(
            self.dynamic_api_call_patch_path, return_value=(None, "500")
        )
        self.dynamic_api_call_mock = self.dynamic_api_call_patch.start()

        # call `call_routing_info` method
        result: None = RoutingManger.call_routing_info(
            "req_id", "kam_group_servers"
        )

        self.assertIsNone(
            result,
            f"Expected return value of call_routing_info is None but actually return {result}",
        )

    # #################################### dynamically_unpack_values ###################################
    def test_dynamically_unpack_values_without_url(self):
        # call `dynamically_unpack_values` method
        result: dict = RoutingManger.dynamically_unpack_values(
            self.instance_for_kam_api,
            json.loads(
                get_dynamic_api_call_response_post_method()["detail"][0][
                    "data"
                ]
            ).get("body"),
            self.kam_api_name,
        )

        self.assertDictEqual(
            result,
            expected_response_of_dynamic_api_call_post_method().get("data"),
            f"Expected result is {True}, but actual result is {result}",
        )

    def test_dynamically_unpack_values_with_url(self):
        # call `dynamically_unpack_values` method
        result: str = RoutingManger.dynamically_unpack_values(
            self.instance_for_kam_api,
            json.loads(
                get_dynamic_api_call_response_post_method()["detail"][0][
                    "data"
                ]
            ).get("body"),
            self.kam_api_name,
            get_dynamic_api_call_response_post_method()["detail"][0]["url"],
        )

        self.assertEqual(
            result,
            get_dynamic_api_call_response_post_method()["detail"][0]["url"],
            f"Expected result is {True}, but actual result is {result}",
        )

    # ########################################### dynamic_api_call #####################################
    def test_dynamic_api_call_get_method_status_200(self):
        self.requests_request_patch = patch(self.requests_request_patch_path)
        self.requests_request_mock = self.requests_request_patch.start()

        # set return value of `requests.request`
        self.requests_request_mock.return_value.status_code = "200"
        self.requests_request_mock.return_value.text = json.dumps(
            get_dynamic_api_call_response_get_method()
        )

        # calling `dynamic_api_call` method
        response, status_code = RoutingManger.dynamic_api_call(
            "req_id", get_dynamic_api_call_data(self.kam_api_name)
        )

        # check getting status 200 or not
        self.assertEqual(
            status_code,
            "200",
            f"Expected status code is 200, but actual value is {status_code}",
        )

        # match response with expected response
        self.assertDictEqual(
            response,
            get_dynamic_api_call_response_get_method(),
            f'Expected response is {get_dynamic_api_call_response_get_method()["detail"][0]}, but actual response is {response}',
        )

        # check `dynamic_api_call` method called with once with expected args
        called_data = get_dynamic_api_call_data(self.kam_api_name)
        self.requests_request_mock.assert_called_once_with(
            method=called_data["method"],
            url=called_data["url"],
            headers=called_data["headers"],
            params=called_data["data"],
            timeout=conf.INTRA_API_TIMEOUT,
            verify=False,
        )

    def test_dynamic_api_call_post_method_status_200(self):
        self.requests_request_patch = patch(self.requests_request_patch_path)
        self.requests_request_mock = self.requests_request_patch.start()

        # set return value of `requests.request`
        self.requests_request_mock.return_value.status_code = "200"
        self.requests_request_mock.return_value.text = json.dumps(
            get_dynamic_api_call_response_post_method()
        )

        # calling `dynamic_api_call` method
        response, status_code = RoutingManger.dynamic_api_call(
            "req_id", get_dynamic_api_call_data(self.kam_api_name, "POST")
        )

        # check getting status 200 or not
        self.assertEqual(
            status_code,
            "200",
            f"Expected status code is 200, but actual value is {status_code}",
        )

        # match response with expected response
        self.assertDictEqual(
            response,
            get_dynamic_api_call_response_post_method(),
            f'Expected response is {get_dynamic_api_call_response_post_method()["detail"][0]}, but actual response is {response}',
        )

        # check `dynamic_api_call` method called with once with expected args
        called_data = get_dynamic_api_call_data(self.kam_api_name, "POST")
        self.requests_request_mock.assert_called_once_with(
            method=called_data["method"],
            url=called_data["url"],
            headers=called_data["headers"],
            data=called_data["data"],
            timeout=conf.INTRA_API_TIMEOUT,
            verify=False,
        )

    def test_dynamic_api_call_status_404(self):
        self.requests_request_patch = patch(self.requests_request_patch_path)
        self.requests_request_mock = self.requests_request_patch.start()

        # set return value of `requests.request`
        self.requests_request_mock.return_value.status_code = "404"
        self.requests_request_mock.return_value.text = None

        # calling `dynamic_api_call` method
        response, status_code = RoutingManger.dynamic_api_call(
            "req_id", get_dynamic_api_call_data(self.kam_api_name)
        )

        self.assertEqual(
            status_code,
            "404",
            f"Expected status code is 404, but actual value is {status_code}",
        )
        self.assertTrue(
            response,
            f"Expected response is True, but getting actual value is {response}",
        )

    def test_dynamic_api_call_status_500(self):
        self.requests_request_patch = patch(self.requests_request_patch_path)
        self.requests_request_mock = self.requests_request_patch.start()

        # set return value of `requests.request`
        self.requests_request_mock.return_value.status_code = "500"
        self.requests_request_mock.return_value.text = True

        # calling `dynamic_api_call` method
        response, status_code = RoutingManger.dynamic_api_call(
            "req_id", get_dynamic_api_call_data(self.kam_api_name)
        )

        self.assertEqual(
            status_code,
            "500",
            f"Expected status code is 500, but actual value is {status_code}",
        )
        self.assertIsNone(
            response,
            f"Expected response is True, but getting actual value is {response}",
        )

    # ##################################### get_route_process_flow #####################################
    def test_get_route_process_flow_if_status_200(self):
        self.get_routing_info_patch = patch(
            self.get_routing_info_patch_path,
            return_value=get_pf_routing_info_repponse(),
        )
        self.get_routing_info_mock = self.get_routing_info_patch.start()

        self.dynamic_api_call_patch = patch(
            self.dynamic_api_call_patch_path,
            return_value=(get_pf_route_api_response(), "200"),
        )
        self.dynamic_api_call_mock = self.dynamic_api_call_patch.start()

        # calling `get_route_process_flow` method
        response: str = RoutingManger.get_route_process_flow(
            self.instance_for_kam_api
        )

        expected_res: str = get_pf_route_api_response()["detail"][
            "destination_name"
        ]
        self.assertEqual(
            response,
            expected_res,
            f"Expected response is {expected_res}, but actual response is {response}",
        )

    def test_get_route_process_flow_if_status_404(self):
        self.get_routing_info_patch = patch(
            self.get_routing_info_patch_path,
            return_value=get_pf_routing_info_repponse(),
        )
        self.get_routing_info_mock = self.get_routing_info_patch.start()

        self.dynamic_api_call_patch = patch(
            self.dynamic_api_call_patch_path,
            return_value=({"details": {}}, "404"),
        )
        self.dynamic_api_call_mock = self.dynamic_api_call_patch.start()

        # calling `get_route_process_flow` method
        response: str = RoutingManger.get_route_process_flow(
            self.instance_for_kam_api
        )

        expected_res: str = conf.OBD_SERVICES["complete_service"]
        self.assertEqual(
            response,
            expected_res,
            f"Expected response is {expected_res}, but actual response is {response}",
        )

    def test_get_route_process_flow_if_status_any_other_than_200_and_404(self):
        self.get_routing_info_patch = patch(
            self.get_routing_info_patch_path,
            return_value=get_pf_routing_info_repponse(),
        )
        self.get_routing_info_mock = self.get_routing_info_patch.start()

        self.dynamic_api_call_patch = patch(
            self.dynamic_api_call_patch_path,
            return_value=({"details": {}}, "500"),
        )
        self.dynamic_api_call_mock = self.dynamic_api_call_patch.start()

        # calling `get_route_process_flow` method
        response: str = RoutingManger.get_route_process_flow(
            self.instance_for_kam_api
        )

        expected_res: str = conf.OBD_SERVICES["cancel_service"]
        self.assertEqual(
            response,
            expected_res,
            f"Expected response is {expected_res}, but actual response is {response}",
        )

    # ############################### dynamic_target_service_queue_push ################################
    def test_dynamic_target_service_queue_push(self):
        self.create_sqs_q_info_data(
            [
                {
                    "gateway_prefix": conf.SERVICE_ROUTER_GATEWAY_PREFIX,
                    "queue_name": conf.SERVICE_ROUTER_QUEUE,
                    "queue_url": self.q_url,
                }
            ]
        )

        service_name: str = "call"

        # patch `sqs_client.list_queues`
        self.sqs_list_queues_patch = patch(self.sqs_list_queues_patch_path)
        self.sqs_list_queues_mock = self.sqs_list_queues_patch.start()

        # patch `sqs_client.send_message`
        self.sqs_send_msg_patch = patch(self.sqs_send_msg_patch_path)
        self.sqs_send_msg_mock = self.sqs_send_msg_patch.start()

        # calling `dynamic_target_service_queue_push` method
        RoutingManger.dynamic_target_service_queue_push(
            service_name, self.cri_instance
        )

        # check `sqs_client.send_message` called once with expected args or not
        self.sqs_send_msg_mock.assert_called_once_with(
            QueueUrl=self.q_url,
            MessageBody=expected_data_push_to_q(
                self.cri_instance, service_name
            ),
        )

    # ################################# main_service_queue_router ######################################
    def test_main_service_queue_router_if_pf_not_found(self):
        service_name: str = conf.OBD_SERVICES["complete_service"]

        # patch `RoutingManger.get_route_process_flow`
        self.get_route_process_flow_patch = patch(
            self.get_route_process_flow_patch_path, return_value=service_name
        )
        self.get_route_process_flow_mock = (
            self.get_route_process_flow_patch.start()
        )

        # calling `RoutingManager.main_service_queue_router` method
        result: str = RoutingManger.main_service_queue_router(
            self.cri_instance
        )

        self.assertEqual(
            result,
            service_name,
            f"Expected return value is {service_name}, but actual return value is {result}",
        )

    def test_main_service_queue_router(self):
        # create `SQSQueueInfo` data
        self.create_sqs_q_info_data(
            [
                {
                    "gateway_prefix": conf.SERVICE_ROUTER_GATEWAY_PREFIX,
                    "queue_name": conf.SERVICE_ROUTER_QUEUE,
                    "queue_url": self.q_url,
                }
            ]
        )

        service_name: str = "call"

        # patch `RoutingManger.get_route_process_flow`
        self.get_route_process_flow_patch = patch(
            self.get_route_process_flow_patch_path, return_value=service_name
        )
        self.get_route_process_flow_mock = (
            self.get_route_process_flow_patch.start()
        )

        # patch `sqs_client.list_queues`
        self.sqs_list_queues_patch = patch(self.sqs_list_queues_patch_path)
        self.sqs_list_queues_mock = self.sqs_list_queues_patch.start()

        # patch `sqs_client.send_message`
        self.sqs_send_msg_patch = patch(self.sqs_send_msg_patch_path)
        self.sqs_send_msg_mock = self.sqs_send_msg_patch.start()

        result: bool = RoutingManger.main_service_queue_router(
            self.cri_instance
        )

        # check return value of `RoutingManger.main_service_queue_router`
        self.assertTrue(
            result,
            f"Expected return value is {True}, but actual return value is {result}",
        )

        # check `sqs_client.send_message` called once with expected args or not
        self.sqs_send_msg_mock.assert_called_once_with(
            QueueUrl=self.q_url,
            MessageBody=expected_data_push_to_q(
                self.cri_instance, service_name
            ),
        )

    # ################################## set_api_response_failure ######################################
    def test_set_api_response_failure(self):
        res_status: str = "200"
        req_id: str = self.cri_instance.request_id
        api_data: dict = get_dynamic_api_call_data(self.kam_api_name)

        response = Mock()
        response.text = json.dumps(
            expected_response_of_dynamic_api_call_post_method().get("data")
        )

        # calling `set_api_response_failure` method
        RoutingManger.set_api_response_failure(
            req_id, self.q_url, res_status, api_data, response
        )

        # check record create in DB or not
        instance = ApiResponseFailure.objects.filter(
            request_id=self.cri_instance.request_id
        )
        self.assertTrue(
            instance.exists(),
            f"Expected return value is {True}, but actual return value is {instance.exists()}",
        )

        # match DB record values
        instance = instance.first()
        self.assertEqual(
            req_id,
            instance.request_id,
            f"Expected value of request_id is {req_id}, but actual value is {instance.request_id}",
        )
        self.assertEqual(
            int(res_status),
            instance.status_code,
            f"Expected value of status_code is {res_status}, but actual value is {instance.status_code}",
        )

        expected_response: dict = {
            "req_id": req_id,
            "url": self.q_url,
            "status": res_status,
            "failure_api": api_data,
            "context": "Api failed",
        }
        expected_response.update(
            response=expected_response_of_dynamic_api_call_post_method().get(
                "data"
            )
        )
        self.assertDictEqual(
            expected_response,
            json.loads(instance.response),
            f"Expected response value is {expected_response}, but actual response value is {instance.response}",
        )

    # ####################################### event_api_trigger ########################################
    def test_event_api_trigger(self):
        self.get_routing_info_patch = patch(
            self.get_routing_info_patch_path,
            return_value=expected_response_of_dynamic_api_call_get_method(),
        )
        self.get_routing_info_mock = self.get_routing_info_patch.start()

        self.requests_request_patch = patch(self.requests_request_patch_path)
        self.requests_request_mock = self.requests_request_patch.start()

        # set return value of `requests.request`
        self.requests_request_mock.return_value.status_code = "200"
        self.requests_request_mock.return_value.text = json.dumps(
            get_dynamic_api_call_response_get_method()
        )

        # calling `event_api_trigger` method
        response, status = RoutingManger.event_api_trigger(
            self.kam_api_name, self.instance_for_kam_api
        )

        self.assertEqual(
            status,
            "200",
            f"Expected status is 200, but actual status is {status}",
        )

        self.assertDictEqual(
            response,
            get_dynamic_api_call_response_get_method(),
            f"Expected response is {get_dynamic_api_call_response_get_method()}, but actual response is {response}",
        )
