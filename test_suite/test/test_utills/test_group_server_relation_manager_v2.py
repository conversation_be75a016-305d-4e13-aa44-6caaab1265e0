from copy import deepcopy

from django.conf import settings
from rest_framework import status

import boto3
import pytest
from moto import mock_aws

from handlers.models import (
    Group,
    GroupServerRelationship,
    Server,
    SQSQueueInfo,
)
from utills.group_server_relation_manager.main_v2 import (
    GroupServerRelationManager_V2,
)
from utills.helpers.helper import Helper


@pytest.mark.unittest
@pytest.mark.django_db
def test_group_server_relation_manager_v2_get_pilot_number_details(
    load_json, mock_fix_did_pilot_list_api
):
    fix_did_response = load_json("external_apis/fix_did/fix_did_detail.json")
    pilot_number = fix_did_response["data"]["pilot_number"]
    api_response = mock_fix_did_pilot_list_api(pilot_number)

    details = GroupServerRelationManager_V2.get_pilot_number_details(
        pilot_number
    )
    assert details == api_response["data"][0]


@pytest.mark.unittest
@pytest.mark.django_db
def test_group_server_relation_manager_v2_get_pilot_number_details_failed(
    load_json, mock_fix_did_pilot_list_api
):
    fix_did_response = load_json("external_apis/fix_did/fix_did_detail.json")
    pilot_number = fix_did_response["data"]["pilot_number"]
    api_response = mock_fix_did_pilot_list_api(
        pilot_number, status_code=status.HTTP_400_BAD_REQUEST
    )

    assert not GroupServerRelationManager_V2.get_pilot_number_details(
        pilot_number
    )


@pytest.mark.unittest
@pytest.mark.django_db
@pytest.mark.parametrize(
    "group_type",
    [Group.AWS_GROUP_TYPE, Group.PHY_AWS_GROUP_TYPE, Group.PHY_GROUP_TYPE],
)
def test_group_server_relation_manager_v2_get_server_group_type_via_pilot_number(
    group_type, load_json, mock_fix_did_pilot_list_api
):
    fix_did_response = load_json("external_apis/fix_did/fix_did_detail.json")
    pilot_number = fix_did_response["data"]["pilot_number"]
    mock_fix_did_pilot_list_api(pilot_number, server_group=group_type)

    assert (
        GroupServerRelationManager_V2.get_server_group_type_via_pilot_number(
            pilot_number
        )
        == group_type
    )


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "group_type", [Group.AWS_GROUP_TYPE, Group.PHY_AWS_GROUP_TYPE]
)
def test_group_server_relation_manager_v2_add_aws_group_server(
    group_type, load_json, mock_fix_did_pilot_list_api
):
    fix_did_response = load_json("external_apis/fix_did/fix_did_detail.json")
    kam_api_response = load_json("external_apis/kamailio/group_servers.json")

    kam_group_id = list(kam_api_response["result"].keys())[0]
    group_data = kam_api_response["result"][kam_group_id]

    pilot_number = fix_did_response["data"]["pilot_number"]
    mock_fix_did_pilot_list_api(pilot_number, server_group=group_type)

    GroupServerRelationManager_V2.add_group_servers(
        kam_group_id, deepcopy(group_data), pilot_number
    )

    sqs_client = boto3.client("sqs", region_name="ap-south-1")

    group_data.pop("servers")

    assert not sqs_client.list_queues(
        QueueNamePrefix=settings.GROUPS_GATEWAY
    ).get("QueueUrls")

    assert not SQSQueueInfo.objects.filter(
        gateway_prefix=settings.GROUPS_GATEWAY
    ).exists()
    group_data["group_type"] = group_type

    assert Group.objects.filter(
        assigned_queues={},
        name=group_data.pop("name"),
        group_alias=group_data.pop("group_alias"),
        region=group_data.pop("region"),
        is_enable=Group.YES,
        is_default=Group.NO,
        kam_group_id=kam_group_id,
        settings=group_data,
    ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_group_server_relation_manager_v2_add_phy_group_server(
    load_json, mock_fix_did_pilot_list_api
):
    fix_did_response = load_json("external_apis/fix_did/fix_did_detail.json")
    kam_api_response = load_json("external_apis/kamailio/group_servers.json")

    kam_group_id = list(kam_api_response["result"].keys())[0]
    group_data = kam_api_response["result"][kam_group_id]

    pilot_number = fix_did_response["data"]["pilot_number"]
    mock_fix_did_pilot_list_api(
        pilot_number, server_group=Group.PHY_GROUP_TYPE
    )

    GroupServerRelationManager_V2.add_group_servers(
        kam_group_id, deepcopy(group_data), pilot_number
    )

    sqs_client = boto3.client("sqs", region_name="ap-south-1")

    group_servers = group_data.pop("servers")

    assert (
        len(
            sqs_client.list_queues(
                QueueNamePrefix=settings.GROUPS_GATEWAY
            ).get("QueueUrls")
        )
        == 2
    )

    assert (
        SQSQueueInfo.objects.filter(
            gateway_prefix=settings.GROUPS_GATEWAY
        ).count()
        == 2
    )
    group_data["group_type"] = Group.PHY_GROUP_TYPE

    assert Group.objects.filter(
        assigned_queues=Helper.get_group_assigned_queues(kam_group_id),
        name=group_data.pop("name"),
        group_alias=group_data.pop("group_alias"),
        region=group_data.pop("region"),
        is_enable=Group.YES,
        is_default=Group.NO,
        kam_group_id=kam_group_id,
        settings=group_data,
    ).exists()

    for server in group_servers:
        assert Server.objects.filter(
            name=server, is_enable=Server.YES
        ).exists()

        GroupServerRelationship.objects.filter(
            group__kam_group_id=kam_group_id, server__name=server
        ).exists()
