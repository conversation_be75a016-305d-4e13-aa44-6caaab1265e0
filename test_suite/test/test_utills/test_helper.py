from copy import deepcopy

from django.test import tag

import pytest
from mock import patch

from central.settings.handlers import conf
from handlers.models import CommonSetting, IvrInfo, SQSQueueInfo
from test_suite.test.base import BaseTestCase
from test_suite.test.data.utills.data import (
    EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST,
    IVR_IDS,
    IVR_INFO_DATA,
    SQS_QUEUE_INFO_DATA,
)
from utills.helpers.helper import Helper


class HandlerTestCase(BaseTestCase):
    # todo Skipping encode_data, decode_data,  invoke_process_manager  methods

    @tag("central_1")
    def test_get_threshold(self):
        entity_with_default_values = {
            conf.CENTRAL_1_ENTITY: conf.CENTRAL_1_THRESHOLD,
            conf.CENTRAL_2_ENTITY: conf.CENTRAL_2_THRESHOLD,
            conf.ON_HOLD_ENTITY: conf.ON_HOLD_THRESHOLD,
            conf.RESPONSE_MANAGER_ENTITY: conf.RESPONSE_MANAGER_THRESHOLD,
            conf.GROUPS_ENTITY: conf.GROUP_QUEUE_THRESHOLD,
            conf.DATA_DELETION_ENTITY: conf.DATA_DELETION_THRESHOLD,
        }

        for k, v in entity_with_default_values.items():
            returned_threshold = Helper.get_threshold(k, v)
            self.assertEqual(
                returned_threshold,
                v,
                f"Threshold, Didn't matched, Expected threshold: {v}, Actual threshold: {returned_threshold}",
            )

            qs = CommonSetting.objects.filter(entity=k)

            self.assertTrue(
                qs.exists(), f"CommonSetting qs doesn't exist for entity - {k}"
            )

    @tag("central_1", "central_2", "central_delay_q")
    def test_get_queue_name_url_mapping(self):
        central_pq_gateway_data = {}
        for data in SQS_QUEUE_INFO_DATA:
            queue_name = data.get("queue_name")
            queue_url = data.get("queue_url")
            gateway_prefix = data.get("gateway_prefix")

            if gateway_prefix == conf.CENTRAL_IVRQ_GATEWAY_PREFIX:
                with patch(
                    "utills.helpers.helper.SQSManager"
                ) as mock_sqs_manager:
                    # patching the get_queue_urls method to return queue_url
                    mock_sqs_manager.get_queue_urls.return_value = [
                        queue_url,
                    ]

                    ivr_queue_url = Helper.get_queue_name_url_mapping(
                        "queue_name",
                        queue_name,
                        conf.CENTRAL_IVRQ_GATEWAY_PREFIX,
                    )

                    expected_data = {queue_name: queue_url}

                    self.assertEqual(
                        ivr_queue_url,
                        expected_data,
                        f"Ivr queue url didn't matched, Expected:{queue_url}, Actual: {expected_data}",
                    )

                    qs = SQSQueueInfo.objects.filter(queue_name=queue_name)

                    qs_exists = qs.exists()
                    self.assertTrue(
                        qs_exists,
                        f"SQSQueueInfo qs doesn't exist for queue_name- {queue_name}",
                    )

                    if qs_exists:
                        obj_queue_url = qs.first().queue_url
                        self.assertEqual(
                            obj_queue_url,
                            queue_url,
                            f"SQSQueueInfo queue_url didn't matched, Expected: {queue_url}, Actual: {obj_queue_url}",
                        )

            if gateway_prefix == conf.CENTRAL_PQ_GATEWAY_PREFIX:
                SQSQueueInfo.objects.create(**data)
                central_pq_gateway_data[queue_name] = queue_url

        cq_name_url = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )
        self.assertEqual(
            central_pq_gateway_data,
            cq_name_url,
            f"Central PQ gateway data didn't matched, Expected: {central_pq_gateway_data}, Actual: {cq_name_url}",
        )

    @tag("central_1", "central_2", "central_delay_q")
    @patch("utills.helpers.helper.SQSManager")
    def test_get_queue_name_url_mapping_if_queue_url_not_found(
        self, mock_sqs_manager
    ):
        mock_sqs_manager.side_effect = Exception()

        queue_name: str = ""
        queue_url: None = None

        # get queue_name from `SQS_QUEUE_INFO_DATA`
        for data in SQS_QUEUE_INFO_DATA:  # data in dict
            if data.get("gateway_prefix") == conf.CENTRAL_IVRQ_GATEWAY_PREFIX:
                queue_name = data.get("queue_name")
                break

        """ raise exception if conf.CENTRAL_IVRQ_GATEWAY_PREFIX
            not found in SQS_QUEUE_INFO_DATA
        """
        if not queue_name:
            raise Exception(
                f"Queue name not found in `data.common_data.SQS_QUEUE_INFO_DATA.{conf.CENTRAL_IVRQ_GATEWAY_PREFIX}`"
            )

        expected_response: dict = {queue_name: queue_url}
        response: dict = Helper.get_queue_name_url_mapping(
            "queue_name", queue_name, conf.CENTRAL_IVRQ_GATEWAY_PREFIX
        )

        self.assertEqual(
            response,
            expected_response,
            f"IVR queue url didn't matched, Expected: {expected_response}, Actual: {response}",
        )

    @tag("central_1", "central_2", "on_hold_circulator")
    def test_make_pm_response(self):
        expected_data = {
            "emitter": "some emitter",
            "failure_reason": "some failure reason",
            "message": "some message",
        }
        res = Helper.make_pm_response(
            expected_data["emitter"],
            expected_data["failure_reason"],
            expected_data["message"],
        )

        self.assertEqual(
            res,
            expected_data,
            f"PM response didn't matched, Expected: {expected_data}, Actual: {res}",
        )

    def test_random_string(self):
        string_len = 20
        rand_string = Helper.random_string(stringLength=string_len)

        self.assertEqual(
            type(rand_string),
            str,
            f"random_string return value is not type string, Expected: {type(rand_string)}, Actual: {type(rand_string)}",
        )
        self.assertEqual(
            len(rand_string),
            string_len,
            f"Random string length didn't matched, Expected: {string_len}, Actual: {len(rand_string)}",
        )

    @tag("central_1")
    def test_is_ivr_rule_allowing_call(self):
        ivr_rule_allowed_data = {"validated": True}
        ivr_rule_not_allowed_data = {"validated": False}
        ivr_id = IVR_IDS[0]

        res = Helper.is_ivr_rule_allowing_call(ivr_rule_allowed_data, ivr_id)

        self.assertTrue(
            res,
            f"is_ivr_rule_allowing_call return value is not True for data  - {ivr_rule_allowed_data}, Expected: True, Actual: {res}",
        )

        res = Helper.is_ivr_rule_allowing_call(
            ivr_rule_not_allowed_data, ivr_id
        )

        res = Helper.is_ivr_rule_allowing_call(
            ivr_rule_not_allowed_data, ivr_id
        )

    def test_get_group_assigned_queues(self):
        np_q_data = {}
        pq_data = {}
        name = None

        queue_urls = []

        for data in SQS_QUEUE_INFO_DATA:
            queue_name = data.get("queue_name")
            queue_url = data.get("queue_url")
            gateway_prefix = data.get("gateway_prefix")
            if gateway_prefix == conf.GROUPS_GATEWAY:
                queue_urls.append(queue_url)

                if name is None:
                    name = queue_name.split("_")[::-1][0]

                if conf.GROUPS_NPQ_KEY in queue_name:
                    np_q_data["name"] = queue_name
                    np_q_data["url"] = queue_url
                    np_q_data["threshold"] = conf.GROUP_QUEUE_THRESHOLD[
                        conf.GROUPS_NPQ_KEY
                    ]

                elif conf.GROUPS_PQ_KEY in queue_name:
                    pq_data["name"] = queue_name
                    pq_data["url"] = queue_url
                    pq_data["threshold"] = conf.GROUP_QUEUE_THRESHOLD[
                        conf.GROUPS_PQ_KEY
                    ]

        expected_data = {
            conf.GROUPS_PQ_KEY: pq_data,
            conf.GROUPS_NPQ_KEY: np_q_data,
        }

        with patch(
            "utills.helpers.helper.SQSManager.create_queue",
            side_effect=queue_urls,
        ):
            res = Helper.get_group_assigned_queues(name)

            self.assertEqual(
                res,
                expected_data,
                f"get_group_assigned_queues return value is not matched, Expected: {expected_data}, Actual: {res}",
            )

        self.assertEqual(
            res,
            expected_data,
            f"get_group_assigned_queues return value is not matched for name- {name}, Expected: {expected_data}, Actual: {res}",
        )

    @tag("central_1")
    def test_get_max_workers(self):
        less_than_1_res = Helper.get_max_workers(0, "testing")

        more_than_max_res = Helper.get_max_workers(
            conf.MAX_ACCEPTED_HANDLER_THREADS + 5, "testing"
        )

        between_range_res = Helper.get_max_workers(
            conf.MAX_ACCEPTED_HANDLER_THREADS // 2, "testing"
        )

        self.assertEqual(
            less_than_1_res,
            1,
            f"get_max_workers return value is not matched for value- {0}, Expected: 1, Actual: {less_than_1_res}",
        )

        self.assertTrue(
            1 <= between_range_res <= conf.MAX_ACCEPTED_HANDLER_THREADS,
            f"get_max_worker return value is not between the given range with value - {conf.MAX_ACCEPTED_HANDLER_THREADS//2}, Expected: {conf.MAX_ACCEPTED_HANDLER_THREADS//2}, Actual: {between_range_res}",
        )

    @tag("central_1")
    def test_get_binary_choice(self):
        expected_values = [0, 1]
        res = Helper.get_binary_choice()
        self.assertIn(
            res,
            expected_values,
            f"get_binary_choice return value is not in expected values, Expected: {expected_values}, Actual: {res}",
        )

    @tag("central_1")
    def test_get_ivrinfo_common_setting(self):
        for data in IVR_INFO_DATA:
            obj = IvrInfo.objects.create(**data)

        existing_key = list(obj.common_setting.keys())[0]
        existing_value = obj.common_setting.get(existing_key)
        default_value = "some_random_default_data"

        returned_value = Helper.get_ivrinfo_common_setting(
            obj.ivr_id, existing_key, default_value
        )

        self.assertEqual(
            existing_value,
            returned_value,
            f"get_ivrinfo_common_setting return value is not matched, Expected: {existing_value}, Actual: {returned_value}",
        )

    def test_get_toggle_view_only_value_with_book_user_missing(self):
        for data in IVR_INFO_DATA:
            IvrInfo.objects.create(**data)
        request = deepcopy(
            EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST
        )
        assert 0 == Helper.get_toggle_view_only_value(request["ivr_id"])

    def test_get_toggle_view_only_value_with_book_user_1(self):
        for data in IVR_INFO_DATA:
            IvrInfo.objects.create(**data)
        request = deepcopy(
            EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST
        )
        ivr_info = IvrInfo.objects.get(ivr_id=request["ivr_id"])
        ivr_info.common_setting["book_user"] = 1
        ivr_info.save()
        assert 0 == Helper.get_toggle_view_only_value(request["ivr_id"])

    def test_get_toggle_view_only_value_with_book_user_invalid_value(self):
        for data in IVR_INFO_DATA:
            IvrInfo.objects.create(**data)
        request = deepcopy(
            EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST
        )
        ivr_info = IvrInfo.objects.get(ivr_id=request["ivr_id"])
        ivr_info.common_setting["book_user"] = "random"
        ivr_info.save()

        assert 0 == Helper.get_toggle_view_only_value(request["ivr_id"])

    def test_get_toggle_view_only_value_with_book_user_0(self):
        for data in IVR_INFO_DATA:
            IvrInfo.objects.create(**data)
        request = deepcopy(
            EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST
        )
        ivr_info = IvrInfo.objects.get(ivr_id=request["ivr_id"])
        ivr_info.common_setting["book_user"] = 0
        ivr_info.save()
        assert 1 == Helper.get_toggle_view_only_value(request["ivr_id"])

    def test_get_toggle_view_only_value_with_coc_ivr(self):
        for data in IVR_INFO_DATA:
            IvrInfo.objects.create(**data)
        ivr_info = IvrInfo.objects.filter(ivr_type=IvrInfo.COC).first()
        request = deepcopy(
            EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST
        )
        request["ivr_id"] = ivr_info.ivr_id
        ivr_info.save()

        assert 1 == Helper.get_toggle_view_only_value(request["ivr_id"])

    def test_get_toggle_view_only_value_with_coc_ivr_book_user_1(self):
        for data in IVR_INFO_DATA:
            IvrInfo.objects.create(**data)
        ivr_info = IvrInfo.objects.filter(ivr_type=IvrInfo.COC).first()
        request = deepcopy(
            EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST
        )
        request["ivr_id"] = ivr_info.ivr_id
        request["book_user"] = "1"
        assert 1 == Helper.get_toggle_view_only_value(request["ivr_id"])


@pytest.mark.unittest
@pytest.mark.django_db
def test_get_user_lock_duration():
    data = deepcopy(IVR_INFO_DATA[0])
    data["common_setting"][
        conf.CACHE_UDC_LOCKED_USERS_AGENT_FIRST_TTL_SETTING_NAME
    ] = conf.CACHE_UDC_LOCKED_USERS_AGENT_FIRST_TTL
    data["common_setting"][
        conf.CACHE_UDC_LOCKED_USERS_DUR_SETTING_NAME
    ] = conf.CACHE_UDC_LOCKED_USERS_TTL

    obj = IvrInfo.objects.create(**data)
    assert (
        Helper.get_user_lock_duration(1, obj.ivr_id)
        == conf.CACHE_UDC_LOCKED_USERS_AGENT_FIRST_TTL
    )
    assert (
        Helper.get_user_lock_duration(0, obj.ivr_id)
        == conf.CACHE_UDC_LOCKED_USERS_TTL
    )

    data["common_setting"][
        conf.CACHE_UDC_LOCKED_USERS_AGENT_FIRST_TTL_SETTING_NAME
    ] = 0
    data["common_setting"][conf.CACHE_UDC_LOCKED_USERS_DUR_SETTING_NAME] = 0

    obj.common_setting = data["common_setting"]
    obj.save()

    assert Helper.get_user_lock_duration(1, obj.ivr_id) == 1
    assert Helper.get_user_lock_duration(0, obj.ivr_id) == 1
    data["common_setting"][
        conf.CACHE_UDC_LOCKED_USERS_AGENT_FIRST_TTL_SETTING_NAME
    ] = -1
    data["common_setting"][conf.CACHE_UDC_LOCKED_USERS_DUR_SETTING_NAME] = -1

    obj.common_setting = data["common_setting"]
    obj.save()

    assert Helper.get_user_lock_duration(1, obj.ivr_id) == 1
    assert Helper.get_user_lock_duration(0, obj.ivr_id) == 1
