import copy
import json
from copy import deepcopy
from unittest.mock import patch

from django.test import tag
from rest_framework import status

import pytest

from central.settings.handlers import conf
from test_suite.test.base import BaseTestCase
from test_suite.test.data.utills.data import (
    EXTERNAL_API_UDC_CHANNEL_SUCCESS_RESPONSE,
    EXTERNAL_API_UDC_DC_API_SUCCESS_RESPONSE,
    EXTERNAL_API_UDC_METHOD_CHANNEL_API_RESPONSE,
    EXTERNAL_API_UDC_METHOD_DC_API_RESPONSE,
    EXTERNAL_API_UDC_METHOD_TYPE_1_ANON_UUID_RESPONSE,
    EXTERNAL_API_UDC_METHOD_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_RESPONSE,
    EXTERNAL_API_UDC_TYPE_1_ANON_UUID_REQUEST,
    EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST,
    EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_USER_LOCKED_RESPONSE,
    EXTERNAL_API_UDC_TYPE_2_DC_API_REQUEST,
    EXTERNAL_API_UDC_USER_VIEW_ONLY_ENABLED_SUCCESS_RESPONSE,
    IVR_IDS,
    IVR_INFO_DATA,
)
from utills.cache_manager.main import CacheManager
from utills.destination.request_transformer import RequestDataTransformer
from utills.exceptions import DestinationResponseException
from utills.external_apis import ExternalAPIs


@tag("utills")
class ExternalAPITestCase(BaseTestCase):
    SUCCESS_STATUS_CODE = "200"
    FAIL_STATUS_CODE = "404"

    IVR_DATA = IVR_INFO_DATA

    def setUp(self):
        super().setUp()
        self.external_apis_requests_get_patcher = patch(
            "utills.external_apis.requests.get"
        )

        self.mock_requests_get = (
            self.external_apis_requests_get_patcher.start()
        )

    def tearDown(self):
        super().setUp()
        self.external_apis_requests_get_patcher.stop()

        cache_keys = CacheManager.get_keys(
            conf.CACHE_UDC_LOCKED_USERS_KEY_NAME
        )
        for key in cache_keys:
            CacheManager.delete_key(key)

    @tag("central_1")
    def test_talk_to_api(self):
        expected_success_response = {"success": True}
        self.mock_requests_get.return_value.status_code = (
            self.SUCCESS_STATUS_CODE
        )
        self.mock_requests_get.return_value.json.return_value = (
            expected_success_response
        )

        request_id = "random_data"
        url = "{url}?request_id={request_id}".format(
            url=conf.CANCELLATION_API_URL, request_id=request_id
        )
        headers = {"Authorization": "Token " + conf.PROCESS_MANAGER_AUTH_TOKEN}

        response, status_code = ExternalAPIs.get_api(
            url, request_id=request_id, headers=headers
        )

        self.verify_response(response, url, expected_success_response)
        self.verify_status_code(status_code, url, self.SUCCESS_STATUS_CODE)

    @tag("central_1", "on_hold_circulator")
    @patch(
        "utills.cache_manager.main.CacheManager.get_value", return_value=None
    )
    @patch("utills.cache_manager.main.CacheManager.set_value")
    def test_talk_to_ivr_rule_api(self, get_value_mock, set_value_mock):
        ivr_id = IVR_IDS[0]
        self.mock_requests_get.return_value.json.return_value = {
            "data": {"validated": False}
        }
        self.mock_requests_get.return_value.status_code = self.FAIL_STATUS_CODE
        call_not_allowed = ExternalAPIs.talk_to_ivr_rule_api(ivr_id)

        self.assertFalse(
            call_not_allowed,
            f"Wrong response received from method - talk_to_ivr_rule_api in case of ivr_rule not allowed, res - {call_not_allowed}",
        )

        self.mock_requests_get.return_value.json.return_value = {
            "data": {"validated": True}
        }
        self.mock_requests_get.return_value.status_code = (
            self.SUCCESS_STATUS_CODE
        )

        call_allowed = ExternalAPIs.talk_to_ivr_rule_api(ivr_id)

        self.assertTrue(
            call_allowed,
            f"Wrong response received from method - talk_to_ivr_rule_api in case of ivr_rule allowed, res - {call_allowed}",
        )

    @tag("central_1")
    def test_talk_to_cancellation_api(self):
        random_request_id = "aasdlkjsaldjda"
        self.mock_requests_get.return_value.json.return_value = {
            "detail": [
                {"request_id": random_request_id, "request_status": 2},
            ]
        }
        self.mock_requests_get.return_value.status_code = (
            self.SUCCESS_STATUS_CODE
        )
        ivr_id = IVR_IDS[0]

        res = ExternalAPIs.talk_to_cancellation_api(ivr_id, random_request_id)

        self.assertIsNotNone(
            res,
            f"Wrong response received from method - talk_to_cancellation_api, res - {res}",
        )

    @tag("central_2", "on_hold_circulator")
    def test_manage_udc_api_check_type_1_anon_uuid(self):
        cri_obj, json_data = self.create_cri_object_from_dict(
            EXTERNAL_API_UDC_TYPE_1_ANON_UUID_REQUEST
        )
        self.mock_requests_get.return_value.json.return_value = (
            EXTERNAL_API_UDC_CHANNEL_SUCCESS_RESPONSE
        )
        self.mock_requests_get.return_value.status_code = (
            self.SUCCESS_STATUS_CODE
        )

        res = ExternalAPIs.manage_udc_api_check(cri_obj, user_view_only=0)
        expected_data = {
            "user_data": deepcopy(
                EXTERNAL_API_UDC_METHOD_TYPE_1_ANON_UUID_RESPONSE
            )
        }
        expected_data.update(
            **EXTERNAL_API_UDC_CHANNEL_SUCCESS_RESPONSE["data"]
        )
        self.assertEqual(
            res,
            expected_data,
            "Response Didn't Matched",
        )

    @tag("central_2", "on_hold_circulator")
    def test_manage_udc_api_check_type_1_uc_view_only_enabled(self):
        cri_obj, json_data = self.create_cri_object_from_dict(
            EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST
        )
        self.mock_requests_get.return_value.json.return_value = (
            EXTERNAL_API_UDC_USER_VIEW_ONLY_ENABLED_SUCCESS_RESPONSE
        )
        self.mock_requests_get.return_value.status_code = (
            self.SUCCESS_STATUS_CODE
        )
        raw_req_data = json.loads(cri_obj.raw_data)
        user_id = raw_req_data.get("user_id", "")

        # user_lock =False

        with patch(
            "utills.helpers.helper.Helper.get_ivrinfo_common_setting",
            return_value="0",
        ):
            res = ExternalAPIs.manage_udc_api_check(
                cri_obj, lock_udc=False, user_view_only=1
            )
            self.assertEqual(
                res,
                EXTERNAL_API_UDC_METHOD_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_RESPONSE,
                "Response Didn't Matched",
            )
            is_locked_user = CacheManager.is_key_exist(
                user_id, conf.CACHE_UDC_LOCKED_USERS_KEY_NAME
            )
            self.assertFalse(is_locked_user, "is_locked_user is not False!!")

        # user_lock=True
        res = ExternalAPIs.manage_udc_api_check(
            cri_obj, lock_udc=True, user_view_only=1
        )

        self.assertEqual(
            res,
            EXTERNAL_API_UDC_METHOD_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_RESPONSE,
            "Response Didn't Matched in case of lock udc=True",
        )
        is_locked_user = CacheManager.is_key_exist(
            user_id, conf.CACHE_UDC_LOCKED_USERS_KEY_NAME
        )
        self.assertTrue(is_locked_user, "is_locked_user is not True!!")

        # user is already locked
        res = ExternalAPIs.manage_udc_api_check(
            cri_obj, lock_udc=True, user_view_only=1
        )

        self.assertEqual(
            res,
            EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_USER_LOCKED_RESPONSE,
            "Response Didn't Matched in case of already locked user!!!",
        )

    @tag("central_2", "on_hold_circulator")
    def test_manage_udc_api_check_type_2(self):
        cri_obj, json_data = self.create_cri_object_from_dict(
            EXTERNAL_API_UDC_TYPE_2_DC_API_REQUEST
        )
        self.mock_requests_get.return_value.status_code = (
            self.SUCCESS_STATUS_CODE
        )

        # is_department_check_allowed= 0
        with patch(
            "utills.helpers.helper.Helper.get_ivrinfo_common_setting",
            return_value="0",
        ):
            self.mock_requests_get.return_value.json.return_value = (
                EXTERNAL_API_UDC_CHANNEL_SUCCESS_RESPONSE
            )
            # user_lock=True
            res = ExternalAPIs.manage_udc_api_check(cri_obj, user_view_only=0)
            self.assertEqual(
                res,
                EXTERNAL_API_UDC_METHOD_CHANNEL_API_RESPONSE,
                "Response Didn't Matched in case of is_department_check_allowed as 0 ",
            )

        # is_department_check_allowed =1
        self.mock_requests_get.return_value.json.return_value = (
            EXTERNAL_API_UDC_DC_API_SUCCESS_RESPONSE
        )
        res = ExternalAPIs.manage_udc_api_check(cri_obj, user_view_only=0)
        self.assertEqual(
            res,
            EXTERNAL_API_UDC_METHOD_DC_API_RESPONSE,
            "Response Didn't Matched in case of is_department_check_allowed as 1 ",
        )

        # call_direction is agent, but user_data is {}
        call_direction_response = copy.deepcopy(
            EXTERNAL_API_UDC_DC_API_SUCCESS_RESPONSE
        )
        call_direction_response["data"].pop("user_data")
        self.mock_requests_get.return_value.json.return_value = (
            call_direction_response
        )

        res = ExternalAPIs.manage_udc_api_check(cri_obj, user_view_only=0)

        self.assertEqual(
            res,
            {},
            "Response Didn't match in case of call_direction agent and user_data - {}",
        )

        # call_direction is customer and  is_department_check_allowed =1
        with patch(
            "utills.helpers.helper.Helper.get_ivrinfo_common_setting",
            side_effect=["1", "customer", 30],
        ):
            res = ExternalAPIs.manage_udc_api_check(cri_obj, user_view_only=0)

            call_direction_method_response = copy.deepcopy(
                EXTERNAL_API_UDC_METHOD_DC_API_RESPONSE
            )
            call_direction_method_response.pop("user_data")
            self.assertEqual(
                res,
                call_direction_method_response,
                "Response Didn't match in case of call_direction customer and user_data - {}",
            )


@pytest.mark.unittest
def test_external_api_talk_to_obd_internal_api(
    load_json, mock_obd_internal_api
):
    request = load_json("central_request_info/type_1.json")
    expected_res = load_json(
        "external_apis/obd_internal_api/success_response.json"
    )
    res = mock_obd_internal_api()

    transformed_request = RequestDataTransformer().transform(request)
    response = ExternalAPIs.talk_to_obd_internal_api(transformed_request)

    assert response == expected_res
    assert res.call_count == 1


@pytest.mark.unittest
@pytest.mark.parametrize(
    "status_code,response_fixture,retry",
    [
        (
            status.HTTP_400_BAD_REQUEST,
            "external_apis/obd_internal_api/400_response.json",
            False,
        ),
        (
            status.HTTP_404_NOT_FOUND,
            "external_apis/obd_internal_api/404_response.json",
            False,
        ),
        (
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            "external_apis/obd_internal_api/500_response.json",
            True,
        ),
        (
            status.HTTP_408_REQUEST_TIMEOUT,
            "external_apis/obd_internal_api/retry_error.json",
            True,
        ),
        (
            status.HTTP_429_TOO_MANY_REQUESTS,
            "external_apis/obd_internal_api/retry_error.json",
            True,
        ),
        (
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "external_apis/obd_internal_api/retry_error.json",
            True,
        ),
        (
            status.HTTP_412_PRECONDITION_FAILED,
            "external_apis/obd_internal_api/retry_error.json",
            True,
        ),
    ],
    ids=[
        status.HTTP_400_BAD_REQUEST,
        status.HTTP_404_NOT_FOUND,
        status.HTTP_500_INTERNAL_SERVER_ERROR,
        status.HTTP_408_REQUEST_TIMEOUT,
        status.HTTP_429_TOO_MANY_REQUESTS,
        status.HTTP_422_UNPROCESSABLE_ENTITY,
        status.HTTP_412_PRECONDITION_FAILED,
    ],
)
def test_external_api_talk_to_obd_internal_api_failed(
    status_code, response_fixture, retry, load_json, mock_obd_internal_api
):
    request = load_json("central_request_info/type_1.json")
    api_response = load_json(response_fixture)

    res = mock_obd_internal_api(
        api_response=api_response,
        status_code=status_code,
    )

    transformed_request = RequestDataTransformer().transform(request)
    with pytest.raises(DestinationResponseException) as exc_info:
        ExternalAPIs.talk_to_obd_internal_api(transformed_request)

    assert res.call_count == 1
    assert exc_info.value.response == api_response
    assert exc_info.value.status_code == str(status_code)
    assert exc_info.value.retry == retry
    assert res.call_count == 1
