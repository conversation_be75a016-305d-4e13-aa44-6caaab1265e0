from unittest.mock import patch

from django.test import override_settings, tag

from central.settings.handlers import conf
from handlers.models import SQSQueueInfo
from test_suite.test.base import BaseTestCase
from test_suite.test.data.utills.data import (
    EXTERNAL_API_UDC_METHOD_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_RESPONSE,
    EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST,
    GQ_PUSHER_DUMMY_GROUP_DATA,
    SQS_QUEUE_INFO_DATA,
    UTILS_IVR_INFO_DATA,
)
from utills.cache_manager.main import CacheManager
from utills.group_queues_pusher.main import GroupQueuesPusher
from utills.helpers.helper import Helper
from utills.shared_cache_manager.main import SharedCacheManager
from utills.udc_helper import UDCHelper


@tag("utills")
@override_settings(CACHEOPS_ENABLED=False)
class GroupQueuePusherTestCase(BaseTestCase):
    IVR_DATA = UTILS_IVR_INFO_DATA
    maxDiff = None
    DEFAULT_PQ2_COUNT = 20
    DEFAULT_CACHE_EXPIRY = 10

    @classmethod
    def setUpTestData(cls):
        for data in SQS_QUEUE_INFO_DATA:
            SQSQueueInfo.objects.create(**data)

        super().setUpTestData()

    def setUp(self):
        super().setUp()
        self.sqs_send_message_patch = patch(
            "utills.sqs_manager.main.SQSManager.send_message_to_sqs"
        )
        self.sqs_send_message_mock = self.sqs_send_message_patch.start()

        self.sqs_delete_message_patch = patch(
            "utills.sqs_manager.main.SQSManager.sqs_delete_message"
        )
        self.sqs_delete_message_mock = self.sqs_delete_message_patch.start()

        self.sqs_fifo_pusher_patch = patch(
            "utills.sqs_manager.main.SQSManager.sqs_fifo_pusher"
        )
        self.sqs_fifo_pusher_mock = self.sqs_fifo_pusher_patch.start()

        CacheManager.set_value(
            conf.CENTRAL_PQ2_COUNT_KEY,
            self.DEFAULT_PQ2_COUNT,
            expiry=self.DEFAULT_CACHE_EXPIRY,
        )

    def tearDown(self):
        super().tearDown()
        self.sqs_send_message_patch.stop()
        self.sqs_delete_message_patch.stop()
        self.sqs_fifo_pusher_patch.stop()

        CacheManager.delete_key(conf.CENTRAL_PQ2_COUNT_KEY)
        shared_cache_keys = SharedCacheManager.get_keys(conf.REDIS_VERSION_KEY)
        for key in shared_cache_keys:
            SharedCacheManager.delete_key(key)

    @tag("central_2")
    def test_push_to_group_queue_source_central_2(self):
        group = GQ_PUSHER_DUMMY_GROUP_DATA
        source = "central_2"
        body = EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST
        instance, _ = self.create_cri_object_from_dict(body)
        udc_helper = UDCHelper(instance)
        udc_helper.udc_response = (
            EXTERNAL_API_UDC_METHOD_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_RESPONSE
        )
        q_key = conf.CENTRAL_PQ2_COUNT_KEY
        cq_name_urls = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )
        pq_url = cq_name_urls[conf.CENTRAL_PQ2]
        ReceiptHandle = "some_receipt_handler"

        shared_cache_key = ":{prefix}:{value}".format(
            prefix=conf.REDIS_VERSION_KEY,
            value=conf.GROUP_MESSGAE_COUNT_KEY.format(q_url=group["url"]),
        )

        initial_shared_cache_val = (
            SharedCacheManager.get_value(shared_cache_key) or 0
        )

        GroupQueuesPusher().push_to_group_queue(
            source, body, group, udc_helper, q_key, pq_url, ReceiptHandle
        )

        self.sqs_send_message_mock.assert_called_once()
        self.sqs_delete_message_mock.assert_called_once_with(
            pq_url, ReceiptHandle
        )

        shared_cache_val = SharedCacheManager.get_value(shared_cache_key)

        self.assertEqual(
            initial_shared_cache_val,
            int(shared_cache_val) - 1,
            "Shared Cache key values wasn't increased by 1.",
        )

        cache_val = CacheManager.get_value(q_key)

        self.assertEqual(
            cache_val,
            self.DEFAULT_PQ2_COUNT - 1,
            "q_key count wasn't decreased in cache!!!",
        )

        self.sqs_fifo_pusher_mock.assert_called_once()

    @tag("on_hold_circulator")
    def test_push_to_group_queue_source_on_hold_circulator(self):
        group = GQ_PUSHER_DUMMY_GROUP_DATA
        source = "central_2"
        body = EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST
        instance, _ = self.create_cri_object_from_dict(body)
        udc_helper = UDCHelper(instance)
        udc_helper.udc_response = (
            EXTERNAL_API_UDC_METHOD_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_RESPONSE
        )
        q_key = conf.CENTRAL_PQ2_COUNT_KEY
        cq_name_urls = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )
        pq_url = cq_name_urls[conf.CENTRAL_PQ2]
        ReceiptHandle = "some_receipt_handler"

        shared_cache_key = ":{prefix}:{value}".format(
            prefix=conf.REDIS_VERSION_KEY,
            value=conf.GROUP_MESSGAE_COUNT_KEY.format(q_url=group["url"]),
        )

        initial_shared_cache_val = (
            SharedCacheManager.get_value(shared_cache_key) or 0
        )

        GroupQueuesPusher().push_to_group_queue(
            source, body, group, udc_helper, q_key, pq_url, ReceiptHandle
        )

        self.sqs_send_message_mock.assert_called_once()

        shared_cache_val = SharedCacheManager.get_value(shared_cache_key)

        self.assertEqual(
            int(initial_shared_cache_val),
            int(shared_cache_val) - 1,
            "Shared Cache key values wasn't increased by 1.",
        )
        self.sqs_fifo_pusher_mock.assert_called_once()
