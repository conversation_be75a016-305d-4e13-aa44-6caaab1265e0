from copy import deepcopy
from random import choice
from unittest.mock import patch

from django.db.models import QuerySet
from django.test import override_settings, tag

from handlers.models import Group, GroupServerRelationship, Number, Server
from test_suite.test.base import BaseTestCase
from utills.group_server_relation_manager.main import (
    GroupServerRelationManager,
)


@tag("group_server_relation_manager", "group_server_consistancy")
@override_settings(CACHEOPS_ENABLED=False)
class GroupServerRelationManagerTestCase(BaseTestCase):
    group_instance: Group = (
        None  # set group_instance value from `setUpClass` method
    )
    server_instance: Server = (
        None  # set server_instance value from `setUpClass` method
    )

    kam_group_id: str = "1"
    group_name: str = "TCS-TA-MP"
    group_alias: str = "TCS-TA-MP"
    region: str = "Delhi"
    c_id: str = "TCS-TA-MP"
    d_id: str = "MYOP"
    operator: str = "TA_TCS"
    caller_id: str = "11615680"
    last_digit: str = "9"
    servers: list = [
        "d1.voicetree.info",
        "d2.voicetree.info",
        "d3.voicetree.info",
        "d4.voicetree.info",
        "d5.voicetree.info",
    ]

    settings_dict: dict = {
        "c_id": "NOIDA-VPS-AI-CO",
        "d_id": "SIP",
        "operator": "VPS_Airtel",
        "caller_id": "12043850",
    }
    assigned_queues: dict = {
        "p_q": {
            "name": "gateway_p_q_name",
            "url": "https://ap-south-1.queue.amazonaws.com/472952060482/gateway_p_q-name",
            "threshold": 10,
        },
        "np_q": {
            "name": "gateway_np_q-name",
            "url": "https://ap-south-1.queue.amazonaws.com/472952060482/gateway_np_q-name",
            "threshold": 30,
        },
    }

    group_data: dict = {
        "name": group_name,
        "group_alias": group_alias,
        "region": region,
        "c_id": c_id,
        "d_id": d_id,
        "operator": operator,
        "caller_id": caller_id,
        "servers": servers,
        "last_digit": last_digit,
    }

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

        # create Group record
        cls.group_instance: Group = Group.objects.create(
            name="TCS-TA-UP",
            group_alias="TCS-TA-UP",
            region="Gujrat",
            assigned_queues=cls.assigned_queues,
            is_enable=Group.YES,
            is_default=Group.NO,
            kam_group_id="108",
            settings=cls.settings_dict,
        )

        # create server record
        cls.server_instance: Server = Server.objects.create(
            name="g1.voicetree.info"
        )

    def setUp(self):
        super().setUp()

        # mock `Helper.get_group_assigned_queues`
        self.get_grp_assigned_q_patch_path: str = (
            "utills.helpers.helper.Helper.get_group_assigned_queues"
        )
        self.get_grp_assigned_q_patch = None
        self.get_grp_assigned_q_mock = None

    def tearDown(self):
        super().tearDown()

        patch_vars: tuple = (self.get_grp_assigned_q_patch,)

        for patch_var in patch_vars:
            if patch_var is not None:
                patch_var.stop()

    def create_groups(self, num_of_rec: int = 10) -> list:
        """method for create Group model records and return list of kam_group_ids of created groups
        Args:
            num_of_rec (int, optional): Number of Group records to be created in database. Defaults to 10.
        Returns:
            list: list of `kam_group_id` of groups
        """

        ids: list = []
        for _ in range(num_of_rec):
            instance: Group = Group.objects.create(
                name=self.group_name,
                group_alias=self.group_alias,
                region=self.region,
                assigned_queues=self.assigned_queues,
                is_enable=Group.YES,
                is_default=Group.NO,
                kam_group_id=f"100{_}",
                settings=self.settings_dict,
            )
            ids.append(str(instance.kam_group_id))
        return ids

    def create_servers(self, num_of_rec: int = 10) -> list:
        """method for create Server model records and return list of server_names of created servers
        Args:
            num_of_rec (int, optional): Number of Server records to be created in database. Defaults to 10.
        Returns:
            list: list of `names` of servers
        """

        names: list = []
        for _ in range(num_of_rec):
            server_name: str = f"{choice(['b', 'c'])}{_}.voicetree.info"
            instance: Server = Server.objects.create(name=server_name)
            names.append(instance.name)
        return names

    def get_expected_value(self, field: str, last_digit: str):
        """ method for return expected value of Group model instance
        Args:
            field (str): Group model field\
        Returns:
            (str, int, dict, Json etc): Expected value of Group field
        """

        if field == "assigned_queues":
            return str(
                self.assigned_queues
            )  # typecast to str because field type is TextField in django model

        elif field == "kam_group_id":
            return 108  # value of kam_group_id of group_instance saved from setUpClass method

        elif field == "settings":
            return {
                "c_id": self.c_id,
                "d_id": self.d_id,
                "operator": self.operator,
                "caller_id": "11615680",
                "last_digit": last_digit,
            }

    # ################################################### TEST CAESE ##############################################
    def test_create_group(self):
        # calling `create_group` method
        group_id: int = GroupServerRelationManager.create_group(
            self.group_name,
            self.assigned_queues,
            self.kam_group_id,
            self.settings_dict,
            self.group_alias,
            self.region,
        )

        self.assertTrue(
            Group.objects.filter(
                name=self.group_name, group_alias=self.group_alias
            ).exists(),
            f"Group data of group `{self.group_name}` not created",
        )

        self.assertIsInstance(
            group_id,
            int,
            f"Expected return type of group_id is int, but actual return type is {type(group_id)}",
        )

    def test_create_server(self):
        server_name: str = (
            self.servers[0] if self.servers else "s1.voicettree.info"
        )

        # calling `create_server` method
        server_id: int = GroupServerRelationManager.create_server(server_name)

        self.assertTrue(
            Server.objects.filter(name=server_name).exists(),
            f"Server data of name `{server_name}` not created",
        )

        self.assertIsInstance(
            server_id,
            int,
            f"Expected return type of server_id is int, but actual return type is {type(server_id)}",
        )

    def test_create_group_server_relation(self):
        # create group
        group_instance: Group = Group.objects.create(
            name=self.group_name,
            group_alias=self.group_alias,
            region=self.region,
            assigned_queues=self.assigned_queues,
            is_enable=Group.YES,
            is_default=Group.NO,
            kam_group_id=self.kam_group_id,
            settings=self.settings_dict,
        )

        # create server
        server_instance: Server = Server.objects.create(name=self.servers[0])

        # calling `create_group_server_relation` method
        GroupServerRelationManager.create_group_server_relation(
            group_instance.id, server_instance.id
        )

        self.assertTrue(
            GroupServerRelationship.objects.filter(
                group=group_instance.id, server=server_instance.id
            ).exists(),
            f"GroupServerRelationship object not created for group_id {group_instance.id} and server_id {server_instance.id}",
        )

    def test_manage_default_number(self):
        # calling `manage_default_number` method
        GroupServerRelationManager.manage_default_number(
            self.group_instance.id, self.group_data
        )

        shared_did: str = f"{self.caller_id}{self.last_digit}"
        self.assertTrue(
            Number.objects.filter(number=shared_did).exists(),
            "Number object not created from manage_default_number",
        )

    def test_disable_groups(self):
        num_of_groups_rec: int = 5
        kam_group_ids: list = self.create_groups(num_of_rec=num_of_groups_rec)

        disabled_kam_group_ids: list = kam_group_ids[2:]
        enabled_kam_group_ids: list = kam_group_ids[:2]

        # calling `disable_groups` method
        GroupServerRelationManager.disable_groups(enabled_kam_group_ids)

        self.assertFalse(
            Group.objects.filter(
                kam_group_id__in=disabled_kam_group_ids, is_enable=Group.YES
            ).exists(),
            f"Groups of kam_group_ids: {disabled_kam_group_ids} not disabled",
        )

    def test_disable_servers(self):
        num_of_server_rec: int = 5
        kam_group_names: list = self.create_servers(
            num_of_rec=num_of_server_rec
        )

        disabled_kam_group_names: list = kam_group_names[2:]
        enabled_kam_group_names: list = kam_group_names[:2]

        # calling `disable_groups` method
        GroupServerRelationManager.disable_servers(enabled_kam_group_names)

        self.assertFalse(
            Server.objects.filter(
                name__in=disabled_kam_group_names, is_enable=Server.YES
            ).exists(),
            f"Server of names: {disabled_kam_group_names} not disabled",
        )

    def test_manage_servers_if_server_does_not_exists(self):
        group_id: int = self.group_instance.id
        server_name: str = self.servers[-1]

        is_server_exists: bool = Server.objects.filter(
            name=server_name
        ).exists()  # expected False

        # create new server and group_server_related for testing extra entries removing statement
        new_server: Server = Server.objects.create(name=self.servers[2])
        GroupServerRelationship.objects.create(
            group_id=group_id, server_id=new_server.id
        )

        # calling `manage_servers` method
        GroupServerRelationManager.manage_servers(group_id, [server_name])

        # fail test if `self.servers[-1]` already exist in db, because we are checking here for server which does not exists in db
        if not is_server_exists:
            self.assertTrue(
                Server.objects.filter(name=server_name).exists(),
                f"Server `{server_name}` not created from manage_servers",
            )
        else:
            self.assertTrue(
                False, f"Server with name {server_name} already exists"
            )

        # check `group_server_relation` create or not
        server_id: int = Server.objects.get(name=server_name).id
        self.assertTrue(
            GroupServerRelationship.objects.filter(
                group_id=group_id, server_id=server_id
            ).exists(),
            f"GroupServerRelationship does not create from manage_servers for group_id: {group_id} and server_id: {server_id}",
        )

        # check extra entries removed or not
        self.assertFalse(
            GroupServerRelationship.objects.filter(
                group_id=group_id, server_id=new_server.id
            ).exists(),
            "GroupServerRelationship extra entries not deleted",
        )

    def test_manage_servers_if_server_already_exists(self):
        group_id: int = self.group_instance.id
        server_name: str = self.server_instance.name

        is_server_exists: bool = Server.objects.filter(
            name=server_name
        ).exists()  # expected True

        # calling `manage_servers` method
        GroupServerRelationManager.manage_servers(group_id, [server_name])

        # fail test if `server_name` does exist in db, because we are checking here for server which already exists in db
        if is_server_exists:
            self.assertTrue(
                Server.objects.filter(name=server_name).exists(),
                f"Server `{server_name}` not updated from manage_servers",
            )
        else:
            self.assertTrue(
                False, f"Server with name {server_name} does not exists"
            )

        # check `group_server_relation` create or not
        server_id: int = self.server_instance.id
        self.assertTrue(
            GroupServerRelationship.objects.filter(
                group_id=group_id, server_id=server_id
            ).exists(),
            f"GroupServerRelationship does not create from manage_servers for group_id: {group_id} and server_id: {server_id}",
        )

    def test_add_group_servers(self):
        # mock `Helper.get_group_assigned_queues` method
        self.get_grp_assigned_q_patch = patch(
            self.get_grp_assigned_q_patch_path,
            return_value=self.assigned_queues,
        )
        self.get_grp_assigned_q_mock = self.get_grp_assigned_q_patch.start()

        # making group_data
        group_data: dict = deepcopy(self.group_data)
        group_data["last_digit"] = "4"

        # calling `add_group_servers` method
        GroupServerRelationManager.add_group_servers(
            self.kam_group_id, group_data
        )

        # check group created or not
        self.assertTrue(
            Group.objects.filter(name=self.group_name).exists(),
            "Group not created from `add_group_servers`",
        )

        # check server(s) created or not
        for server_name in self.servers:
            self.assertTrue(
                Server.objects.filter(name=server_name).exists(),
                f"Server name `{server_name}` not created or updated",
            )

        # check Number created or not
        shared_did: str = (
            f'{group_data["caller_id"]}{group_data["last_digit"]}'
        )
        self.assertTrue(
            Number.objects.filter(number=shared_did).exists(),
            "Number does not exists from add_group_servers",
        )

    def test_update_group_servers(self):
        # fetching group queryset
        group_obj: QuerySet = Group.objects.filter(id=self.group_instance.id)

        # making group_data
        last_digit: str = "6"
        group_data: dict = deepcopy(self.group_data)
        group_data["last_digit"] = last_digit
        group_data["group_alias"] = "MP Server"
        group_data["servers"] = [
            "d1.voicetree.org",
            "d2.voicetree.org",
            "d3.voicetree.org",
            "d4.voicetree.org",
            "d5.voicetree.org",
        ]

        # making duplicate copy of group_data for passing to `update_group_servers` method
        grp_data_copy: dict = deepcopy(group_data)

        # calling `update_group_servers` method
        GroupServerRelationManager.update_group_servers(
            group_obj, self.kam_group_id, grp_data_copy
        )

        # check group updated or not
        self.group_instance.refresh_from_db()  # refresh group instance

        group_fields: tuple = (
            "name",
            "region",
            "group_alias",
            "assigned_queues",
            "kam_group_id",
            "settings",
        )

        for field in group_fields:
            self.assertEqual(
                getattr(
                    self.group_instance, field
                ),  # instance attribute / property value
                group_data.get(
                    field, self.get_expected_value(field, last_digit)
                ),  # expected value of field
                f"Expected value of `{field}` is {group_data.get(field, self.get_expected_value(field, last_digit))}, \
                    but actual value is {getattr(self.group_instance, field)}",
            )

        # check server(s) created or not
        for server_name in group_data["servers"]:
            self.assertTrue(
                Server.objects.filter(name=server_name).exists(),
                f"Server name `{server_name}` not created or updated",
            )

        # check Number created or not
        shared_did: str = (
            f'{group_data["caller_id"]}{group_data["last_digit"]}'
        )
        self.assertTrue(
            Number.objects.filter(number=shared_did).exists(),
            "Number does not exists from add_group_servers",
        )
