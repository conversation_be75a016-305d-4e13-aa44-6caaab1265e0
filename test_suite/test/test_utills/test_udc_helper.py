import json
from uuid import uuid4

from django.conf import settings
from rest_framework import status

import pytest

from handlers.models import IvrInfo
from test_suite.test.factories import CentralRequestInfoFactory, IvrInfoFactory
from utills.cache_manager.main import CacheManager
from utills.exceptions import UDCLockedException, UDCUnavailable
from utills.helpers.helper import Helper
from utills.udc_helper import UDCHelper


@pytest.mark.unittest
@pytest.mark.django_db
@pytest.mark.parametrize(
    "req_file,res_file,udc_check",
    [
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            1,
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            0,
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            1,
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
            0,
        ),
    ],
)
def test_udc_api_checks_success(
    req_file, res_file, udc_check, load_json, mock_udc_apis
):
    request = load_json(req_file)
    udc_response = load_json(res_file)
    instance = CentralRequestInfoFactory(
        c_id=request["company_id"],
        ivr_id=request["ivr_id"],
        user_id=request["user_id"] if request.get("user_id") else None,
        request_type=request["type"],
        request_id=request["request_id"],
        raw_data=json.dumps(request),
        job_id=request["job_id"],
    )
    IvrInfoFactory(
        ivr_type=IvrInfo.P2P,
        c_id=instance.c_id,
        ivr_id=instance.ivr_id,
        common_setting={},
        company_display_number=request["company_display_number"],
    )
    Helper.get_ivrinfo_common_setting(
        instance.ivr_id,
        settings.UDC_API_TOGGLE_KEY,
        udc_check,
    )
    if instance.request_type == "1":
        udc_response["data"]["user_data"]["uuid"] = instance.user_id
        udc_response["data"]["user_data"]["company"] = instance.c_id

    user_view_only = 0
    user_is_avail = udc_check
    mock_udc_apis(
        instance,
        {"response": udc_response, "status_code": status.HTTP_200_OK},
        user_view_only=user_view_only,
        user_is_avail=user_is_avail,
    )
    helper = UDCHelper(instance)
    helper.udc_api_checks(user_view_only)
    assert helper.udc_response == udc_response["data"]


@pytest.mark.unittest
@pytest.mark.django_db
def test_udc_api_checks_locked_udc(load_json, mock_udc_apis):
    request = load_json("central_request_info/type_1.json")
    udc_response = load_json(
        "external_apis/udc/user_view_only_enabled_success.json"
    )
    instance = CentralRequestInfoFactory(
        c_id=request["company_id"],
        ivr_id=request["ivr_id"],
        user_id=request["user_id"] if request.get("user_id") else None,
        request_type=request["type"],
        request_id=request["request_id"],
        raw_data=json.dumps(request),
        job_id=request["job_id"],
    )
    IvrInfoFactory(
        ivr_type=IvrInfo.P2P,
        c_id=instance.c_id,
        ivr_id=instance.ivr_id,
        common_setting={},
        company_display_number=request["company_display_number"],
    )

    cache_key = "{prefix}{key}".format(
        prefix=settings.CACHE_UDC_LOCKED_USERS_KEY_NAME,
        key=instance.user_id,
    )
    CacheManager.set_value(cache_key, 1, 2)

    udc_response["data"]["user_data"]["uuid"] = instance.user_id
    udc_response["data"]["user_data"]["company"] = instance.c_id

    user_view_only = 0

    helper = UDCHelper(instance)
    with pytest.raises(
        UDCLockedException,
        match=f"UDC is locked for request_id - {instance.request_id}",
    ):
        helper.udc_api_checks(user_view_only)
    assert not helper.udc_response


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "req_file,res_file,udc_check",
    [
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            1,
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_not_available.json",
            1,
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_not_available.json",
            0,
        ),
    ],
)
def test_udc_api_checks_not_available(
    req_file, res_file, udc_check, load_json, mock_udc_apis
):
    request = load_json(req_file)
    udc_response = load_json(res_file)
    instance = CentralRequestInfoFactory(
        c_id=request["company_id"],
        ivr_id=request["ivr_id"],
        user_id=request["user_id"] if request.get("user_id") else None,
        request_type=request["type"],
        request_id=request["request_id"],
        raw_data=json.dumps(request),
        job_id=request["job_id"],
    )
    IvrInfoFactory(
        ivr_type=IvrInfo.P2P,
        c_id=instance.c_id,
        ivr_id=instance.ivr_id,
        common_setting={},
        company_display_number=request["company_display_number"],
    )
    Helper.get_ivrinfo_common_setting(
        instance.ivr_id,
        settings.UDC_API_TOGGLE_KEY,
        udc_check,
    )
    if instance.request_type == "1":
        udc_response["data"]["user_data"]["uuid"] = instance.user_id
        udc_response["data"]["user_data"]["company"] = instance.c_id

    user_view_only = 0
    user_is_avail = udc_check
    mock_udc_apis(
        instance,
        {"response": udc_response, "status_code": status.HTTP_200_OK},
        user_view_only=user_view_only,
        user_is_avail=user_is_avail,
    )
    helper = UDCHelper(instance)
    with pytest.raises(
        UDCUnavailable,
        match=f"UDC Unavailable for request_id - {instance.request_id}, ivr_id - {instance.ivr_id}",
    ):
        helper.udc_api_checks(user_view_only)
    assert not helper.udc_response


@pytest.mark.unittest
@pytest.mark.django_db
def test_get_avail_channels_from_response(load_json):
    request = load_json("central_request_info/type_1.json")
    udc_response = load_json(
        "external_apis/udc/user_view_only_enabled_success.json",
    )
    instance = CentralRequestInfoFactory(
        c_id=request["company_id"],
        ivr_id=request["ivr_id"],
        user_id=request["user_id"] if request.get("user_id") else None,
        request_type=request["type"],
        request_id=request["request_id"],
        raw_data=json.dumps(request),
        job_id=request["job_id"],
    )
    helper = UDCHelper(instance)
    helper.udc_response = udc_response["data"]
    assert (
        helper.get_avail_channels_from_response()
        == udc_response["data"]["channel_data"]
    )


@pytest.mark.unittest
@pytest.mark.django_db
def test_get_avail_channels_from_response_error(load_json):
    request = load_json("central_request_info/type_1.json")
    udc_response = load_json(
        "external_apis/udc/user_view_only_enabled_success.json",
    )
    instance = CentralRequestInfoFactory(
        c_id=request["company_id"],
        ivr_id=request["ivr_id"],
        user_id=request["user_id"] if request.get("user_id") else None,
        request_type=request["type"],
        request_id=request["request_id"],
        raw_data=json.dumps(request),
        job_id=request["job_id"],
    )
    helper = UDCHelper(instance)
    assert helper.get_avail_channels_from_response() == 0
    del udc_response["data"]["channel_data"]
    helper.udc_response = udc_response
    assert helper.get_avail_channels_from_response() == 0


@pytest.mark.unittest
@pytest.mark.django_db
def test_check_keys_in_cache(load_json):
    request = load_json("central_request_info/type_1.json")
    udc_response = load_json(
        "external_apis/udc/user_view_only_enabled_success.json",
    )
    instance = CentralRequestInfoFactory(
        c_id=request["company_id"],
        ivr_id=request["ivr_id"],
        user_id=request["user_id"] if request.get("user_id") else None,
        request_type=request["type"],
        request_id=request["request_id"],
        raw_data=json.dumps(request),
        job_id=request["job_id"],
    )
    instance = CentralRequestInfoFactory(
        c_id=request["company_id"],
        ivr_id=request["ivr_id"],
        user_id=request["user_id"] if request.get("user_id") else None,
        request_type=request["type"],
        request_id=uuid4(),
        raw_data=json.dumps(request),
        job_id=request["job_id"],
    )
    udc_response["data"]["channel_data"] = 5

    helper = UDCHelper(instance)
    helper.udc_response = udc_response["data"]
    helper.set_request_in_cache()
    helper_2 = UDCHelper(instance)
    helper_2.udc_response = udc_response["data"]
    helper_2.set_request_in_cache()

    assert helper.check_keys_in_cache()
    udc_response["data"]["channel_data"] = 1
    helper.udc_response = udc_response["data"]
    with pytest.raises(
        UDCUnavailable, match="keys count is more then available channels."
    ):
        helper.check_keys_in_cache()


@pytest.mark.unittest
@pytest.mark.django_db
@pytest.mark.parametrize(
    "req_file,res_file,udc_check",
    [
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            1,
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            0,
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            1,
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
            0,
        ),
    ],
)
def test_udc_checks(req_file, res_file, udc_check, load_json, mock_udc_apis):
    request = load_json(req_file)
    udc_response = load_json(res_file)
    instance = CentralRequestInfoFactory(
        c_id=request["company_id"],
        ivr_id=request["ivr_id"],
        user_id=request["user_id"] if request.get("user_id") else None,
        request_type=request["type"],
        request_id=request["request_id"],
        raw_data=json.dumps(request),
        job_id=request["job_id"],
    )
    instance = CentralRequestInfoFactory(
        c_id=request["company_id"],
        ivr_id=request["ivr_id"],
        user_id=request["user_id"] if request.get("user_id") else None,
        request_type=request["type"],
        request_id=uuid4(),
        raw_data=json.dumps(request),
        job_id=request["job_id"],
    )
    IvrInfoFactory(
        ivr_type=IvrInfo.P2P,
        c_id=instance.c_id,
        ivr_id=instance.ivr_id,
        common_setting={},
        company_display_number=request["company_display_number"],
    )
    Helper.get_ivrinfo_common_setting(
        instance.ivr_id,
        settings.UDC_API_TOGGLE_KEY,
        udc_check,
    )
    if instance.request_type == "1":
        udc_response["data"]["user_data"]["uuid"] = instance.user_id
        udc_response["data"]["user_data"]["company"] = instance.c_id

    udc_response["data"]["channel_data"] = 5

    user_view_only = 0
    user_is_avail = udc_check
    mock_udc_apis(
        instance,
        {"response": udc_response, "status_code": status.HTTP_200_OK},
        user_view_only=user_view_only,
        user_is_avail=user_is_avail,
    )
    helper_2 = UDCHelper(instance)
    helper_2.set_request_in_cache()

    helper = UDCHelper(instance)
    helper.set_request_in_cache()
    assert helper.udc_checks(user_view_only)


@pytest.mark.unittest
@pytest.mark.django_db
@pytest.mark.parametrize(
    "req_file,res_file,udc_check",
    [
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            1,
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            0,
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            1,
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
            0,
        ),
    ],
)
def test_udc_checks_error(
    req_file, res_file, udc_check, load_json, mock_udc_apis
):
    request = load_json(req_file)
    udc_response = load_json(res_file)
    instance = CentralRequestInfoFactory(
        c_id=request["company_id"],
        ivr_id=request["ivr_id"],
        user_id=request["user_id"] if request.get("user_id") else None,
        request_type=request["type"],
        request_id=request["request_id"],
        raw_data=json.dumps(request),
        job_id=request["job_id"],
    )
    instance = CentralRequestInfoFactory(
        c_id=request["company_id"],
        ivr_id=request["ivr_id"],
        user_id=request["user_id"] if request.get("user_id") else None,
        request_type=request["type"],
        request_id=uuid4(),
        raw_data=json.dumps(request),
        job_id=request["job_id"],
    )
    IvrInfoFactory(
        ivr_type=IvrInfo.P2P,
        c_id=instance.c_id,
        ivr_id=instance.ivr_id,
        common_setting={},
        company_display_number=request["company_display_number"],
    )
    Helper.get_ivrinfo_common_setting(
        instance.ivr_id,
        settings.UDC_API_TOGGLE_KEY,
        udc_check,
    )
    if instance.request_type == "1":
        udc_response["data"]["user_data"]["uuid"] = instance.user_id
        udc_response["data"]["user_data"]["company"] = instance.c_id

    udc_response["data"]["channel_data"] = 1

    user_view_only = 0
    user_is_avail = udc_check
    mock_udc_apis(
        instance,
        {"response": udc_response, "status_code": status.HTTP_200_OK},
        user_view_only=user_view_only,
        user_is_avail=user_is_avail,
    )
    helper_2 = UDCHelper(instance)
    helper_2.set_request_in_cache()

    helper = UDCHelper(instance)
    helper.set_request_in_cache()
    with pytest.raises(
        UDCUnavailable, match="keys count is more then available channels."
    ):
        helper.udc_checks(user_view_only)


@pytest.mark.unittest
@pytest.mark.django_db
def test_set_request_in_cache(load_json):
    request = load_json("central_request_info/type_1.json")
    instance = CentralRequestInfoFactory(
        c_id=request["company_id"],
        ivr_id=request["ivr_id"],
        user_id=request["user_id"] if request.get("user_id") else None,
        request_type=request["type"],
        request_id=request["request_id"],
        raw_data=json.dumps(request),
        job_id=request["job_id"],
    )
    helper = UDCHelper(instance)
    assert not helper.cache.get_value(helper.cache.key)
    helper.set_request_in_cache()
    assert helper.cache.get_value(helper.cache.key) == "1"
