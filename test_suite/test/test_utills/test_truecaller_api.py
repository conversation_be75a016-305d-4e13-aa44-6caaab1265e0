from django.conf import settings
from django.test import override_settings
from rest_framework import status

import pytest

from test_suite.conftest import TRUE_CALLER_TOKEN
from utills.external_apis import TrueCallerApi
from utills.helpers.common_settings import TrueCallerCommonSettings


@pytest.mark.django_db
@override_settings(TRUE_CALLER_ENABLED=False)
def test_is_enabled():
    assert TrueCallerApi.is_enabled() is False


@pytest.mark.django_db
def test_get_url(truecaller_api):
    # Test with path starting with /
    url = truecaller_api._get_url("/test/path")
    assert url == f"{settings.TRUE_CALLER_API_URL}test/path"

    # Test with path not starting with /
    url = truecaller_api._get_url("test/path")
    assert url == f"{settings.TRUE_CALLER_API_URL}test/path"


@pytest.mark.django_db
def test_get_token_from_cache(truecaller_api, clean_cache):
    # Set a token in the cache
    cached_token = "cached_token_value"
    truecaller_api.token_cache.set(cached_token)

    # Get the token
    token = truecaller_api.get_token()

    # Assert the token is the cached token
    assert token == cached_token


@pytest.mark.django_db
def test_get_token_from_api(
    truecaller_api, mock_true_caller_token_api, clean_cache
):
    mock_true_caller_token_api()
    # Get the token
    token = truecaller_api.get_token()
    # Assert the token is from the API
    assert token == TRUE_CALLER_TOKEN

    # Verify the token was cached
    assert truecaller_api.token_cache.get() == TRUE_CALLER_TOKEN


@pytest.mark.django_db
def test_talk_to_token_api_success(truecaller_api, mock_true_caller_token_api):
    mock_true_caller_token_api()
    # Call the method
    token = truecaller_api.talk_to_token_api()
    # Assert the token is from the API
    assert token == TRUE_CALLER_TOKEN


@pytest.mark.django_db
def test_talk_to_token_api_failure(truecaller_api, mock_true_caller_token_api):
    mock_true_caller_token_api(status_code=status.HTTP_401_UNAUTHORIZED)
    # Call the method
    token = truecaller_api.talk_to_token_api()

    # Assert the token is None
    assert token is None


@pytest.mark.django_db
def test_talk_to_token_api_missing_token(
    truecaller_api, mock_true_caller_token_api
):
    mock_true_caller_token_api(response={"status": "success"})
    # Call the method
    token = truecaller_api.talk_to_token_api()
    # Assert the token is None
    assert token is None


@pytest.mark.django_db
def test_talk_to_dial_assist_api_success(
    truecaller_api, mock_true_caller_dial_assist_api, clean_cache
):
    # Set a token in the cache
    token = "test_token"
    truecaller_api.token_cache.set(token)

    # Test data
    caller_number = "1234567890"
    receiver_number = "0987654321"
    mock_true_caller_dial_assist_api()

    # Call the method
    _, status_code = truecaller_api.talk_to_dial_assist_api(
        caller_number, receiver_number
    )
    # Assert the response
    assert status_code == status.HTTP_200_OK


@pytest.mark.django_db
def test_get_token_ttl(truecaller_api):
    # The TTL should be 1 hour - 30 seconds
    expected_ttl = (60 * 60) - 30
    assert truecaller_api._get_token_ttl() == expected_ttl


# TrueCallerCommonSettings Tests
@pytest.mark.django_db
def test_truecaller_common_settings_get_true_caller_common_settings_creates_new():
    """Test that get_true_caller_common_settings creates a new record if it doesn't exist"""
    from handlers.models import CommonSetting

    # Ensure no existing record
    CommonSetting.objects.filter(entity="true_caller").delete()

    settings_manager = TrueCallerCommonSettings()
    obj = settings_manager.get_true_caller_common_settings()

    assert obj.entity == "true_caller"
    assert obj.get_settings["enabled"] == settings.TRUE_CALLER_ENABLED
    assert obj.get_settings["company_ids"] == []


@pytest.mark.django_db
def test_truecaller_common_settings_get_true_caller_common_settings_returns_existing():
    """Test that get_true_caller_common_settings returns existing record"""
    from handlers.models import CommonSetting
    import json

    # Create an existing record
    existing_obj = CommonSetting.objects.create(
        entity="true_caller",
        settings=json.dumps({"enabled": False, "company_ids": ["test123"]}),
    )

    settings_manager = TrueCallerCommonSettings()
    obj = settings_manager.get_true_caller_common_settings()

    assert obj.id == existing_obj.id
    assert obj.get_settings["enabled"] is False
    assert obj.get_settings["company_ids"] == ["test123"]


@pytest.mark.django_db
def test_truecaller_common_settings_get_company_id():
    """Test get_company_id method"""
    from handlers.models import CommonSetting
    import json

    # Create a record with company IDs
    CommonSetting.objects.create(
        entity="true_caller",
        settings=json.dumps(
            {"enabled": True, "company_ids": ["comp1", "comp2"]}
        ),
    )

    settings_manager = TrueCallerCommonSettings()
    company_ids = settings_manager.get_company_id()

    assert company_ids == ["comp1", "comp2"]


@pytest.mark.django_db
def test_truecaller_common_settings_get_company_id_empty():
    """Test get_company_id method returns empty list when no company_ids"""
    from handlers.models import CommonSetting
    import json

    # Create a record without company IDs
    CommonSetting.objects.create(
        entity="true_caller", settings=json.dumps({"enabled": True})
    )

    settings_manager = TrueCallerCommonSettings()
    company_ids = settings_manager.get_company_id()

    assert company_ids == []


@pytest.mark.django_db
def test_truecaller_common_settings_is_enabled_false_when_env_disabled():
    """Test is_enabled returns False when environment variable is False"""
    settings_manager = TrueCallerCommonSettings()
    # Manually set IS_ENABLED to False to test the behavior
    settings_manager.IS_ENABLED = False
    assert settings_manager.is_enabled() is False


@pytest.mark.django_db
def test_truecaller_common_settings_is_enabled_false_when_db_disabled():
    """Test is_enabled returns False when database setting is disabled"""
    from handlers.models import CommonSetting
    import json

    # Create a record with enabled=False
    CommonSetting.objects.create(
        entity="true_caller",
        settings=json.dumps({"enabled": False, "company_ids": []}),
    )

    settings_manager = TrueCallerCommonSettings()
    assert settings_manager.is_enabled() is False


@pytest.mark.django_db
def test_truecaller_common_settings_is_enabled_true():
    """Test is_enabled returns True when both env and db are enabled"""
    from handlers.models import CommonSetting
    import json

    # Create a record with enabled=True
    CommonSetting.objects.create(
        entity="true_caller",
        settings=json.dumps({"enabled": True, "company_ids": []}),
    )

    settings_manager = TrueCallerCommonSettings()
    assert settings_manager.is_enabled() is True


@pytest.mark.django_db
def test_truecaller_common_settings_is_enabled_for_company_false_when_env_disabled():
    """Test is_enabled_for_company returns False when environment variable is False"""
    settings_manager = TrueCallerCommonSettings()
    # Manually set IS_ENABLED to False to test the behavior
    settings_manager.IS_ENABLED = False
    assert settings_manager.is_enabled_for_company("test_company") is False


@pytest.mark.django_db
def test_truecaller_common_settings_is_enabled_for_company_false_when_no_company_ids():
    """Test is_enabled_for_company returns False when no company IDs configured"""
    from handlers.models import CommonSetting
    import json

    # Create a record with enabled=True but no company IDs
    CommonSetting.objects.create(
        entity="true_caller",
        settings=json.dumps({"enabled": True, "company_ids": []}),
    )

    settings_manager = TrueCallerCommonSettings()
    assert settings_manager.is_enabled_for_company("test_company") is False


@pytest.mark.django_db
def test_truecaller_common_settings_is_enabled_for_company_false_when_company_not_in_list():
    """Test is_enabled_for_company returns False when company not in allowed list"""
    from handlers.models import CommonSetting
    import json

    # Create a record with enabled=True and specific company IDs
    CommonSetting.objects.create(
        entity="true_caller",
        settings=json.dumps(
            {"enabled": True, "company_ids": ["comp1", "comp2"]}
        ),
    )

    settings_manager = TrueCallerCommonSettings()
    assert settings_manager.is_enabled_for_company("comp3") is False


@pytest.mark.django_db
def test_truecaller_common_settings_is_enabled_for_company_true():
    """Test is_enabled_for_company returns True when company is in allowed list"""
    from handlers.models import CommonSetting
    import json

    # Create a record with enabled=True and specific company IDs
    CommonSetting.objects.create(
        entity="true_caller",
        settings=json.dumps(
            {"enabled": True, "company_ids": ["comp1", "comp2"]}
        ),
    )

    settings_manager = TrueCallerCommonSettings()
    assert settings_manager.is_enabled_for_company("comp1") is True
    assert settings_manager.is_enabled_for_company("comp2") is True


@pytest.mark.django_db
def test_truecaller_common_settings_remove_object():
    """Test remove_object deletes the CommonSetting record"""
    from handlers.models import CommonSetting
    import json

    # Create a record
    CommonSetting.objects.create(
        entity="true_caller",
        settings=json.dumps({"enabled": True, "company_ids": []}),
    )

    # Verify it exists
    assert CommonSetting.objects.filter(entity="true_caller").exists()

    # Remove it
    settings_manager = TrueCallerCommonSettings()
    settings_manager.remove_object()

    # Verify it's deleted
    assert not CommonSetting.objects.filter(entity="true_caller").exists()


# Additional TrueCallerApi Tests for edge cases and error handling
@pytest.mark.django_db
def test_truecaller_api_get_url_with_trailing_slash():
    """Test _get_url method with base URL having trailing slash"""
    api = TrueCallerApi()
    # Mock the base URL with trailing slash
    with override_settings(TRUE_CALLER_API_URL="https://api.truecaller.com/"):
        url = api._get_url("/test/path")
        assert url == "https://api.truecaller.com/test/path"


@pytest.mark.django_db
def test_truecaller_api_get_url_without_trailing_slash():
    """Test _get_url method with base URL without trailing slash"""
    api = TrueCallerApi()
    # Mock the base URL without trailing slash
    with override_settings(TRUE_CALLER_API_URL="https://api.truecaller.com"):
        url = api._get_url("test/path")
        assert url == "https://api.truecaller.com/test/path"


@pytest.mark.django_db
def test_truecaller_api_get_token_when_api_fails(
    truecaller_api, mock_true_caller_token_api
):
    """Test get_token when API call fails"""
    from rest_framework import status

    # Mock the token API to fail
    mock_true_caller_token_api(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
    )

    # This will cause the API call to fail
    token = truecaller_api.get_token()
    assert token is None


@pytest.mark.django_db
def test_truecaller_api_talk_to_dial_assist_api_without_token():
    """Test talk_to_dial_assist_api when no token is available"""
    api = TrueCallerApi()

    # Mock get_token to return None
    def mock_get_token():
        return None

    api.get_token = mock_get_token

    result = api.talk_to_dial_assist_api("1234567890", "0987654321")
    assert result is None


@pytest.mark.django_db
def test_truecaller_api_talk_to_dial_assist_api_failure(
    truecaller_api, mock_true_caller_dial_assist_api
):
    """Test talk_to_dial_assist_api when API call fails"""
    # Set a token in the cache
    token = "test_token"
    truecaller_api.token_cache.set(token)

    # Mock the dial assist API to fail
    mock_true_caller_dial_assist_api(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
    )

    # Test data
    caller_number = "1234567890"
    receiver_number = "0987654321"

    # Call the method
    _, status_code = truecaller_api.talk_to_dial_assist_api(
        caller_number, receiver_number
    )

    # Assert the response
    assert status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


@pytest.mark.django_db
def test_truecaller_api_private_methods():
    """Test private getter methods"""
    api = TrueCallerApi()

    assert api._get_api_key() == settings.TRUE_CALLER_API_KEY
    assert api._get_client_id() == settings.TRUE_CALLER_CLIENT_ID
    assert api._get_key_id() == settings.TRUE_CALLER_KEY_ID


@pytest.mark.django_db
def test_truecaller_api_token_cache_operations():
    """Test token cache operations"""
    api = TrueCallerApi()

    # Test setting and getting token
    test_token = "test_token_123"
    api.token_cache.set(test_token, ttl=3600)

    cached_token = api.token_cache.get()
    assert cached_token == test_token

    # Test deleting token
    api.token_cache.delete()
    deleted_token = api.token_cache.get()
    assert deleted_token is None


@pytest.mark.django_db
def test_truecaller_api_talk_to_token_api_network_error(
    truecaller_api, mock_true_caller_token_api
):
    """Test talk_to_token_api when network error occurs"""
    from rest_framework import status

    # Mock the token API to return an error status
    mock_true_caller_token_api(status_code=status.HTTP_503_SERVICE_UNAVAILABLE)

    # Call the method
    token = truecaller_api.talk_to_token_api()

    # Assert the token is None due to error
    assert token is None


@pytest.mark.django_db
def test_truecaller_api_get_token_uses_cached_token():
    """Test that get_token uses cached token when available"""
    api = TrueCallerApi()

    # Set a token in cache
    cached_token = "cached_token_value"
    api.token_cache.set(cached_token)

    # Mock talk_to_token_api to return a different token
    def mock_talk_to_token_api():
        return "new_token_from_api"

    api.talk_to_token_api = mock_talk_to_token_api

    # Get token should return cached token, not call API
    token = api.get_token()
    assert token == cached_token


@pytest.mark.django_db
def test_truecaller_api_get_token_calls_api_when_no_cache():
    """Test that get_token calls API when no cached token"""
    api = TrueCallerApi()

    # Ensure no cached token
    api.token_cache.delete()

    # Mock talk_to_token_api to return a token
    api_token = "new_token_from_api"

    def mock_talk_to_token_api():
        return api_token

    api.talk_to_token_api = mock_talk_to_token_api

    # Get token should call API and cache the result
    token = api.get_token()
    assert token == api_token

    # Verify token was cached
    assert api.token_cache.get() == api_token
