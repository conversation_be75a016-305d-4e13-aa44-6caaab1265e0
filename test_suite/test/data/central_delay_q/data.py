import json


# Body of SQS queue data from `obd_central_delay_queue`
body_data: dict = {
    'reference_id': None,
    'service_flow_router': 'call',
    'is_successfully_validated': 1,
    'company_id': '1',
    'secret_token': 'abc',
    'type': '2',
    'ivr_id': '1',
    'number': '+919191919191',
    'custom_1': 'a',
    'ttl_reference_id': 15000,
    'request_id': 'd482e062-2d29-11ea-a99f-5add6278f6f2',
    'request_time': '2020-01-02 06:33:47',
    'priority': 'NA',
    'delay': 'NA',
    'expiry': '1',
    'service_mapping': {
        '1': 'logic'
    },
    'ivr_priority': 1
}


# Data received from SQS queue: `obd_central_delay_queue`
q_data: dict = {
    'Messages': [{
        'Body': json.dumps(body_data),
        'ReceiptHandle': 'string_value'
    }]
}
