from central.settings.handlers import conf

IVR_IDS = ["55855d0edf16b391", "55855d0edf17b391", "55855d0edf18b391"]

SQS_QUEUE_INFO_DATA = [
    {
        "gateway_prefix": conf.CENTRAL_PQ_GATEWAY_PREFIX,
        "queue_url": "https://ap-southeast-1.queue.amazonaws.com/************/obd-central-pq2",
        "queue_name": conf.CENTRAL_PQ2,
    },
    {
        "gateway_prefix": conf.CENTRAL_PQ_GATEWAY_PREFIX,
        "queue_url": "https://ap-southeast-1.queue.amazonaws.com/************/obd-central-pq3",
        "queue_name": conf.CENTRAL_PQ3,
    },
    {
        "gateway_prefix": conf.PROCESS_MANAGER_GATEWAY_PREFIX,
        "queue_url": "https://ap-southeast-1.queue.amazonaws.com/************/obd_pm_invoker",
        "queue_name": conf.PROCESS_MANAGER_QUEUE,
    },
    {
        "gateway_prefix": conf.ON_HOLD_Q_GATEWAY_PREFIX,
        "queue_url": "https://ap-southeast-1.queue.amazonaws.com/************/obd-central-on-hold",
        "queue_name": conf.ON_HOLD_Q_NAME,
    },
    {
        "gateway_prefix": conf.GROUPS_GATEWAY,
        "queue_url": "https://ap-southeast-1.queue.amazonaws.com/************/obd-central-group-queues_p_q_37",
        "queue_name": f"{conf.QUEUES_PREFIX_LIST[0]}p_q_37",
    },
    {
        "gateway_prefix": conf.GROUPS_GATEWAY,
        "queue_url": "https://ap-southeast-1.queue.amazonaws.com/************/obd-central-group-queues_np_q_37",
        "queue_name": f"{conf.QUEUES_PREFIX_LIST[0]}np_q_37",
    },
    {
        "gateway_prefix": conf.CENTRAL_IVRQ_GATEWAY_PREFIX,
        "queue_url": f"https://ap-southeast-1.queue.amazonaws.com/************/obd-central-ivrid-{IVR_IDS[0]}",
        "queue_name": f"{conf.CACHE_IVR_KEY_NAME}{IVR_IDS[0]}",
    },
]


IVR_INFO_DATA = [
    {
        "c_id": "1",
        "common_setting": {
            "call_expiry": 86400,
            "company_ip_list": [],
            "failover": 1,
            "misscall_expiry": 100,
            "num_throttle": 2323,
            "obdv2_call_direction": "agent",
            "on_hold_max_ivr_threshold": 10,
            "on_hold_max_user_check_count": 5,
            "shared_backup": 1,
            "ttl_num": 2000,
            "ttl_reference_id": 2323,
        },
        "company_display_number": "911204022601",
        "company_priority": 1,
        "country_code": "91",
        "id": 2,
        "ivr_id": IVR_IDS[0],
        "ivr_priority": 1,
        "ivr_type": 2,
    },
    {
        "c_id": "1",
        "common_setting": {
            "call_expiry": 86400,
            "company_ip_list": [],
            "failover": 1,
            "misscall_expiry": 100,
            "num_throttle": 2323,
            "obdv2_call_direction": "agent",
            "on_hold_max_ivr_threshold": 10,
            "on_hold_max_user_check_count": 5,
            "shared_backup": 1,
            "ttl_num": 2000,
            "ttl_reference_id": 2323,
        },
        "company_display_number": "911204022601",
        "company_priority": 1,
        "country_code": "91",
        "id": 3,
        "ivr_id": IVR_IDS[1],
        "ivr_priority": 1,
        "ivr_type": 1,
    },
    {
        "c_id": "1",
        "common_setting": {
            "call_expiry": 86400,
            "company_ip_list": [],
            "failover": 1,
            "misscall_expiry": 100,
            "num_throttle": 2323,
            "obdv2_call_direction": "agent",
            "on_hold_max_ivr_threshold": 10,
            "on_hold_max_user_check_count": 5,
            "shared_backup": 1,
            "ttl_num": 2000,
            "ttl_reference_id": 2323,
        },
        "company_display_number": "911204022601",
        "company_priority": 1,
        "country_code": "91",
        "id": 4,
        "ivr_id": IVR_IDS[2],
        "ivr_priority": 2,
        "ivr_type": 3,
    },
]


DUMMY_REQUESTS = [
    {
        "is_successfully_validated": 1,
        "company_id": "60a392bbe3d97152",
        "type": "1",
        "ivr_id": IVR_IDS[0],
        "number": 7860985234,
        "public_ivr_id": "60a49c4c07ba8419",
        "number_cc": 91,
        "call_expiry": "1",
        "misscall_expiry": "1",
        "company_display_number": "919319368366",
        "secret_token": "953e784b7d4251dac5c47233ad6761e6cfa79d4eb953675e27792fb1193c6c5e",
        "obd_v2_settings": {"shared_backup": 1, "failover": 1},
        "user_id": "61e66e986558c735",
        "reference_id": "a1580c8a-7f47-430d-9e96-5a6cefadbefd",
        "request_id": "5bd61b2c-b006-11ec-b0f9-164f4aa09487",
        "request_time": "2022-03-30 08:49:49",
        "exc_info": "NoneType: None",
        "dial_reverse": 1,
        "call_hold": True,
    },
    {
        "is_successfully_validated": 1,
        "company_id": "60d02bf71af0c636",
        "type": "1",
        "ivr_id": IVR_IDS[1],
        "number": 8860565234,
        "public_ivr_id": "612f339c3e64f474",
        "number_cc": 91,
        "call_expiry": "1",
        "misscall_expiry": "1",
        "company_display_number": "918587908013",
        "secret_token": "2f0a9a9a52640bf51b8d441bf80cdb1bb7fe9294ab12b7ac72bbff2448f69e5c",
        "obd_v2_settings": {"shared_backup": 1, "failover": 1},
        "user_id": "62147ba269dea820",
        "request_id": "5a1ae830-b006-11ec-b0f9-164f4aa09487",
        "request_time": "2022-03-30 08:49:46",
        "exc_info": "NoneType: None",
        "dial_reverse": 1,
        "call_hold": True,
    },
    {
        "is_successfully_validated": 1,
        "company_id": "60a392bbe3d97152",
        "type": "1",
        "ivr_id": IVR_IDS[2],
        "number": 8788115705,
        "public_ivr_id": "60a49c4c07ba8419",
        "number_cc": 91,
        "call_expiry": "1",
        "misscall_expiry": "1",
        "company_display_number": "919319368366",
        "secret_token": "953e784b7d4251dac5c47233ad6761e6cfa79d4eb953675e27792fb1193c6c5e",
        "obd_v2_settings": {"shared_backup": 1, "failover": 1},
        "user_id": "61bdc0b194510576",
        "reference_id": "804a99cb-9907-4de3-bf37-5a07246684a4",
        "request_id": "5a4b1ba4-b006-11ec-b0f9-164f4aa09487",
        "request_time": "2022-03-30 08:49:47",
        "exc_info": "NoneType: None",
        "dial_reverse": 1,
        "call_hold": True,
    },
    {
        "is_successfully_validated": 1,
        "company_id": "5fec32b6dbd3b453",
        "type": "1",
        "ivr_id": IVR_IDS[0],
        "number": 7307169744,
        "public_ivr_id": "5ffe7d572b1f5935",
        "number_cc": 91,
        "call_expiry": "1",
        "misscall_expiry": "1",
        "company_display_number": "917042882883",
        "secret_token": "579d9b7492e558b01f1023c191774cab23465b4d73f2437d8d38816ab3cdf18e",
        "obd_v2_settings": {"shared_backup": 1, "failover": 1},
        "user_id": "609607168ac73793",
        "reference_id": "11b2baae-bfd5-43c2-9212-aa7403df6a46",
        "request_id": "5931b688-b006-11ec-b0f9-164f4aa09487",
        "request_time": "2022-03-30 08:49:45",
        "exc_info": "NoneType: None",
        "dial_reverse": 1,
        "call_hold": True,
    },
    {
        "is_successfully_validated": 1,
        "company_id": "5a8faa138a57f584",
        "type": "2",
        "ivr_id": IVR_IDS[1],
        "number": 9890790925,
        "public_ivr_id": "5f880780229a4287",
        "number_cc": 91,
        "call_expiry": "1",
        "misscall_expiry": "1",
        "company_display_number": "919870140141",
        "secret_token": "3203a0ac4e09057d40f721f1f1f0f5b5d0bb868a9fb5aa4c320f078a645ca9ea",
        "obd_v2_settings": {"shared_backup": 1, "failover": 1},
        "reference_id": "40324318",
        "request_id": "5888dc34-b006-11ec-b0f9-164f4aa09487",
        "request_time": "2022-03-30 08:49:44",
        "exc_info": "NoneType: None",
        "dial_reverse": 0,
        "call_hold": True,
    },
    {
        "is_successfully_validated": 1,
        "company_id": "5a8faa138a57f584",
        "type": "2",
        "ivr_id": IVR_IDS[2],
        "number": 9850428972,
        "public_ivr_id": "5f880780229a4287",
        "number_cc": 91,
        "call_expiry": "1",
        "misscall_expiry": "1",
        "company_display_number": "919870140141",
        "secret_token": "3203a0ac4e09057d40f721f1f1f0f5b5d0bb868a9fb5aa4c320f078a645ca9ea",
        "obd_v2_settings": {"shared_backup": 1, "failover": 1},
        "reference_id": "47919265",
        "request_id": "5865520a-b006-11ec-b0f9-164f4aa09487",
        "request_time": "2022-03-30 08:49:44",
        "exc_info": "NoneType: None",
        "dial_reverse": 0,
    },
    {
        "is_successfully_validated": 1,
        "company_id": "5a8faa138a57f584",
        "type": "2",
        "ivr_id": IVR_IDS[0],
        "number": 8095100817,
        "public_ivr_id": "5f880780229a4287",
        "number_cc": 91,
        "call_expiry": "1",
        "misscall_expiry": "1",
        "company_display_number": "919870140141",
        "secret_token": "3203a0ac4e09057d40f721f1f1f0f5b5d0bb868a9fb5aa4c320f078a645ca9ea",
        "obd_v2_settings": {"shared_backup": 1, "failover": 1},
        "reference_id": "47919256",
        "request_id": "579c1142-b006-11ec-b0f9-164f4aa09487",
        "request_time": "2022-03-30 08:49:42",
        "exc_info": "NoneType: None",
        "dial_reverse": 0,
    },
    {
        "is_successfully_validated": 1,
        "company_id": "5a8faa138a57f584",
        "type": "2",
        "ivr_id": IVR_IDS[1],
        "number": 7499694363,
        "public_ivr_id": "5f880780229a4287",
        "number_cc": 91,
        "call_expiry": "1",
        "misscall_expiry": "1",
        "company_display_number": "919870140141",
        "secret_token": "3203a0ac4e09057d40f721f1f1f0f5b5d0bb868a9fb5aa4c320f078a645ca9ea",
        "obd_v2_settings": {"shared_backup": 1, "failover": 1},
        "reference_id": "36037607",
        "request_id": "573834f6-b006-11ec-b0f9-164f4aa09487",
        "request_time": "2022-03-30 08:49:42",
        "exc_info": "NoneType: None",
        "dial_reverse": 0,
    },
]

COMMON_KAM_GROUP_ID = 37

""" Pop the number_data and  groupivrinforelationship_data field before using
this, The above mentioned fields will be used to create the records
in Number and GroupIvrInfoRelationship with the group.
"""
DUMMY_GROUPS_OBJ_DATA = [
    {
        "name": "test-group",
        "region": "GURUGRAM",
        "group_alias": "test-group-alias",
        "group_priority": 1,
        "assigned_queues": "{'p_q': {'name': 'obd-central-group-queues_p_q_37', 'url': 'https://ap-southeast-1.queue.amazonaws.com/************/obd-central-group-queues_p_q_37', 'threshold': 60}, 'np_q': {'name': 'obd-central-group-queues_np_q_37', 'url': 'https://ap-southeast-1.queue.amazonaws.com/************/obd-central-group-queues_np_q_37', 'threshold': 30}}",
        "kam_group_id": COMMON_KAM_GROUP_ID,
        "settings": '{"is_obd":"1","c_id":"XYZ","d_id":"SIP","operator":"SIP_AIRTEL","caller_id":"122","last_digit":"04"}',
        "is_default": 1,
        "number_data": {
            "number": "12344567890",
            "number_priority": 1,
        },
        "groupivrinforelationship_data": {"ivr_info_ivr_id": IVR_IDS[0]},
    }
]

DUMMY_GROUP_DATA = {
    "url": "https://ap-southeast-1.queue.amazonaws.com/************/obd-central-group-queues_p_q_37",
    "name": "some_name",
    "ivr_settings": IVR_INFO_DATA[0]["common_setting"],
    "ivr_type": IVR_INFO_DATA[0]["ivr_type"],
    "source_number": "1234567890",
    "group_settings": "",
}


DUMMY_SUCCESS_UDC_RESPONSE = {
    "status": "success",
    "code": 200,
    "message": "",
    "data": {
        "is_channel_avail": 1,
        "channel_data": 20,
        "is_user_avail": 1,
        "user_data": {
            "uuid": "614ad7c391f86637",
            "company": "5fe46eecc7e1c148",
            "is_anon": False,
            "name": "Farha Npt",
            "extension": 25,
            "is_active": 4,
            "is_enabled": 1,
            "contact": "9354922907",
            "contact_country": "+91",
            "contact_type": "mobile",
            "contact_2": None,
            "contact_2_country": None,
            "contact_type_2": None,
            "time_zone": "+05:30",
        },
    },
}


DUMMY_UDC_USER_LOCKED_RESPONSE = {
    "status": "911",
    "message": "user_id is locked, cant check for udc availability",
}

DUMMY_UDC_IVR_LOCKED_RESPONSE = {
    "status": "912",
    "message": "ivr_id is locked, cant check for udc availability",
}
