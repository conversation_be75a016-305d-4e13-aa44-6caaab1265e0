import json
from handlers.models import CentralRequestInfo
from test_suite.test.data.common_data import DUMMY_REQUESTS, IVR_INFO_DATA, SQS_QUEUE_INFO_DATA
from central.settings.handlers import conf
from copy import deepcopy

RESPONSE_MANAGER_IVR_INFO_DATA =IVR_INFO_DATA
RESPONSE_MANAGER_DUMMY_REQUESTS = DUMMY_REQUESTS

RESPONSE_MANAGER_SQS_QUEUE_INFO_DATA = SQS_QUEUE_INFO_DATA + [
    {
        'gateway_prefix': conf.RESPONSE_MANAGER_GATEWAY_PREFIX,
        'queue_url': f'https://ap-southeast-1.queue.amazonaws.com/795722550498/{conf.RESPONSE_MANAGER_QUEUE}',
        'queue_name': conf.RESPONSE_MANAGER_QUEUE
    },
]


RESPONSE_MANAGER_QUEUE_DATA_DUMMY_FORMAT = {
        "_ai": "627121212af8993",
        "_so": "1",
        "_ci": "60e121212127e46d119",
        "_cr": "1111111111",
        "_cm": "",
        "_cl": "+911111111111",
        "_cy": "91",
        "_se": "DL, IN",
        "_ts": 1651828245,
        "_st": 1651828216,
        "_ms": 1651828216000,
        "_ss": 33016,
        "_et": 165212245,
        "_dr": "00:00:29",
        "_drm": 0.48,
        "_ty": 1,
        "_ev": 2,
        "_fn": "",
        "_fu": "",
        "_ns": "6",
        "_su": 2,
        "_dn": "",
        "_di": "",
        "_pm": [
            {
                "ky": "ui",
                "vl": "cb2.12121216.8331287"
            },
            {
                "ky": "is",
                "vl": "0"
            },
            {
                "ky": "vt",
                "vl": "2"
            },
            {
                "ky": "ic",
                "vl": "0"
            },
            {
                "ky": "ia",
                "vl": "0"
            },
            {
                "ky": "ib",
                "vl": "0"
            }
        ],
        "_cn": [

        ],
        "_ld":[
            {
                "_an": False,
                "_rst": "2022-05-06 09:10:16",
                "_ds": "",
                "_did": " 91121212121`2",
                "_rr": [

                ],
                "_tt":[

                ],
                "_su":"1",
                "_st":1651828245,
                "_et":1651828245,
                "_dr":"00:00:00",
                "_ac":"missed"
            }
        ],
        "_an":0,
        "_bp":[
            {
                "_tm": [
                    {
                        "type": "local",
                        "use": 1
                    }
                ],
                "_ti": [
                    {
                        "type": "local",
                        "use": 0
                    }
                ],
                "_ta": [
                    {
                        "type": "local",
                        "use": 0
                    }
                ],
                "_tr": [
                    {
                        "type": "local",
                        "use": 0
                    }
                ]
            }
        ],
        "_us": [

        ],
        "_tc":[

        ],
        "request_id":"568fc82e-cd1c-11ec-af70-22ed08b4609a",
        "_ji":"",
        "ivr_id":"60e724bc1d238909",
        "_cri":""
    }


# Dynamically setting up data
RESPONSE_MANAGER_QUEUE_DATA = []

for req in RESPONSE_MANAGER_DUMMY_REQUESTS:
    request_id = req['request_id']
    ivr_id = req['ivr_id']
    data = {}
    body =  deepcopy(RESPONSE_MANAGER_QUEUE_DATA_DUMMY_FORMAT)
    body.update(
        {
            'ivr_id': ivr_id,
            'request_id': request_id,
            'event_response': CentralRequestInfo.RESPONDED
        }
    )

    data['Body'] = json.dumps(body)
    data['ReceiptHandle'] = "some_receipt_handler"

    RESPONSE_MANAGER_QUEUE_DATA.append(data)
