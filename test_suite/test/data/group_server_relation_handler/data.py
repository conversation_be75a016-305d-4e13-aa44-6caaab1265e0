from handlers.models import Group, Server
from copy import deepcopy


kam_group_id: str = '100'
group_name: str = 'DELHI-VPS-AI-CO'
group_alias: str = 'DELHI-VPS-AI-CO'
region: str = 'Delhi'
c_id: str = 'DELHI-TA-MP'
d_id: str = 'MYOP'
operator: str = 'TA_VPS'
caller_id: str = '11345490'
last_digit: str = '9'
servers: list = ['d1.vt.info', 'd2.vt.info', 'd3.vt.info', 'd4.vt.info', 'd5.vt.info']


settings_dict: dict = {"c_id": "NOIDA-VPS-AI-CO", "d_id": "SIP", "operator": "VPS_Airtel", "caller_id": "12043850"}


assigned_queues: dict = {
    'p_q': {
        'name': 'gateway_p_q_name',
        'url': 'https://ap-south-1.queue.amazonaws.com/472952060482/gateway_p_q-name',
        'threshold': 10
    },
    
    'np_q': {
        'name': 'gateway_np_q-name',
        'url': 'https://ap-south-1.queue.amazonaws.com/472952060482/gateway_np_q-name',
        'threshold': 30
    }
}


group_data: dict = {
    'name': group_name,
    'group_alias': group_alias,
    'region': region,
    'c_id': c_id, 
    'd_id': d_id,
    'operator': operator,
    'caller_id': caller_id,
    'servers': servers,
    'last_digit': last_digit
}


group_data_from_kam_api: dict = {
    kam_group_id: deepcopy(group_data)
}


def get_kam_group_server_list_api_response():
    active_group_ids: list = list(Group.objects.all().values_list('id', flat=True))
    active_server_names: list = list(Server.objects.all().values_list('name', flat=True))
    
    server_len: int = len(active_server_names)
    
    inactive_kam_server_names: list = active_server_names[server_len//2:]
    active_kam_server_names: list = active_server_names[:(server_len - (server_len // 2) - 1)]
    
    response: dict = {
        'groups': active_group_ids,
        'servers': active_kam_server_names,
        'inactive_servers': inactive_kam_server_names  # not a part of api response, just added for checking/verifying server disabled or not
    }
    return response


kam_group_id_for_already_case: str = '54'
group_data_for_already_grp_exists_case: dict = {
    'name': 'TCS-TA-UP',
    'group_alias': 'TCS-TA-UP',
    'region': 'Gujrat',
    'assigned_queues': deepcopy(assigned_queues),
    'is_enable': Group.YES,
    'is_default': Group.NO,
    'kam_group_id': kam_group_id_for_already_case,
    'settings': deepcopy(settings_dict)
}


server_name_for_already_exists_case: str = 'g1.vt.info'


group_data_2: dict = deepcopy(group_data)
group_data_2['name'] = 'TCS-TA-UP'
group_data_2['group_alias'] = 'TCS-TA-UP'
group_data_2['region'] = 'Gujrat'
group_data_2['caller_id'] = '11345491'
group_data_2['last_digit'] = '2'


group_data_from_kam_api_already_exists_case: dict = {
    kam_group_id_for_already_case: group_data_2
}
