from test_suite.test.data.common_data import IVR_IDS
from central.settings.handlers import conf
from django.utils import timezone
from datetime import timedelta
from copy import deepcopy
import uuid


# CentralRequestInfo model raw data
cri_data = {
	'request_id': '',
    'ivr_id': '',
    'c_id': '',
    'user_id': '',
    'request_type': 1,
    'raw_data': None,
	'is_req_completed': 0,
    'is_onhold_req': 0,
    'is_enable': 1,
	'completion_event_type': 0,
	'event_response': 0,
	'onhold_udc_check_availibilty_cnt': 0,
	'iteration': 1,
	'expired_at': timezone.now() + timedelta(seconds=conf.DEFAULT_CALL_EXPIRY),
	'added_on': ''
}


def get_added_on(threshold: dict, days: int):
    """ getting added_on date for CentralRequestInfo records
    
    Args:
        threshold (dict): response of `Helper.get_threshold` method
        days (int): 
        
    Returns:
        _type_: _description_
    """
    
    assert isinstance(days, int), 'days must be in int'
    assert isinstance(threshold, dict), 'threshold must be in dict'
    
    added_on = timezone.now() + timedelta(days=threshold[conf.DATA_KEEP_DAYS] + days)
    return added_on


def get_cri_data(threshold: dict, no_of_records: int = 10):
    """ making CentralRequestInfo records
    
    Args:
        threshold (dict): response of `Helper.get_threshold` method
        no_of_records (int, optional): number of records to create, default value is 10
        
    Returns:
        _type_: _description_
    """
    
    cri_records: list = []
    for _ in range(no_of_records):
        cri_rec: dict = deepcopy(cri_data)
        
        cri_rec['request_id'] = str(uuid.uuid4())
        cri_rec['ivr_id'] = str(uuid.uuid4()).replace('-', '')[:16]
        cri_rec['c_id'] = str(uuid.uuid4()).replace('-', '')[:16]
        cri_rec['user_id'] = str(uuid.uuid4())
        cri_rec['added_on'] = get_added_on(threshold, _ + 1)
        
        cri_records.append(cri_rec)
    return cri_records
