import json

from handlers.models import Number
from test_suite.test.data.common_data import (
    COMMON_KAM_GROUP_ID,
    DUMMY_GROUP_DATA,
    IVR_IDS,
    IVR_INFO_DATA,
    SQS_QUEUE_INFO_DATA,
)


def get_assigned_queues(data):
    return json.loads(data["assigned_queues"].replace("'", '"'))


CALLER_ID_FILTER_NUMBER = "**********"
REGION_FILTER_NAME = "DELHI"
GROUP_FILTER_ALIAS = "test-2-group-alias"
SQS_QUEUE_INFO_DATA = SQS_QUEUE_INFO_DATA
UTILS_IVR_INFO_DATA = IVR_INFO_DATA


GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_1_DATA = {
    "name": "test-group-1",
    "region": "GURUGRAM",
    "group_alias": "test-group-alias-1",
    "group_priority": 1,
    "assigned_queues": json.dumps(
        {
            "p_q": {
                "name": "obd-central-group-queues_p_q_37",
                "url": "https://ap-southeast/obd-central-group-queues_p_q_37",
                "threshold": 60,
            },
            "np_q": {
                "name": "obd-central-group-queues_np_q_37",
                "url": "https://ap-southeast/obd-central-group-queues_np_q_37",
                "threshold": 30,
            },
        }
    ),
    "kam_group_id": COMMON_KAM_GROUP_ID,
    "settings": json.dumps(
        {
            "is_obd": "1",
            "c_id": "XYZ",
            "d_id": "SIP",
            "operator": "SIP_AIRTEL",
            "caller_id": "122",
            "last_digit": "04",
        }
    ),
    "is_default": 0,
    "number_data": {
        "number": CALLER_ID_FILTER_NUMBER,
        "number_priority": 1,
        "is_fix_did": Number.YES,
    },
    "groupivrinforelationship_data": {"ivr_info_ivr_id": IVR_IDS[0]},
}


GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_2_DATA = {
    "name": "test-group-2",
    "region": REGION_FILTER_NAME,
    "group_alias": "test-group-alias-2",
    "group_priority": 1,
    "assigned_queues": json.dumps(
        {
            "p_q": {
                "name": "obd-central-group-queues_p_q_38",
                "url": "https://ap-southeast/obd-central-group-queues_p_q_38",
                "threshold": 60,
            },
            "np_q": {
                "name": "obd-central-group-queues_np_q_38",
                "url": "https://ap-southeast/obd-central-group-queues_np_q_38",
                "threshold": 30,
            },
        }
    ),
    "kam_group_id": 38,
    "settings": json.dumps(
        {
            "is_obd": "1",
            "c_id": "XYZ",
            "d_id": "SIP",
            "operator": "SIP_AIRTEL",
            "caller_id": "122",
            "last_digit": "04",
        }
    ),
    "is_default": 0,
    "number_data": {
        "number": "********",
        "number_priority": 1,
        "is_fix_did": Number.YES,
    },
    "groupivrinforelationship_data": {"ivr_info_ivr_id": IVR_IDS[0]},
}


GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_3_DATA = {
    "name": "test-group-3",
    "region": "NOIDA",
    "group_alias": GROUP_FILTER_ALIAS,
    "group_priority": 1,
    "assigned_queues": json.dumps(
        {
            "p_q": {
                "name": "obd-central-group-queues_p_q_39",
                "url": "https://ap-southeast/obd-central-group-queues_p_q_39",
                "threshold": 60,
            },
            "np_q": {
                "name": "obd-central-group-queues_np_q_39",
                "url": "https://ap-southeast/obd-central-group-queues_np_q_39",
                "threshold": 39,
            },
        }
    ),
    "kam_group_id": 39,
    "settings": json.dumps(
        {
            "is_obd": "1",
            "c_id": "XYZ",
            "d_id": "SIP",
            "operator": "SIP_VODAFONE",
            "caller_id": "122",
            "last_digit": "04",
        }
    ),
    "is_default": 0,
    "number_data": {
        "number": "**********",
        "number_priority": 1,
        "is_fix_did": Number.YES,
    },
    "groupivrinforelationship_data": {"ivr_info_ivr_id": IVR_IDS[0]},
}


GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FAILOVER_DATA = {
    "name": "test-group-4",
    "region": "NOIDA",
    "group_alias": "test-group-alias-4",
    "group_priority": 1,
    "assigned_queues": json.dumps(
        {
            "p_q": {
                "name": "obd-central-group-queues_p_q_40",
                "url": "https://ap-southeast/obd-central-group-queues_p_q_40",
                "threshold": 60,
            },
            "np_q": {
                "name": "obd-central-group-queues_np_q_40",
                "url": "https://ap-southeast/obd-central-group-queues_np_q_40",
                "threshold": 39,
            },
        }
    ),
    "kam_group_id": 40,
    "settings": json.dumps(
        {
            "is_obd": "1",
            "c_id": "XYZ",
            "d_id": "SIP",
            "operator": "SIP_VODAFONE",
            "caller_id": "122",
            "last_digit": "04",
        }
    ),
    "is_default": 0,
    "number_data": {
        "number": "**********",
        "number_priority": 1,
        "is_fix_did": Number.YES,
    },
    "groupivrinforelationship_data": {"ivr_info_ivr_id": IVR_IDS[1]},
}


GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_SHARED_BACKUP_DATA = {
    "name": "test-group-5",
    "region": "NOIDA",
    "group_alias": "test-group-alias-5",
    "group_priority": 1,
    "assigned_queues": json.dumps(
        {
            "p_q": {
                "name": "obd-central-group-queues_p_q_41",
                "url": "https://ap-southeast/obd-central-group-queues_p_q_41",
                "threshold": 60,
            },
            "np_q": {
                "name": "obd-central-group-queues_np_q_41",
                "url": "https://ap-southeast/obd-central-group-queues_np_q_41",
                "threshold": 39,
            },
        }
    ),
    "kam_group_id": 41,
    "settings": json.dumps(
        {
            "is_obd": "1",
            "c_id": "XYZ",
            "d_id": "SIP",
            "operator": "SIP_VODAFONE",
            "caller_id": "122",
            "last_digit": "04",
        }
    ),
    "is_default": 1,
    "number_data": {
        "number": "**********",
        "number_priority": 1,
        "is_fix_did": Number.NO,
    },
}


GQ_ROUTING_MANAGER_FILTER_1_DUMMY_REQUESTS = {
    "is_successfully_validated": 1,
    "company_id": "60a392bbe3d97152",
    "type": "1",
    "ivr_id": IVR_IDS[0],
    "number": **********,
    "public_ivr_id": "60a49c4c07ba8419",
    "number_cc": 91,
    "call_expiry": "1",
    "misscall_expiry": "1",
    "company_display_number": "************",
    "secret_token": "953e784b7d4251dac5c4723e",
    "obd_v2_settings": {"shared_backup": 0, "failover": 0},
    "user_id": "61e66e986558c735",
    "reference_id": "a1580c8a-7f47-430d-9e96-5a6cefadbefd",
    "request_id": "5bd61b2c-b006-11ec-b0f9-164f4aa09487",
    "request_time": "2022-03-30 08:49:49",
    "exc_info": "NoneType: None",
    "dial_reverse": 1,
    "caller_id": CALLER_ID_FILTER_NUMBER,
}


GQ_ROUTING_MANAGER_FILTER_2_DUMMY_REQUESTS = {
    "is_successfully_validated": 1,
    "company_id": "60a392bbe3d97152",
    "type": "1",
    "ivr_id": IVR_IDS[0],
    "number": **********,
    "public_ivr_id": "60a49c4c07ba8419",
    "number_cc": 91,
    "call_expiry": "1",
    "misscall_expiry": "1",
    "company_display_number": "************",
    "secret_token": "953e784b7d4251dac5c472c5e",
    "obd_v2_settings": {"shared_backup": 0, "failover": 0},
    "user_id": "61e66e986558c735",
    "reference_id": "a1580c8a-7f47-430d-9e96-5a2mefadbefd",
    "request_id": "5bd61b2c-b006-11ec-b0f9-204f4aa09487",
    "request_time": "2022-03-30 08:49:49",
    "exc_info": "NoneType: None",
    "dial_reverse": 1,
    "region": REGION_FILTER_NAME,
}


GQ_ROUTING_MANAGER_FILTER_3_DUMMY_REQUESTS = {
    "is_successfully_validated": 1,
    "company_id": "60a392bbe3d97152",
    "type": "1",
    "ivr_id": IVR_IDS[0],
    "number": **********,
    "public_ivr_id": "60a49c4c07ba8419",
    "number_cc": 91,
    "call_expiry": "1",
    "misscall_expiry": "1",
    "company_display_number": "************",
    "secret_token": "953e784b7d4251dac5c4723fb1193c6c5e",
    "obd_v2_settings": {"shared_backup": 0, "failover": 0},
    "user_id": "61e66e986558c735",
    "reference_id": "a1580c8a-7f47-430d-9e96-5a3mefadbefd",
    "request_id": "5bd61b2c-b006-11ec-b0f9-214f4aa09487",
    "request_time": "2022-03-30 08:49:49",
    "exc_info": "NoneType: None",
    "dial_reverse": 1,
    "group": GROUP_FILTER_ALIAS,
}


GQ_ROUTING_MANAGER_FAILOVER_DUMMY_REQUESTS = {
    "is_successfully_validated": 1,
    "company_id": "60a392bbe3d97152",
    "type": "1",
    "ivr_id": IVR_IDS[1],
    "number": **********,
    "public_ivr_id": "60a49c1207ba8419",
    "number_cc": 91,
    "call_expiry": "1",
    "misscall_expiry": "1",
    "company_display_number": "************",
    "secret_token": "953e784b7d4251dac5c4722fb1193c6c5e",
    "obd_v2_settings": {"shared_backup": 0, "failover": 1},
    "user_id": "61e66e986558c735",
    "reference_id": "a1580c8a-7f47-430d-9e96-5a4mefadbefd",
    "request_id": "5bd61b2c-b006-11ec-b0f9-224f4aa09487",
    "request_time": "2022-03-30 08:49:49",
    "exc_info": "NoneType: None",
    "dial_reverse": 1,
}


GQ_ROUTING_MANAGER_SHARED_BACKUP_DUMMY_REQUESTS = {
    "is_successfully_validated": 1,
    "company_id": "60a392bbe3d97152",
    "type": "1",
    "ivr_id": IVR_IDS[2],
    "number": **********,
    "public_ivr_id": "60a49c1207ba8419",
    "number_cc": 91,
    "call_expiry": "1",
    "misscall_expiry": "1",
    "company_display_number": "************",
    "secret_token": "953e784b7d4251e27792fb1193c6c5e",
    "obd_v2_settings": {"shared_backup": 1, "failover": 1},
    "user_id": "61e66e986558c735",
    "reference_id": "a1580c8a-7f47-430d-9e96-5a5mefadbefd",
    "request_id": "5bd61b2c-b006-11ec-b0f9-234f4aa09487",
    "request_time": "2022-03-30 08:49:49",
    "exc_info": "NoneType: None",
    "dial_reverse": 1,
}


GQ_ROUTING_MANAGER_FILTER_1_EXPECTED_RESPONSE = {
    "url": get_assigned_queues(
        GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_1_DATA
    )["p_q"]["url"],
    "name": get_assigned_queues(
        GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_1_DATA
    )["p_q"]["name"],
    "ivr_settings": IVR_INFO_DATA[0]["common_setting"],
    "ivr_type": IVR_INFO_DATA[0]["ivr_type"],
    "source_number": GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_1_DATA[
        "number_data"
    ]["number"],
    "group_settings": GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_1_DATA[
        "settings"
    ],
}


GQ_ROUTING_MANAGER_FILTER_2_EXPECTED_RESPONSE = {
    "url": get_assigned_queues(
        GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_2_DATA
    )["p_q"]["url"],
    "name": get_assigned_queues(
        GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_2_DATA
    )["p_q"]["name"],
    "ivr_settings": IVR_INFO_DATA[0]["common_setting"],
    "ivr_type": IVR_INFO_DATA[0]["ivr_type"],
    "source_number": GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_2_DATA[
        "number_data"
    ]["number"],
    "group_settings": GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_2_DATA[
        "settings"
    ],
}


GQ_ROUTING_MANAGER_FILTER_3_EXPECTED_RESPONSE = {
    "url": get_assigned_queues(
        GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_3_DATA
    )["p_q"]["url"],
    "name": get_assigned_queues(
        GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_3_DATA
    )["p_q"]["name"],
    "ivr_settings": IVR_INFO_DATA[0]["common_setting"],
    "ivr_type": IVR_INFO_DATA[0]["ivr_type"],
    "source_number": GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_3_DATA[
        "number_data"
    ]["number"],
    "group_settings": GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FILTER_3_DATA[
        "settings"
    ],
}


GQ_ROUTING_MANAGER_FAILOVER_EXPECTED_RESPONSE = {
    "url": get_assigned_queues(
        GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FAILOVER_DATA
    )["p_q"]["url"],
    "name": get_assigned_queues(
        GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FAILOVER_DATA
    )["p_q"]["name"],
    "ivr_settings": IVR_INFO_DATA[1]["common_setting"],
    "ivr_type": IVR_INFO_DATA[1]["ivr_type"],
    "source_number": GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FAILOVER_DATA[
        "number_data"
    ]["number"],
    "group_settings": GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_FAILOVER_DATA[
        "settings"
    ],
}


GQ_ROUTING_MANAGER_SHARED_BACKUP_EXPECTED_RESPONSE = {
    "url": get_assigned_queues(
        GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_SHARED_BACKUP_DATA
    )["p_q"]["url"],
    "name": get_assigned_queues(
        GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_SHARED_BACKUP_DATA
    )["p_q"]["name"],
    "ivr_settings": IVR_INFO_DATA[2]["common_setting"],
    "ivr_type": IVR_INFO_DATA[2]["ivr_type"],
    "source_number": GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_SHARED_BACKUP_DATA[
        "number_data"
    ]["number"],
    "group_settings": GQ_ROUTING_MANAGER_DUMMY_GROUPS_OBJ_SHARED_BACKUP_DATA[
        "settings"
    ],
}

EXTERNAL_API_UDC_CHANNEL_SUCCESS_RESPONSE = {
    "status": "success",
    "code": 200,
    "message": "",
    "data": {"is_channel_avail": 1, "channel_data": 24},
}

EXTERNAL_API_UDC_DC_API_SUCCESS_RESPONSE = {
    "status": "success",
    "code": 200,
    "message": "",
    "data": {
        "is_channel_avail": 1,
        "is_department_avail": 1,
        "channel_data": 4,
        "multi_department": 0,
        "user_data": {"is_enabled": 1},
    },
}

EXTERNAL_API_UDC_USER_VIEW_ONLY_ENABLED_SUCCESS_RESPONSE = {
    "status": "success",
    "code": 200,
    "message": "",
    "data": {
        "is_channel_avail": 1,
        "channel_data": 20,
        "is_user_avail": 1,
        "user_data": {
            "uuid": "620a0a047db39645",
            "company": "5c136e0fe2014464",
            "is_anon": False,
            "name": "Mohammad Shahid",
            "extension": 29,
            "is_active": 1,
            "is_enabled": 1,
            "contact": "1212121212",
            "contact_country": "+91",
            "contact_type": "mobile",
            "contact_2": None,
            "contact_2_country": None,
            "contact_type_2": None,
            "time_zone": "+05:30",
        },
    },
}


EXTERNAL_API_UDC_TYPE_1_ANON_UUID_REQUEST = {
    "is_successfully_validated": 1,
    "company_id": "5dcbb859f3b34915",
    "type": "1",
    "ivr_id": IVR_IDS[0],
    "number": 1212121212,
    "public_ivr_id": "5f9awc40313e35f909",
    "number_cc": 91,
    "call_expiry": "1",
    "misscall_expiry": "1",
    "company_display_number": "************",
    "secret_token": "4715716a17ae3125baa1613cd1b2501b67",
    "obd_v2_settings": {"shared_backup": 1, "failover": 1},
    "number_2": 2323232323,
    "number_2_cc": 91,
    "anon_uuid": "5ddb996b5d894519",
    "reference_id": "************@m%20%2022-04-12T05:42:44.227Z",
    "request_id": "603c7fa4-ba23-11ec-88a6-8aa731b45d0b",
    "request_time": "2022-04-12 05:42:44",
    "exc_info": "NoneType: None",
    "dial_reverse": 1,
    "source_number": "3434343434",
    "ivr_type": 2,
}

EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST = {
    "is_successfully_validated": 1,
    "company_id": "5f3cd6ccfd637300",
    "type": "1",
    "ivr_id": IVR_IDS[0],
    "number": 1212121212,
    "public_ivr_id": "5f9awc40313e35f909",
    "number_cc": 91,
    "call_expiry": "1",
    "misscall_expiry": "1",
    "company_display_number": "************",
    "secret_token": "ebb3bf342e632558fbe2269a3487d206",
    "obd_v2_settings": {"shared_backup": 1, "failover": 1},
    "user_id": "5fa25212906d3804",
    "reference_id": "14142642_13095381_0_1649744856",
    "request_id": "a4c5ee5c-ba29-11ec-9bae-ea5d67d962f0",
    "request_time": "2022-04-12 06:27:36",
    "exc_info": "NoneType: None",
    "dial_reverse": 1,
}
EXTERNAL_API_UDC_TYPE_2_DC_API_REQUEST = {
    "is_successfully_validated": 1,
    "company_id": "5a8faa121a57f584",
    "type": "2",
    "ivr_id": IVR_IDS[0],
    "number": 8197777275,
    "public_ivr_id": "5f8801210229a4287",
    "number_cc": 91,
    "call_expiry": "1",
    "misscall_expiry": "1",
    "company_display_number": "91121212211",
    "secret_token": "3203a0ac4e09057d40f721c320f078a645ca9ea",
    "obd_v2_settings": {"shared_backup": 1, "failover": 1},
    "reference_id": "20220628",
    "request_id": "f4ebecc2-ba3a-11ec-bea6-7291be14d301",
    "request_time": "2022-04-12 08:31:32",
    "exc_info": "NoneType: None",
    "dial_reverse": 0,
}


EXTERNAL_API_UDC_METHOD_TYPE_1_ANON_UUID_RESPONSE = {
    "contact_country": EXTERNAL_API_UDC_TYPE_1_ANON_UUID_REQUEST[
        "number_2_cc"
    ],
    "contact": EXTERNAL_API_UDC_TYPE_1_ANON_UUID_REQUEST["number_2"],
}


EXTERNAL_API_UDC_METHOD_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_RESPONSE = (
    EXTERNAL_API_UDC_USER_VIEW_ONLY_ENABLED_SUCCESS_RESPONSE["data"]
)

EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_USER_LOCKED_RESPONSE = {
    "status": "911",
    "message": "user_id: {user_id} is locked, cant check for udc availability".format(
        user_id=EXTERNAL_API_UDC_TYPE_1_USER_ID_VIEW_ONLY_ENABLED_REQUEST[
            "user_id"
        ]
    ),
}

EXTERNAL_API_UDC_METHOD_DC_API_RESPONSE = (
    EXTERNAL_API_UDC_DC_API_SUCCESS_RESPONSE["data"]
)

EXTERNAL_API_UDC_METHOD_CHANNEL_API_RESPONSE = (
    EXTERNAL_API_UDC_CHANNEL_SUCCESS_RESPONSE["data"]
)

GQ_PUSHER_DUMMY_GROUP_DATA = DUMMY_GROUP_DATA


SQS_MANAGER_QUEUE_NAME = "obd_v2_testing_environment"
SQS_MANAGER_QUEUE_URL = "https://ap-southeast-1obd_v2_testing_environment"
