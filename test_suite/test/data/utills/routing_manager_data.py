from test_suite.test.data.data_deletion_handler.data import cri_data
from central.settings.handlers import conf

from copy import deepcopy
import json
import uuid


# create cri data
def get_cri_data(num_of_rec: int = 1):
    cri_records: list = []
    
    for _ in range(num_of_rec):
        cri_rec: dict = deepcopy(cri_data)
        
        cri_rec['request_id'] = str(uuid.uuid4())
        cri_rec['ivr_id'] = str(uuid.uuid4()).replace('-', '')[:16]
        cri_rec['c_id'] = str(uuid.uuid4()).replace('-', '')[:16]
        cri_rec['user_id'] = str(uuid.uuid4())
        cri_rec['raw_data'] = json.dumps({'key1': 'value1'})
        
        cri_records.append(cri_rec)
    return cri_records


# ###################################### get_routing_info #########################################
base_data: dict = {
    "detail": [
        {
            "name": "kam_group_servers",
            "method": "POST",
            "data": {
                "body": {}, 
                "headers": {}, 
                "params": {},
            },
            "is_external": 1,
            "ip_url": None,
            "relative_url": "http://kam-mumbai.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_servers",
            "url": "http://kam-mumbai.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_servers"
        }
    ]
}


# dynamic_api_call_response for dynamic_args
def get_dynamic_api_call_response():
    response: dict = deepcopy(base_data)
    data: dict = response['detail'][0]['data']
    data['dynamic_args'] = {"ivr_id": 'ivr_id', "company_id": 'company_id'}
    data['body'] = {'token': 'token', 'kam_group_id': 'kam_group_id', 'is_obd': 'is_obd'}
    response['detail'][0]['data'] = json.dumps(data)
    return response


# dynamic_api_call_response for GET method
def get_dynamic_api_call_response_get_method():
    response: dict = deepcopy(base_data)
    response['detail'][0]['data']['params'] = {'request_status': 'request_status'}
    response['detail'][0]['data'] = json.dumps(response['detail'][0]['data'])
    response['detail'][0]['method'] = 'GET'
    return response


# dynamic_api_call_response for POST method
def get_dynamic_api_call_response_post_method():
    response: dict = deepcopy(base_data)
    response['detail'][0]['data']['body'] = {'token': 'token', 'kam_group_id': 'kam_group_id', 'is_obd': 'is_obd'}
    response['detail'][0]['data'] = json.dumps(response['detail'][0]['data'])
    response['detail'][0]['method'] = 'POST'
    return response


def expected_response_of_dynamic_api_call_dynamic_args():
    data = {
        "name": "kam_group_servers",
        "method": "POST",
        "data": {
            'token': conf.SECRET_TOKENS['kam_group_servers_token'], 
            'kam_group_id': '', 
            'is_obd': '1',
        },
        "headers": {},
        "is_external": 1,
        "ip_url": None,
        "relative_url": "http://kam-mumbai.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_servers",
        "url": "http://kam-mumbai.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_servers"
    }
    return data


def expected_response_of_dynamic_api_call_get_method():
    data = {
        "name": "kam_group_servers",
        "method": "GET",
        "data": {'request_status': 'request_status'},
        "headers": {},
        "is_external": 1,
        "ip_url": None,
        "relative_url": "http://kam-mumbai.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_servers",
        "url": "http://kam-mumbai.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_servers"
    }
    return data


def expected_response_of_dynamic_api_call_post_method():
    data = {
        "name": "kam_group_servers",
        "method": "POST",
        "data": {
            'token': conf.SECRET_TOKENS['kam_group_servers_token'], 
            'kam_group_id': '', 
            'is_obd': '1',
        },
        "headers": {},
        "is_external": 1,
        "ip_url": None,
        "relative_url": "http://kam-mumbai.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_servers",
        "url": "http://kam-mumbai.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_servers"
    }
    return data


# ###################################### call_routing_info #########################################
def expected_call_routing_info_response():
    return get_dynamic_api_call_response_post_method()['detail'][0]


def call_routing_info_called_with():
    routing_info_data_pack = deepcopy(conf.ROUTING_INFO_API_DATA)
    routing_info_data_pack['data']['params']['name'] = 'kam_group_servers'
    routing_info_data_pack['headers'] = routing_info_data_pack['data']['headers']
    routing_info_data_pack['data'] = routing_info_data_pack['data']['params']
    return routing_info_data_pack


# ######################################### dynamic_api_call #######################################
def get_dynamic_api_call_data(name: str, method: str = 'GET'):
    api_call_data = deepcopy(conf.ROUTING_INFO_API_DATA)
    
    api_call_data['headers'] = api_call_data['data']['headers']
    api_call_data['data']['params']['name'] = name
    api_call_data['data'] = api_call_data['data']['params']
    api_call_data['method'] = method.upper()
    
    return api_call_data


# ##################################### get_route_process_flow #####################################
def get_pf_routing_info_repponse():
    data = {
        "name": conf.ROUTING_INFO_URL_NAMES["process_flow_router_name"],
        "method": "GET",
        "data": {'company_id': 'c_id', 'iteration': 'iteration', 'ivr_id': 'ivr_id', 'source_name': 'source_name', 'event': 'event_response'},
        "headers": {},
        "is_external": 1,
        "ip_url": None,
        "relative_url": "http://kam-mumbai.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_servers",
        "url": "http://kam-mumbai.voicetree.biz/kamailio_api/src/Kamailio/index.php/group_servers"
    }
    
    return data


def get_pf_route_api_response():
    data: dict = {
        "detail": {
            "id": 0,
            "company_id": "string",
            "company_display_number": "string",
            "ivr_id": "string",
            "source_name": "string",
            "event": 0,
            "iteration": 0,
            "destination_name": "string"
        }
    }
    return data


# ############################# dynamic_target_service_queue_push ##################################
def expected_data_push_to_q(cri_instance, service_name, data: dict = None):
    data = data if data else {}
    data.update(json.loads(cri_instance.raw_data))
    data.update({conf.SERVICE_ROUTER_FLAG: service_name})
    return json.dumps(data)
