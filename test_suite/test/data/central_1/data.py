from test_suite.test.data.common_data import *



CENTRAL_1_IVRS = IVR_IDS 



SQS_QUEUE_INFO_DATA = SQS_QUEUE_INFO_DATA  + [
    {
        "gateway_prefix" : conf.CENTRAL_IVRQ_GATEWAY_PREFIX,
        "queue_url": f"https://ap-southeast-1.queue.amazonaws.com/795722550498/obd-central-ivrid-{CENTRAL_1_IVRS[1]}",
        "queue_name": f"obd-central-ivrid-{CENTRAL_1_IVRS[1]}"
    },
    {
        "gateway_prefix" : conf.CENTRAL_IVRQ_GATEWAY_PREFIX,
        "queue_url": f"https://ap-southeast-1.queue.amazonaws.com/795722550498/obd-central-ivrid-{CENTRAL_1_IVRS[2]}",
        "queue_name": f"obd-central-ivrid-{CENTRAL_1_IVRS[2]}"
    },
]


IVR_INFO_DATA = [*IVR_INFO_DATA]


PAUSED_IVR_IDS = [
    '55855d0edf19b391', '55855d0edf20b391'
]

EXCLUDED_IVR_IDS = [
    '55855d0edf21b391', '55855d0edf22b391'
]


CENTRAL_1_TEST_NEW_REQUEST_DATA = DUMMY_REQUESTS[0:4]


CENTRAL_1_TEST_EXPIRED_REQUEST_DATA = DUMMY_REQUESTS[4:6]
CENTRAL_1_TEST_IVR_TIMING_FAILED_REQUEST = DUMMY_REQUESTS[6:8]


CENTRAL_1_PROCESS_IVRS_REQUESTS = {


    f"https://ap-southeast-1.queue.amazonaws.com/795722550498/obd-central-ivrid-{CENTRAL_1_IVRS[0]}":[
        {
        'is_successfully_validated': 1,
        'company_id': '5a8faa138a57f584',
        'type': '2',
        'ivr_id': CENTRAL_1_IVRS[0],
        'number': 8095100817,
        'public_ivr_id': '5f880780229a4287',
        'number_cc': 91,
        'call_expiry': '1',
        'misscall_expiry': '1',
        'company_display_number': '919870140141',
        'secret_token': '3203a0ac4e09057d40f721f1f1f0f5b5d0bb868a9fb5aa4c320f078a645ca9ea',
        'obd_v2_settings': {
            'shared_backup': 1,
            'failover': 1
        },
        'reference_id': '47919256',
        'request_id': '579c1142-b006-11ec-b0f9-164f4aa09487',
        'request_time': '2022-03-30 08:49:42',
        'exc_info': 'NoneType: None',
        'dial_reverse': 0
    },
    {
        'is_successfully_validated': 1,
        'company_id': '5a8faa138a57f584',
        'type': '2',
        'ivr_id': CENTRAL_1_IVRS[0],
        'number': 7499694363,
        'public_ivr_id': '5f880780229a4287',
        'number_cc': 91,
        'call_expiry': '1',
        'misscall_expiry': '1',
        'company_display_number': '919870140141',
        'secret_token': '3203a0ac4e09057d40f721f1f1f0f5b5d0bb868a9fb5aa4c320f078a645ca9ea',
        'obd_v2_settings': {
            'shared_backup': 1,
            'failover': 1
        },
        'reference_id': '36037607',
        'request_id': '573834f6-b006-11ec-b0f9-164f4aa09487',
        'request_time': '2022-03-30 08:49:42',
        'exc_info': 'NoneType: None',
        'dial_reverse': 0
    },
    {
        'is_successfully_validated': 1,
        'company_id': '5a8faa138a57f584',
        'type': '2',
        'ivr_id': CENTRAL_1_IVRS[0],
        'number': 9850428972,
        'public_ivr_id': '5f880780229a4287',
        'number_cc': 91,
        'call_expiry': '1',
        'misscall_expiry': '1',
        'company_display_number': '919870140141',
        'secret_token': '3203a0ac4e09057d40f721f1f1f0f5b5d0bb868a9fb5aa4c320f078a645ca9ea',
        'obd_v2_settings': {
            'shared_backup': 1,
            'failover': 1
        },
        'reference_id': '47919265',
        'request_id': '5865520a-b006-11ec-b0f9-164f4aa09487',
        'request_time': '2022-03-30 08:49:44',
        'exc_info': 'NoneType: None',
        'dial_reverse': 0
    },
    ],
    f"https://ap-southeast-1.queue.amazonaws.com/795722550498/obd-central-ivrid-{CENTRAL_1_IVRS[1]}":[
    {
        'is_successfully_validated': 1,
        'company_id': '5a8faa138a57f584',
        'type': '2',
        'ivr_id': CENTRAL_1_IVRS[1],
        'number': 9890790925,
        'public_ivr_id': '5f880780239a4287',
        'number_cc': 91,
        'call_expiry': '1',
        'misscall_expiry': '1',
        'company_display_number': '919870140141',
        'secret_token': '3203a0ac4e09057d40f721f1f1f0f5b5d0bb868a9fb5aa4c320f078a645ca9ea',
        'obd_v2_settings': {
            'shared_backup': 1,
            'failover': 1
        },
        'reference_id': '40324318',
        'request_id': '5888dc34-b006-11ec-b0f9-164f4aa09487',
        'request_time': '2022-03-30 08:49:44',
        'exc_info': 'NoneType: None',
        'dial_reverse': 0
    },
    {
        'is_successfully_validated': 1,
        'company_id': '60a392bbe3d97152',
        'type': '1',
        'ivr_id': CENTRAL_1_IVRS[1],
        'number': 8788115705,
        'public_ivr_id': '5f880780239a4287',
        'number_cc': 91,
        'call_expiry': '1',
        'misscall_expiry': '1',
        'company_display_number': '919319368366',
        'secret_token': '953e784b7d4251dac5c47233ad6761e6cfa79d4eb953675e27792fb1193c6c5e',
        'obd_v2_settings': {
                'shared_backup': 1,
                'failover': 1
        },
        'user_id': '61bdc0b194510576',
        'reference_id': '804a99cb-9907-4de3-bf37-5a07246684a4',
        'request_id': '5a4b1ba4-b006-11ec-b0f9-164f4aa09487',
        'request_time': '2022-03-30 08:49:47',
        'exc_info': 'NoneType: None',
        'dial_reverse': 1
    },

    {
        'is_successfully_validated': 1,
        'company_id': '5fec32b6dbd3b453',
        'type': '1',
        'ivr_id': CENTRAL_1_IVRS[1],
        'number': 7307169744,
        'public_ivr_id': '5f880780239a4287',
        'number_cc': 91,
        'call_expiry': '1',
        'misscall_expiry': '1',
        'company_display_number': '917042882883',
        'secret_token': '579d9b7492e558b01f1023c191774cab23465b4d73f2437d8d38816ab3cdf18e',
        'obd_v2_settings': {
                'shared_backup': 1,
                'failover': 1
        },
        'user_id': '609607168ac73793',
        'reference_id': '11b2baae-bfd5-43c2-9212-aa7403df6a46',
        'request_id': '5931b688-b006-11ec-b0f9-164f4aa09487',
        'request_time': '2022-03-30 08:49:45',
        'exc_info': 'NoneType: None',
        'dial_reverse': 1
    },
    ],
    f"https://ap-southeast-1.queue.amazonaws.com/795722550498/obd-central-ivrid-{CENTRAL_1_IVRS[2]}":[

    {
        'is_successfully_validated': 1,
        'company_id': '60a392bbe3d97152',
        'type': '1',
        'ivr_id': CENTRAL_1_IVRS[2],
        'number': 7860985234,
        'public_ivr_id': '60a49c4c27ba8419',
        'number_cc': 91,
        'call_expiry': '1',
        'misscall_expiry': '1',
        'company_display_number': '919319368366',
        'secret_token': '953e784b7d4251dac5c47233ad6761e6cfa79d4eb953675e27792fb1193c6c5e',
        'obd_v2_settings': {
                'shared_backup': 1,
                'failover': 1
        },
        'user_id': '61e66e986558c735',
        'reference_id': 'a1580c8a-7f47-430d-9e96-5a6cefadbefd',
        'request_id': '5bd61b2c-b006-11ec-b0f9-164f4aa09487',
        'request_time': '2022-03-30 08:49:49',
        'exc_info': 'NoneType: None',
        'dial_reverse': 1
    },
    {
        'is_successfully_validated': 1,
        'company_id': '60d02bf71af0c636',
        'type': '1',
        'ivr_id': CENTRAL_1_IVRS[2],
        'number': 8860565234,
        'public_ivr_id': '60a49c4c27ba8419',
        'number_cc': 91,
        'call_expiry': '1',
        'misscall_expiry': '1',
        'company_display_number': '918587908013',
        'secret_token': '2f0a9a9a52640bf51b8d441bf80cdb1bb7fe9294ab12b7ac72bbff2448f69e5c',
        'obd_v2_settings': {
                'shared_backup': 1,
                'failover': 1
        },
        'user_id': '62147ba269dea820',
        'request_id': '5a1ae830-b006-11ec-b0f9-164f4aa09487',
        'request_time': '2022-03-30 08:49:46',
        'exc_info': 'NoneType: None',
        'dial_reverse': 1
    },
    {
        'is_successfully_validated': 1,
        'company_id': '60a392bbe3d97152',
        'type': '1',
        'ivr_id': CENTRAL_1_IVRS[2],
        'number': 8788115705,
        'public_ivr_id': '60a49c4c27ba8419',
        'number_cc': 91,
        'call_expiry': '1',
        'misscall_expiry': '1',
        'company_display_number': '919319368366',
        'secret_token': '953e784b7d4251dac5c47233ad6761e6cfa79d4eb953675e27792fb1193c6c5e',
        'obd_v2_settings': {
                'shared_backup': 1,
                'failover': 1
        },
        'user_id': '61bdc0b194510576',
        'reference_id': '804a99cb-9907-4de3-bf37-5a07246384a4',
        'request_id': '5a4b1ba4-b006-11ec-b0f9-174f4aa09487',
        'request_time': '2022-03-30 08:49:47',
        'exc_info': 'NoneType: None',
        'dial_reverse': 1
    },
    ]
}


CENTRAL_1_RUN_DATA = {
    1: [CENTRAL_1_IVRS[0], CENTRAL_1_IVRS[1]],
    3: [CENTRAL_1_IVRS[2], ]
}
