from copy import deepcopy
from datetime import datetime
from uuid import uuid4

from django.conf import settings

from central.settings.handlers import conf
from test_suite.test.data.common_data import (
    COMMON_KAM_GROUP_ID,
    DUMMY_GROUPS_OBJ_DATA,
    IVR_IDS,
    IVR_INFO_DATA,
    SQS_QUEUE_INFO_DATA,
)

GROUP_IVR_INFO_DATA = IVR_INFO_DATA

GROUP_IVRINFO_SQS_QUEUE_INFO_DATA = SQS_QUEUE_INFO_DATA + [
    {
        "gateway_prefix": conf.GROUP_IVRINFO_RELETION_QUEUE_GATEWAY_PREFIX,
        "queue_url": f"https://ap-southeast-1.queue.amazonaws.com/************/{conf.GROUP_IVRINFO_RELETION_QUEUE}",
        "queue_name": conf.GROUP_IVRINFO_RELETION_QUEUE,
    },
]


GROUP_IVR_INFO_DUMMY_GROUP_DATA = [
    {
        "name": "test-group-2",
        "region": "GURUGRAM",
        "group_alias": "test-group-alias-1",
        "group_priority": 1,
        "assigned_queues": "{'p_q': {'name': 'obd-central-group-queues_p_q_37', 'url': 'https://ap-southeast-1.queue.amazonaws.com/************/obd-central-group-queues_p_q_37', 'threshold': 60}, 'np_q': {'name': 'obd-central-group-queues_np_q_37', 'url': 'https://ap-southeast-1.queue.amazonaws.com/************/obd-central-group-queues_np_q_37', 'threshold': 30}}",
        "kam_group_id": COMMON_KAM_GROUP_ID + 1,
        "settings": '{"is_obd":"1","c_id":"XYZ","d_id":"SIP","operator":"SIP_AIRTEL","caller_id":"122","last_digit":"04"}',
        "is_default": 1,
        "number_data": {
            "number": "*********90",
            "number_priority": 1,
        },
        "groupivrinforelationship_data": {"ivr_info_ivr_id": IVR_IDS[1]},
    }
]

for data in deepcopy(DUMMY_GROUPS_OBJ_DATA):
    data.pop("number_data")
    GROUP_IVR_INFO_DUMMY_GROUP_DATA.append(data)


GROUP_IVR_FIX_DID_DUMMY_DATA = {
    "status": "success",
    "code": 200,
    "message": "did data",
    "data": {
        "did": "*********99",
        "region": "GURUGRAM",
        "type": settings.FIX_DID_OUTGOING_TYPE,
        "version": settings.FIX_DID_VERSION,
        "group": "some-group",
        "group_alias": "test-group-alias",
        "group_priority": 1,
        "number_priority": 1,
        "kam_group_id": str(COMMON_KAM_GROUP_ID),
        "kam_c_id": "XY-XY-XYX",
        "kam_d_id": "XYX-YXY",
        "ivrs": [{"id": IVR_IDS[0], "name": "Click to call"}],
        "company": {"display_number": "121212121221"},
        "pilot_number": "2269659500",
        "is_assigned": 1,
    },
}


GROUP_IVR_EXTERNAL_KAMAILIO_API_DATA = {
    "message": "List of active servers",
    "status": "success",
    "code": 200,
    "result": {
        str(COMMON_KAM_GROUP_ID): {
            "name": GROUP_IVR_FIX_DID_DUMMY_DATA["data"]["group"],
            "is_obd": 1,
            "c_id": GROUP_IVR_INFO_DATA[0]["c_id"],
            "region": "GURUGRAM",
            "group_alias": GROUP_IVR_FIX_DID_DUMMY_DATA["data"]["group_alias"],
            "d_id": "XYX",
            "operator": "AI_OFFICE",
            "caller_id": *********,
            "last_digit": 90,
            "servers": ["xy.something.info"],
        }
    },
}


GROUP_IVR_MAP_DID_EVENT = {
    "uid": uuid4(),
    "event": {
        "namespace": "fix_did",
        "version": "0.1.1",
        "etag": "Qwerty===",
        "action": "map_did_ivr",
        "created_at": datetime.now(),
    },
    "data": {
        "did": [GROUP_IVR_FIX_DID_DUMMY_DATA["data"]["did"]],
        "company_id": "1",
    },
}
