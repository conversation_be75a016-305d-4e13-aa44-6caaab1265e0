import json
from unittest.mock import patch

from central.settings.handlers import conf
from django.test import override_settings, tag
from handlers.response_manager.main import ResponseManager
from handlers.models import CentralRequestInfo, IvrInfo, SQSQueueInfo
from test_suite.test.base import BaseTestCase
from test_suite.test.data.response_manager.data import (
                                                            RESPONSE_MANAGER_DUMMY_REQUESTS,
                                                            RESPONSE_MANAGER_IVR_INFO_DATA,
                                                            RESPONSE_MANAGER_SQS_QUEUE_INFO_DATA,
                                                            RESPONSE_MANAGER_QUEUE_DATA)
from utills.cache_manager.main import CacheManager
from utills.shared_cache_manager.main import SharedCacheManager
from utills.shared_cache_manager.conf import SHARED_REDIS_CON

from datetime import datetime, timedelta
from django.core.cache import cache


@tag('response_manager')
@override_settings(CACHEOPS_ENABLED=False) 
class ResponseManagerTestCase(BaseTestCase):
    IVR_DATA = RESPONSE_MANAGER_IVR_INFO_DATA
    REQUESTS_DATA = RESPONSE_MANAGER_DUMMY_REQUESTS
    CACHE_TTL = 10

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        for data in RESPONSE_MANAGER_SQS_QUEUE_INFO_DATA:
            SQSQueueInfo.objects.create(**data)

    def setUp(self):
        super().setUp()
        cache.clear()

        self.sqs_fetch_message_patch = patch('utills.sqs_manager.main.SQSManager.fetch_message_sp', side_effect=[RESPONSE_MANAGER_QUEUE_DATA,[]])
        self.sqs_fetch_message_mock = self.sqs_fetch_message_patch.start()

        self.rm_sleep_dur_patch = patch('central.settings.handlers.conf.RM_SLEEP_DUR', 0)
        self.rm_sleep_dur_patch.start()

        self.sqs_delete_message_patch = patch(
            'utills.sqs_manager.main.SQSManager.sqs_delete_message')
        self.sqs_delete_message_mock = self.sqs_delete_message_patch.start()

        self.routing_manager_q_router_patch = patch(
            'utills.routing_manager.main.RoutingManger.main_service_queue_router', return_value=conf.OBD_SERVICES['complete_service'])

        self.routing_manager_q_router_mock = self.routing_manager_q_router_patch.start()

        self.sqs_fifo_pusher_patch = patch('utills.sqs_manager.main.SQSManager.sqs_fifo_pusher')
        self.sqs_fifo_pusher_mock = self.sqs_fifo_pusher_patch.start()

        self.setup_requests()

    def tearDown(self):
        super().tearDown()
        cache.clear()

        self.sqs_fetch_message_patch.stop()
        self.rm_sleep_dur_patch.stop()
        self.sqs_delete_message_patch.stop()
        self.routing_manager_q_router_patch.stop()
        self.sqs_fifo_pusher_patch.stop()



    def test_run(self):
        CacheManager.set_value(conf.RESPONSE_MANANGER_COUNT_KEY, len(
            RESPONSE_MANAGER_QUEUE_DATA), expiry=self.CACHE_TTL)
        
        ResponseManager.run()

        qs= CentralRequestInfo.objects.filter(
            is_req_completed = CentralRequestInfo.YES,
            is_onhold_req = CentralRequestInfo.NA,
            completion_event_type = CentralRequestInfo.FROM_RESPONSE_MANAGER,
            ).exclude(event_response=CentralRequestInfo.NA)

        self.assertEqual(len(RESPONSE_MANAGER_QUEUE_DATA), qs.count())

        for data in RESPONSE_MANAGER_QUEUE_DATA:
            body = json.loads(data['Body'])
            request_id = body['request_id']
            
            filtered_qs = qs.filter(request_id=request_id)

            self.assertTrue(filtered_qs.exists())

        self.assertEqual(self.sqs_fifo_pusher_mock.call_count, len(RESPONSE_MANAGER_QUEUE_DATA))
        
        self.assertEqual(self.sqs_delete_message_mock.call_count, len(
            RESPONSE_MANAGER_QUEUE_DATA))

        cache_val = CacheManager.get_value(conf.RESPONSE_MANANGER_COUNT_KEY)

        self.assertIsNone(cache_val)

