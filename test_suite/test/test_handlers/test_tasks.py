from django.test import override_settings

import pytest

from handlers.tasks import notify_true_caller


@pytest.mark.django_db
@pytest.mark.parametrize(
    "request_data",
    [
        "central_request_info/type_1.json",
        "central_request_info/type_2.json",
    ],
)
def test_notify_true_caller_when_enabled(
    load_json,
    request_data,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
):
    data = load_json(request_data)
    data.update(
        {
            "group_settings": {},
            "ivr_settings": {},
            "source_number": "1234567890",
            "type": "1",
        }
    )
    # Mock the TrueCaller APIs
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()

    # Call the task
    notify_true_caller(data)
    assert token_resp.call_count == 1
    assert resp.call_count == 1


@pytest.mark.django_db
@pytest.mark.parametrize(
    "request_data",
    [
        "central_request_info/type_1.json",
        "central_request_info/type_2.json",
    ],
)
def test_notify_true_caller_when_disabled(
    load_json,
    request_data,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_disabled,
):
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()
    data = load_json(request_data)

    # Prepare request data
    data.update(
        {
            "group_settings": {},
            "ivr_settings": {},
            "source_number": "1234567890",
            "type": "1",
        }
    )

    # Call the task
    notify_true_caller(data)
    assert token_resp.call_count == 0
    assert resp.call_count == 0


@pytest.mark.django_db
@pytest.mark.parametrize(
    "request_data",
    [
        "central_request_info/type_1.json",
        "central_request_info/type_2.json",
    ],
)
def test_notify_true_caller_missing_did(
    load_json,
    request_data,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
):
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()
    data = load_json(request_data)

    # Prepare request data
    data.update(
        {
            "group_settings": {},
            "ivr_settings": {},
            "type": "1",
        }
    )
    # Call the task
    notify_true_caller(data)
    assert token_resp.call_count == 0
    assert resp.call_count == 0


@pytest.mark.django_db
@pytest.mark.parametrize(
    "invalid_did,should_call_api",
    [
        ("", False),  # Empty DID
        ("123", False),  # Too short
        ("abc123", False),  # Invalid format
        ("91123456789012345", False),  # Too long
    ],
)
def test_notify_true_caller_invalid_did_formats(
    load_json,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
    invalid_did,
    should_call_api,
):
    """Test notify_true_caller with various invalid DID formats"""
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()

    data = load_json("central_request_info/type_1.json")
    data.update(
        {
            "group_settings": {},
            "ivr_settings": {},
            "source_number": invalid_did,
            "type": "1",
        }
    )

    # Call the task
    notify_true_caller(data)

    # Check API call behavior
    if should_call_api:
        assert token_resp.call_count == 1
        assert resp.call_count == 1
    else:
        assert token_resp.call_count == 0
        assert resp.call_count == 0


@pytest.mark.django_db
def test_notify_true_caller_valid_country_code_conversion(
    load_json,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
):
    """Test that +1234567890 gets converted to 911234567890 and works"""
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()

    data = load_json("central_request_info/type_1.json")
    data.update(
        {
            "group_settings": {},
            "ivr_settings": {},
            "source_number": "+1234567890",  # This should work after normalization
            "type": "1",
        }
    )

    # Call the task
    notify_true_caller(data)

    # Should make API calls since +1234567890 becomes 911234567890 (valid)
    assert token_resp.call_count == 1
    assert resp.call_count == 1


@pytest.mark.django_db
@pytest.mark.parametrize(
    "did",
    [
        "919876543210",  # Already correct
        "+919876543210",  # With plus
        "09876543210",  # Local format
        "9876543210",  # Plain 10 digits
        "+91 9876-543-210",  # With spaces and dashes
    ],
)
def test_notify_true_caller_valid_did_formats(
    load_json,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
    did,
):
    """Test notify_true_caller with various valid DID formats that should be normalized"""
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()

    data = load_json("central_request_info/type_1.json")
    data.update(
        {
            "group_settings": {},
            "ivr_settings": {},
            "source_number": did,
            "type": "1",
        }
    )

    # Call the task
    notify_true_caller(data)

    # Should make API calls for valid DID
    assert token_resp.call_count == 1, f"Failed for DID: {did}"
    assert resp.call_count == 1, f"Failed for DID: {did}"


@pytest.mark.django_db
def test_notify_true_caller_api_failure_handling(
    load_json,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
):
    """Test notify_true_caller when TrueCaller API calls fail"""
    from rest_framework import status

    # Mock token API to fail
    token_resp = mock_true_caller_token_api(
        status_code=status.HTTP_401_UNAUTHORIZED
    )
    resp = mock_true_caller_dial_assist_api()

    data = load_json("central_request_info/type_1.json")
    data.update(
        {
            "group_settings": {},
            "ivr_settings": {},
            "source_number": "919876543210",
            "type": "1",
        }
    )

    # Call the task - should not crash even if API fails
    notify_true_caller(data)

    # Token API should be called but dial assist might not be called due to token failure
    assert token_resp.call_count == 1


@pytest.mark.django_db
@pytest.mark.parametrize(
    "case",
    [
        {"number_cc": "91", "number": "9876543210"},
        {"number_cc": "+91", "number": "9876543210"},
        {"number_cc": "91", "number": "09876543210"},
    ],
)
def test_notify_true_caller_with_different_number_formats(
    load_json,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
    case,
):
    """Test notify_true_caller with different customer number formats"""
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()

    data = load_json("central_request_info/type_1.json")
    data.update(
        {
            "group_settings": {},
            "ivr_settings": {},
            "source_number": "919876543210",
            "type": "1",
            "number_cc": case["number_cc"],
            "number": case["number"],
        }
    )

    # Call the task
    notify_true_caller(data)

    # Should make API calls
    assert token_resp.call_count == 1, f"Failed for case: {case}"
    assert resp.call_count == 1, f"Failed for case: {case}"
