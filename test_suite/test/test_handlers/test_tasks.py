from django.test import override_settings

import pytest

from handlers.tasks import notify_true_caller


@pytest.mark.django_db
@pytest.mark.parametrize(
    "request_data",
    [
        "central_request_info/type_1.json",
        "central_request_info/type_2.json",
    ],
)
def test_notify_true_caller_when_enabled(
    load_json,
    request_data,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
):
    data = load_json(request_data)
    data.update(
        {
            "group_settings": {},
            "ivr_settings": {},
            "source_number": "1234567890",
            "type": "1",
        }
    )
    # Mock the TrueCaller APIs
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()

    # Call the task
    notify_true_caller(data)
    assert token_resp.call_count == 1
    assert resp.call_count == 1


@pytest.mark.django_db
@pytest.mark.parametrize(
    "request_data",
    [
        "central_request_info/type_1.json",
        "central_request_info/type_2.json",
    ],
)
def test_notify_true_caller_when_disabled(
    load_json,
    request_data,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_disabled,
):
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()
    data = load_json(request_data)

    # Prepare request data
    data.update(
        {
            "group_settings": {},
            "ivr_settings": {},
            "source_number": "1234567890",
            "type": "1",
        }
    )

    # Call the task
    notify_true_caller(data)
    assert token_resp.call_count == 0
    assert resp.call_count == 0


@pytest.mark.django_db
@pytest.mark.parametrize(
    "request_data",
    [
        "central_request_info/type_1.json",
        "central_request_info/type_2.json",
    ],
)
def test_notify_true_caller_missing_did(
    load_json,
    request_data,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
):
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()
    data = load_json(request_data)

    # Prepare request data
    data.update(
        {
            "group_settings": {},
            "ivr_settings": {},
            "type": "1",
        }
    )
    # Call the task
    notify_true_caller(data)
    assert token_resp.call_count == 0
    assert resp.call_count == 0
