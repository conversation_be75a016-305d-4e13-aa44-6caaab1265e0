import pytest

from handlers.tasks import normalize_to_91_format


class TestNormalizeTo91Format:
    """Test cases for the normalize_to_91_format function"""

    def test_normalize_already_correct_format(self):
        """Test number already in correct 91XXXXXXXXXX format"""
        number = "919876543210"
        result = normalize_to_91_format(number)
        assert result == "919876543210"

    def test_normalize_with_leading_plus(self):
        """Test number with leading + sign"""
        number = "+919876543210"
        result = normalize_to_91_format(number)
        assert result == "919876543210"

    def test_normalize_local_format_with_leading_zero(self):
        """Test local format with leading 0 (e.g., 09876543210)"""
        number = "09876543210"
        result = normalize_to_91_format(number)
        assert result == "919876543210"

    def test_normalize_plain_10_digit_number(self):
        """Test plain 10-digit mobile number"""
        number = "9876543210"
        result = normalize_to_91_format(number)
        assert result == "919876543210"

    def test_normalize_with_spaces_and_dashes(self):
        """Test number with spaces and dashes"""
        number = "+91 9876-543-210"
        result = normalize_to_91_format(number)
        assert result == "919876543210"

    def test_normalize_local_format_with_spaces(self):
        """Test local format with spaces"""
        number = "0 9876 543 210"
        result = normalize_to_91_format(number)
        assert result == "919876543210"

    def test_normalize_plain_number_with_spaces(self):
        """Test plain number with spaces"""
        number = "9876 543 210"
        result = normalize_to_91_format(number)
        assert result == "919876543210"

    def test_normalize_invalid_too_long_after_91(self):
        """Test number that's too long after 91 prefix"""
        number = "9198765432101234"  # Too many digits after 91
        with pytest.raises(
            ValueError, match="Number too long after '91' prefix"
        ):
            normalize_to_91_format(number)

    def test_normalize_invalid_short_number(self):
        """Test number that's too short"""
        number = "987654321"  # Only 9 digits
        with pytest.raises(
            ValueError, match="Invalid or unsupported phone number format"
        ):
            normalize_to_91_format(number)

    def test_normalize_invalid_long_number_without_91(self):
        """Test number that's too long without 91 prefix"""
        number = "98765432101"  # 11 digits but doesn't start with 0
        with pytest.raises(
            ValueError, match="Invalid or unsupported phone number format"
        ):
            normalize_to_91_format(number)

    def test_normalize_invalid_empty_number(self):
        """Test empty number"""
        number = ""
        with pytest.raises(
            ValueError, match="Invalid or unsupported phone number format"
        ):
            normalize_to_91_format(number)

    def test_normalize_invalid_non_numeric(self):
        """Test non-numeric characters"""
        number = "abcd123456"
        # This will be treated as 10 characters and get "91" prepended
        result = normalize_to_91_format(number)
        assert result == "91abcd123456"

    def test_normalize_invalid_special_characters_only(self):
        """Test number with only special characters"""
        number = "+-() "
        with pytest.raises(
            ValueError, match="Invalid or unsupported phone number format"
        ):
            normalize_to_91_format(number)

    def test_normalize_edge_case_91_prefix_exact_length(self):
        """Test edge case with exactly 12 digits starting with 91"""
        number = "919876543210"
        result = normalize_to_91_format(number)
        assert result == "919876543210"

    def test_normalize_edge_case_local_format_exact_length(self):
        """Test edge case with exactly 11 digits starting with 0"""
        number = "09876543210"
        result = normalize_to_91_format(number)
        assert result == "919876543210"

    def test_normalize_edge_case_plain_exact_length(self):
        """Test edge case with exactly 10 digits"""
        number = "9876543210"
        result = normalize_to_91_format(number)
        assert result == "919876543210"

    def test_normalize_with_country_code_other_than_91(self):
        """Test number with different country code"""
        number = "+1234567890"  # US format - after removing +, becomes 1234567890 (10 digits)
        result = normalize_to_91_format(number)
        assert result == "911234567890"

    def test_normalize_91_prefix_but_wrong_length(self):
        """Test number starting with 91 but wrong total length"""
        number = "91987654321"  # 11 digits total, should be 12
        with pytest.raises(
            ValueError, match="Invalid or unsupported phone number format"
        ):
            normalize_to_91_format(number)

    def test_normalize_local_format_wrong_length(self):
        """Test local format with wrong length"""
        number = "098765432"  # 9 digits total, should be 11
        with pytest.raises(
            ValueError, match="Invalid or unsupported phone number format"
        ):
            normalize_to_91_format(number)

    def test_normalize_whitespace_only(self):
        """Test number with only whitespace"""
        number = "   "
        with pytest.raises(
            ValueError, match="Invalid or unsupported phone number format"
        ):
            normalize_to_91_format(number)

    def test_normalize_mixed_valid_invalid_chars(self):
        """Test number with mix of valid and invalid characters"""
        number = (
            "91abc9876543210"  # 15 characters, starts with 91, longer than 12
        )
        with pytest.raises(
            ValueError, match="Number too long after '91' prefix"
        ):
            normalize_to_91_format(number)
