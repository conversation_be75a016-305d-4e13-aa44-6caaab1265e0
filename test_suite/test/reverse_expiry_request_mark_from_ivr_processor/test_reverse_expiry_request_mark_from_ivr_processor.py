from django.test import override_settings, tag
import json
from unittest.mock import patch
from central.settings.handlers import conf
from handlers.management.commands.reverse_expiry_request_mark_from_ivr_processor import ReverseExpiryRequestMarkFromIvrProcessorRunner
from handlers.models import CentralRequestInfo, OnHoldUserTracing
from handlers.on_hold.processor.main import OnHoldProcessor
from test_suite.test.base import BaseTestCase
from test_suite.test.data.reverse_expiry_request_mark_from_ivr_processor.data import (
    REVERSE_EXPIRY_REQUEST_DUMMY_REQUESTS,
    REVERSE_EXPIRY_REQUEST_IVR_INFO_DATA
)
from utills.shared_cache_manager.main import SharedCacheManager

from utills.shared_cache_manager.conf import SHARED_REDIS_CON
import json

@tag('reverse_expiry_request_mark_from_ivr_processor')
@override_settings(CACHEOPS_ENABLED=False)
class TestReverseExpiryRequestMarkFromIvrProcessor(BaseTestCase):
    IVR_DATA = REVERSE_EXPIRY_REQUEST_IVR_INFO_DATA
    REQUESTS_DATA = REVERSE_EXPIRY_REQUEST_DUMMY_REQUESTS
    SHARED_CACHE_TTL = 10
    SHARED_CACHE_PREFIX = ":{version}:{key}".format(
        version=conf.REDIS_VERSION_KEY, key=conf.CANCELED_REQUEST_KEY)

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        

    def setUp(self):
        super().setUp()
        self.setup_requests()

    def tearDown(self):
        super().tearDown()

    def setup_shared_cache_keys(self, cri_qs):
        for request in cri_qs:
            key = f"{self.SHARED_CACHE_PREFIX}_{request.request_id}"
            SharedCacheManager.set_value(key, True, expiry= self.CACHE_TTL)

    def test_reverse_expiry_mark_from_ivr_processor(self):
        cri_qs = CentralRequestInfo.objects.all()
        self.setup_shared_cache_keys(cri_qs)



    def setup_shared_cache_key(self):
        pattern_prefix = self.SHARED_CACHE_PREFIX
        qs =  CentralRequestInfo.objects.all() # no need to add any filter, as the qs is being filtered by request in the function
        request_ids= []

        for obj in qs:
            SharedCacheManager.set_value(
                f"{pattern_prefix}{obj.request_id}", "true", self.SHARED_CACHE_TTL)

            request_ids.append(obj.request_id)

        return qs.count(), request_ids

    def test_reverse_expiry_mark_from_ivr_processor(self):
        SHARED_REDIS_CON.flushdb()
        
        key_count, request_ids = self.setup_shared_cache_key()
        ReverseExpiryRequestMarkFromIvrProcessorRunner.reverse_expiry_mark_from_ivr_processor()
        keys = SharedCacheManager.get_keys(self.SHARED_CACHE_PREFIX)

        qs = CentralRequestInfo.objects.filter(
            is_req_completed=CentralRequestInfo.YES,
            is_onhold_req = CentralRequestInfo.NA,
            completion_event_type=CentralRequestInfo.FROM_IVR_PROCESSOR_FAILED,
            is_enable=CentralRequestInfo.NO)

        self.assertEqual(qs.count(), key_count, "Count didn't matched")
        
        for request_id in request_ids:
            filter_qs = qs.filter(request_id=request_id)

            self.assertTrue(filter_qs.exists(), f"Request  with request_id - {request_id} wasn't cancelled")

        self.assertEqual(len(keys), 0, "All the keys were not deleted.")

        SHARED_REDIS_CON.flushdb()

    def test_on_hold_user_tracing_patch(self):
        qs =  CentralRequestInfo.objects.all()
        for request in  qs:
            request_data = json.loads(request.raw_data)
            # Marking ON Hold for all requests
            OnHoldProcessor.mark_is_onhold_req(request_data)
        
        user_trace_qs = OnHoldUserTracing.objects.all()

        # Marking all users disabled for each request
        qs.invalidated_update(
            is_req_completed = CentralRequestInfo.NO,
            is_enable = CentralRequestInfo.NO
        )

        ReverseExpiryRequestMarkFromIvrProcessorRunner.on_hold_user_tracing_patch()

        for user_trace_obj in user_trace_qs:
            # Checking whether the disabled req count and enable req count matches as expected.
            ivr_id = user_trace_obj.ivr_id
            user_id = user_trace_obj.user_id
            
            disable_req = CentralRequestInfo.objects.filter(
                ivr_id=ivr_id,
                is_req_completed= CentralRequestInfo.NO,
                is_enable= CentralRequestInfo.NO,
                is_onhold_req=CentralRequestInfo.ENTER,
                user_id=user_id)

            enabled_req = CentralRequestInfo.objects.filter(
                ivr_id=ivr_id,
                is_req_completed= CentralRequestInfo.NO,
                is_enable= CentralRequestInfo.YES,
                is_onhold_req=CentralRequestInfo.ENTER,
                user_id=user_id)

            self.assertEqual(disable_req.count(), user_trace_obj.count_disabled_req)

            self.assertEqual(enabled_req.count(), 1)
