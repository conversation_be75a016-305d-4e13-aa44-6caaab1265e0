from central.settings.handlers import conf
from test_suite.test.base import BaseTestCase
from utills.cache_manager.main import CacheManager
from test_suite.test.data.central_2.data import IVR_INFO_DATA


class Central2BaseTestCase(BaseTestCase):
    IVR_DATA = IVR_INFO_DATA

    DEFAULT_PQ1_COUNT = 0
    DEFAULT_PQ2_COUNT = 20
    DEFAULT_PQ3_COUNT = 20

    DEFAULT_CACHE_EXPIRY = 10


    def setUp(self):
        super().setUp()

        CacheManager.set_value(conf.CENTRAL_PQ1_COUNT_KEY, self.DEFAULT_PQ1_COUNT, expiry=self.DEFAULT_CACHE_EXPIRY)
        CacheManager.set_value(conf.CENTRAL_PQ2_COUNT_KEY, self.DEFAULT_PQ2_COUNT, expiry=self.DEFAULT_CACHE_EXPIRY)
        CacheManager.set_value(conf.CENTRAL_PQ3_COUNT_KEY, self.DEFAULT_PQ3_COUNT, expiry=self.DEFAULT_CACHE_EXPIRY)

    def tearDown(self):
        super().tearDown()
        CacheManager.delete_key(conf.CENTRAL_PQ1_COUNT_KEY)
        CacheManager.delete_key(conf.CENTRAL_PQ2_COUNT_KEY)
        CacheManager.delete_key(conf.CENTRAL_PQ3_COUNT_KEY)


    def get_pq_total_count_from_cache(self):
        pq1_count = CacheManager.get_value(conf.CENTRAL_PQ1_COUNT_KEY) or 0
        pq2_count = CacheManager.get_value(conf.CENTRAL_PQ2_COUNT_KEY)   or 0
        pq3_count =  CacheManager.get_value(conf.CENTRAL_PQ3_COUNT_KEY) or 0

        return (pq1_count + pq2_count + pq3_count)