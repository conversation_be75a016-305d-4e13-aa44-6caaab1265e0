from test_suite.test.central_2.base import Central2BaseTestCase
from django.test import override_settings
from django.test import tag
from utills.cache_manager.main import CacheManager
from central.settings.handlers import conf
from handlers.central_2.helper import Central2_Helper


@tag('central_2')
@override_settings(CACHEOPS_ENABLED=False)
class Central2Helper(Central2BaseTestCase):

    def test_is_cache_key_exists(self):
        val = Central2_Helper.is_cache_keys_exists()
        self.assertTrue(val, "central_2 - is_cache_keys_exists() returned False!!!")

        CacheManager.delete_key(conf.CENTRAL_PQ1_COUNT_KEY)
        CacheManager.delete_key(conf.CENTRAL_PQ2_COUNT_KEY)
        CacheManager.delete_key(conf.CENTRAL_PQ3_COUNT_KEY)
        val = Central2_Helper.is_cache_keys_exists()
        self.assertFalse(
            val, "central_2 - is_cache_keys_exists() returned True!!!")

    def test_get_thresholds(self):
        max_workers, c2_thread_threshold = Central2_Helper.get_thresholds()
        self.assertIsNotNone(max_workers, "central_2 - get_thresholds() returned max_workers is not None!!!")
        self.assertIsNotNone(c2_thread_threshold, "central_2 - get_thresholds() returned c2_thread_threshold is not None!!!")
