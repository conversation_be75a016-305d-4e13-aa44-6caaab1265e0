import json
import random
from copy import deepcopy
from unittest.mock import patch

from django.test import override_settings, tag

from central.settings.handlers import conf
from handlers.central_2.main import Central2
from handlers.models import CentralRequestInfo, Group, SQSQueueInfo
from test_suite.test.central_2.base import Central2BaseTestCase
from test_suite.test.data.central_2.data import (
    CENTRAL_2_DUMMY_CANCEL_REQUESTS,
    CENTRAL_2_DUMMY_GROUP_DATA,
    CENTRAL_2_DUMMY_GROUPS_OBJ_DATA,
    CENTRAL_2_DUMMY_REQUESTS,
    CENTRAL_2_DUMMY_SUCCESS_UDC_RESPONSE,
    CENTRAL_2_DUMMY_UDC_IVR_LOCKED_RESPONSE,
    CENTRAL_2_DUMMY_UDC_USER_LOCKED_RESPONSE,
    SQS_QUEUE_INFO_DATA,
)
from utills.cache_manager.main import CacheManager
from utills.helpers.helper import Helper
from utills.shared_cache_manager.main import Shared<PERSON>acheManager


@tag("central_2")
@override_settings(CACHEOPS_ENABLED=False)
class Central2TestCase(Central2BaseTestCase):
    GROUPS_DATA = CENTRAL_2_DUMMY_GROUPS_OBJ_DATA

    @classmethod
    def setUpTestData(cls):
        for data in SQS_QUEUE_INFO_DATA:
            SQSQueueInfo.objects.create(**data)

        super().setUpTestData()

        cls.set_up_groups()

    def setUp(self, *args, **kwargs):
        super().setUp(*args, **kwargs)

        self.sqs_fifo_pusher_patch = patch(
            "utills.sqs_manager.main.SQSManager.sqs_fifo_pusher"
        )
        self.sqs_fifo_pusher_mock = self.sqs_fifo_pusher_patch.start()

        self.sqs_delete_message_patch = patch(
            "utills.sqs_manager.main.SQSManager.sqs_delete_message"
        )
        self.sqs_delete_message_mock = self.sqs_delete_message_patch.start()

        self.manage_udc_api_check_patch = patch(
            "utills.external_apis.ExternalAPIs.manage_udc_api_check"
        )

        self.manage_udc_api_check_mock = (
            self.manage_udc_api_check_patch.start()
        )

        self.sqs_send_message_to_sqs_patch = patch(
            "utills.sqs_manager.main.SQSManager.send_message_to_sqs"
        )
        self.sqs_send_message_to_sqs_mock = (
            self.sqs_send_message_to_sqs_patch.start()
        )

        self.gq_routing_manager_resource_avail_patch = patch(
            "utills.group_queues_routing_manager.main.GroupQueuesRoutingManager.is_group_resource_avail",
            return_value=True,
        )
        self.gq_routing_manager_resource_avail_mock = (
            self.gq_routing_manager_resource_avail_patch.start()
        )

    def tearDown(self):
        self.sqs_fifo_pusher_patch.stop()
        self.sqs_delete_message_patch.stop()
        self.manage_udc_api_check_patch.stop()
        self.sqs_send_message_to_sqs_patch.stop()
        self.gq_routing_manager_resource_avail_patch.stop()

        shared_cache_key = self.get_shared_cache_key(
            CENTRAL_2_DUMMY_GROUP_DATA
        )

        SharedCacheManager.delete_key(shared_cache_key)

        CacheManager.delete_key(conf.ON_HOLD_COUNT_KEY)

    def central_2_dummy_requests_json_ls(self):
        json_ls = []
        for request in CENTRAL_2_DUMMY_REQUESTS:
            data = {}
            data["Body"] = json.dumps(request)
            data["ReceiptHandle"] = "some receipt handle"
            json_ls.append(data)
        return json_ls

    def get_shared_cache_value(self):
        shared_cache_key = self.get_shared_cache_key(
            CENTRAL_2_DUMMY_GROUP_DATA
        )
        initial_shared_cache_val = int(
            SharedCacheManager.get_value(shared_cache_key) or 0
        )
        return initial_shared_cache_val

    def get_shared_cache_key(self, group):
        shared_cache_key = ":{prefix}:{value}".format(
            prefix=conf.REDIS_VERSION_KEY,
            value=conf.GROUP_MESSGAE_COUNT_KEY.format(q_url=group["url"]),
        )

        return shared_cache_key

    def udc_check_side_effect_for_udc_lock(self):
        choice_ls = [
            CENTRAL_2_DUMMY_UDC_USER_LOCKED_RESPONSE,
            CENTRAL_2_DUMMY_UDC_IVR_LOCKED_RESPONSE,
        ]
        return random.choice(choice_ls)

    def __test_process_group(
        self,
        initial_pq_count,
        initial_shared_cache_val,
        total_req_count,
        udc_success=True,
        udc_lock=False,
    ):
        # whether the udc check has been done for each request
        self.assertEqual(
            self.manage_udc_api_check_mock.call_count,
            total_req_count,
            "Manage udc api check call count is not equal to the request count!!!",
        )

        if udc_lock:
            # whether the requests has been sent to group queue
            self.assertEqual(
                self.sqs_send_message_to_sqs_mock.call_count,
                0,
                "SQS send message to sqs call count is not equal to 0!!!",
            )

            # whether the requests has been deleted from the pq queues
            self.assertEqual(
                self.sqs_delete_message_mock.call_count,
                0,
                "SQS delete message call count is not equal to 0!!!",
            )

            # whether the pq count has been decreased
            self.assertEqual(
                self.get_pq_total_count_from_cache(),
                initial_pq_count,
                "Total pq count hasn't changed!!!",
            )

        else:
            # whether the requests has been sent to group queue
            self.assertEqual(
                self.sqs_send_message_to_sqs_mock.call_count,
                total_req_count,
                "SQS send message to sqs call count is not equal to requests_count!!!",
            )

            # whether the requests has been deleted from the pq queues
            self.assertEqual(
                self.sqs_delete_message_mock.call_count,
                total_req_count,
                "SQS delete message call count is not equal to requests_count!!!",
            )

            # whether the pq count has been decreased
            self.assertEqual(
                self.get_pq_total_count_from_cache(),
                initial_pq_count - total_req_count,
                "Total pq count hasn't been decreased!!!",
            )

            if udc_success:
                # whether the count in shared cache has been increased
                shared_cache_val = self.get_shared_cache_value()
                self.assertEqual(
                    initial_shared_cache_val + total_req_count,
                    shared_cache_val,
                    "Shared cache_val hasn't been incremented",
                )
                # whether the helper.invoke_process_manager has been called for each request
                self.assertEqual(
                    self.sqs_fifo_pusher_mock.call_count,
                    total_req_count,
                    "sqs_fifo_pusher_mock in Helper.invoke_process_manager hasn't been called for all requests!!!",
                )

            else:
                on_hold_count = CacheManager.get_value(conf.ON_HOLD_COUNT_KEY)
                self.assertEqual(
                    on_hold_count,
                    total_req_count,
                    "On Hold Count is not equal to total request count.",
                )

                ohq_url = Helper.get_queue_name_url_mapping(
                    "queue_name",
                    conf.ON_HOLD_Q_NAME,
                    conf.ON_HOLD_Q_GATEWAY_PREFIX,
                )[conf.ON_HOLD_Q_NAME]

                for request in CENTRAL_2_DUMMY_REQUESTS:
                    self.sqs_send_message_to_sqs_mock.assert_any_call(
                        ohq_url, json.dumps(request)
                    )

    def _test_process_group_with_udc_response(
        self, udc_success=True, udc_lock=False
    ):
        initial_pq_count = self.get_pq_total_count_from_cache()

        cq_name_urls = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )
        pq_url = cq_name_urls[conf.CENTRAL_PQ2]
        q_key = conf.CENTRAL_QUEUES_COUNT_KEYS.get(conf.CENTRAL_PQ2)

        # in case of UDC lock (i.e ivr lock or user lock)
        if udc_lock:
            self.manage_udc_api_check_mock.side_effect = (
                lambda *args, **kwargs: self.udc_check_side_effect_for_udc_lock()
            )

        # in case of udc success response
        elif udc_success:
            self.manage_udc_api_check_mock.return_value = (
                CENTRAL_2_DUMMY_SUCCESS_UDC_RESPONSE["data"]
            )
        # in case of udc failed response
        elif not udc_success:
            self.manage_udc_api_check_mock.return_value = {}

        initial_shared_cache_val = self.get_shared_cache_value()

        total_req_count = len(CENTRAL_2_DUMMY_REQUESTS)
        for request in CENTRAL_2_DUMMY_REQUESTS:
            obj, json_body = self.create_cri_object_from_dict(request)
            json_body["ReceiptHandle"] = "some receipt handle"
            Central2().process_group(
                json_body, request, pq_url, CENTRAL_2_DUMMY_GROUP_DATA, q_key
            )

        self.__test_process_group(
            initial_pq_count,
            initial_shared_cache_val,
            total_req_count,
            udc_success=udc_success,
            udc_lock=udc_lock,
        )

    def _test_cancel_request(self, initial_pq_count, initial_shared_cache_val):
        self.assertEqual(
            self.get_pq_total_count_from_cache(),
            initial_pq_count - len(CENTRAL_2_DUMMY_CANCEL_REQUESTS),
            "Total pq count hasn't been decreased!!!",
        )

        canceled_request = CentralRequestInfo.objects.filter(
            is_req_completed=CentralRequestInfo.YES,
            is_enable=CentralRequestInfo.NO,
        )

        self.assertTrue(
            canceled_request.exists(), "No canceled request found!!!"
        )

        self.assertEqual(
            canceled_request.count(),
            len(CENTRAL_2_DUMMY_CANCEL_REQUESTS),
            "Canceled request count is not equal to the request count!!!",
        )

        self.assertEqual(
            self.sqs_fifo_pusher_mock.call_count,
            len(CENTRAL_2_DUMMY_CANCEL_REQUESTS),
            "SQS fifo pusher call count is not equal to the request count!!!",
        )

        self.assertEqual(
            self.sqs_delete_message_mock.call_count,
            len(CENTRAL_2_DUMMY_CANCEL_REQUESTS),
            "SQS delete message call count is not equal to the request count!!!",
        )

        # whether the count in shared cache has been increased
        shared_cache_val = self.get_shared_cache_value()
        self.assertEqual(
            initial_shared_cache_val,
            shared_cache_val,
            "Shared cache value has changed!!!",
        )

    # ------------- Test Cases Starts here -------------------

    def test_cancel_request(self):
        current_pq_count = self.get_pq_total_count_from_cache()
        cq_name_urls = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )
        pq_url = cq_name_urls[conf.CENTRAL_PQ2]
        q_key = conf.CENTRAL_QUEUES_COUNT_KEYS.get(conf.CENTRAL_PQ2)

        initial_shared_cache_val = self.get_shared_cache_value()

        for request in CENTRAL_2_DUMMY_CANCEL_REQUESTS:
            obj, json_body = self.create_cri_object_from_dict(request)
            json_body["ReceiptHandle"] = "some receipt handler"
            Central2().cancle_request(json_body, request, pq_url, q_key)

        self._test_cancel_request(current_pq_count, initial_shared_cache_val)

    def test_process_group_without_request_in_db(self):
        initial_pq_count = self.get_pq_total_count_from_cache()

        cq_name_urls = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )
        pq_url = cq_name_urls[conf.CENTRAL_PQ2]
        q_key = conf.CENTRAL_QUEUES_COUNT_KEYS.get(conf.CENTRAL_PQ2)

        group = Group.objects.first()

        json_body = {}

        json_body["Body"] = json.dumps(CENTRAL_2_DUMMY_REQUESTS[0])
        json_body["ReceiptHandle"] = "some receipt handle"

        # Testing whether the request will be deleted from the queue if not found in DB.
        Central2().process_group(
            json_body, CENTRAL_2_DUMMY_REQUESTS[0], pq_url, group, q_key
        )

        self.assertEqual(
            self.sqs_delete_message_mock.call_count,
            1,
            "SQS delete message call count is not equal to 1!!!",
        )

        self.assertEqual(
            self.get_pq_total_count_from_cache(),
            initial_pq_count - 1,
            "Total pq count hasn't been decreased!!!",
        )

    def test_process_group_udc_response_success(
        self,
    ):
        self._test_process_group_with_udc_response(udc_success=True)

    def test_process_group_udc_response_success_with_cache_key_more_than_channels(
        self,
    ):
        initial_pq_count = self.get_pq_total_count_from_cache()

        company_id = CENTRAL_2_DUMMY_REQUESTS[0]["company_id"]
        request_data = []
        for data in deepcopy(CENTRAL_2_DUMMY_REQUESTS):
            data["company_id"] = company_id
            request_data.append(data)

        total_req_count = len(request_data)

        cq_name_urls = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )
        pq_url = cq_name_urls[conf.CENTRAL_PQ2]
        q_key = conf.CENTRAL_QUEUES_COUNT_KEYS.get(conf.CENTRAL_PQ2)
        udc_res = deepcopy(CENTRAL_2_DUMMY_SUCCESS_UDC_RESPONSE["data"])
        channels_used = 2
        udc_res["channel_data"] = total_req_count - channels_used
        self.manage_udc_api_check_mock.return_value = udc_res
        initial_shared_cache_val = self.get_shared_cache_value()

        for request in request_data:
            _, json_body = self.create_cri_object_from_dict(request)
            json_body["ReceiptHandle"] = "some receipt handle"
            Central2().process_group(
                json_body, request, pq_url, CENTRAL_2_DUMMY_GROUP_DATA, q_key
            )

        shared_cache_val = self.get_shared_cache_value()
        self.assertEqual(
            self.manage_udc_api_check_mock.call_count,
            total_req_count,
            "Manage udc api check call count is not equal to the request count!!!",
        )
        self.assertEqual(
            self.sqs_fifo_pusher_mock.call_count,
            total_req_count - channels_used,
            "sqs_fifo_pusher_mock in Helper.invoke_process_manager hasn't been called for all UDC available requests!!!",
        )
        self.assertEqual(
            initial_shared_cache_val + total_req_count - channels_used,
            shared_cache_val,
            "Shared cache_val isn't equal to count of udc available requests",
        )
        on_hold_count = CacheManager.get_value(conf.ON_HOLD_COUNT_KEY)
        self.assertEqual(
            on_hold_count,
            channels_used,
            "On Hold Count is not equal to channels_used count.",
        )
        ohq_url = Helper.get_queue_name_url_mapping(
            "queue_name",
            conf.ON_HOLD_Q_NAME,
            conf.ON_HOLD_Q_GATEWAY_PREFIX,
        )[conf.ON_HOLD_Q_NAME]

        for request in request_data[len(request_data) - channels_used :]:
            self.sqs_send_message_to_sqs_mock.assert_any_call(
                ohq_url, json.dumps(request)
            )

        self.assertEqual(
            self.get_pq_total_count_from_cache(),
            initial_pq_count - total_req_count,
            "Total pq count hasn't been decreased!!!",
        )

    def test_process_group_udc_response_none(self):
        self._test_process_group_with_udc_response(udc_success=False)

    def test_process_group_udc_response_lock(self):
        self._test_process_group_with_udc_response(
            udc_success=False, udc_lock=True
        )

    def test_process_request(self):
        initial_pq_count = self.get_pq_total_count_from_cache()
        cq_name_urls = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )
        pq_url = cq_name_urls[conf.CENTRAL_PQ2]
        q_key = conf.CENTRAL_QUEUES_COUNT_KEYS.get(conf.CENTRAL_PQ2)

        total_req_count = len(CENTRAL_2_DUMMY_REQUESTS)
        initial_shared_cache_val = self.get_shared_cache_value()

        self.manage_udc_api_check_mock.return_value = (
            CENTRAL_2_DUMMY_SUCCESS_UDC_RESPONSE["data"]
        )

        for request in CENTRAL_2_DUMMY_REQUESTS:
            obj, json_body = self.create_cri_object_from_dict(request)
            json_body["ReceiptHandle"] = "some receipt handle"
            Central2().process_request(json_body, pq_url, q_key)
            self.manage_udc_api_check_mock.assert_called_with(
                obj,
                user_view_only=Helper.get_toggle_view_only_value(obj.ivr_id),
            )

        # testing process_group as process request calls the same function any ways.
        self.__test_process_group(
            initial_pq_count,
            initial_shared_cache_val,
            total_req_count,
            udc_success=True,
            udc_lock=False,
        )

    def test_process_request_cancelled(self):
        initial_pq_count = self.get_pq_total_count_from_cache()
        cq_name_urls = Helper.get_queue_name_url_mapping(
            "gateway_prefix", conf.CENTRAL_PQ_GATEWAY_PREFIX
        )
        pq_url = cq_name_urls[conf.CENTRAL_PQ2]
        q_key = conf.CENTRAL_QUEUES_COUNT_KEYS.get(conf.CENTRAL_PQ2)

        initial_shared_cache_val = self.get_shared_cache_value()

        # Mocking GroupQueuesRoutingManager().assign_group method
        routing_manager_assign_group_patch = patch(
            "utills.group_queues_routing_manager.main.GroupQueuesRoutingManager.assign_group"
        )
        routing_manager_assign_group_mock = (
            routing_manager_assign_group_patch.start()
        )
        routing_manager_assign_group_mock.return_value = {
            "is_cancle_request": True
        }

        for request in CENTRAL_2_DUMMY_CANCEL_REQUESTS:
            obj, json_body = self.create_cri_object_from_dict(request)
            json_body["ReceiptHandle"] = "some receipt handle"
            Central2().process_request(json_body, pq_url, q_key)

        self._test_cancel_request(initial_pq_count, initial_shared_cache_val)

        routing_manager_assign_group_patch.stop()

    def test_process_queue(self):
        with patch(
            "utills.sqs_manager.main.SQSManager.fetch_message_sp",
            return_value=self.central_2_dummy_requests_json_ls(),
        ):
            initial_pq_count = self.get_pq_total_count_from_cache()

            q_key = conf.CENTRAL_QUEUES_COUNT_KEYS.get(conf.CENTRAL_PQ2)
            q_name = conf.CENTRAL_PQ2
            c2_threshold = len(CENTRAL_2_DUMMY_REQUESTS)
            total_req_count = len(CENTRAL_2_DUMMY_REQUESTS)
            initial_shared_cache_val = self.get_shared_cache_value()

            self.manage_udc_api_check_mock.return_value = (
                CENTRAL_2_DUMMY_SUCCESS_UDC_RESPONSE["data"]
            )

            for request in CENTRAL_2_DUMMY_REQUESTS:
                self.create_cri_object_from_dict(request)

            Central2().process_queue(q_name, q_key, c2_threshold)

            # testing process_group as it gets called by process_queue function any ways.
            self.__test_process_group(
                initial_pq_count,
                initial_shared_cache_val,
                total_req_count,
                udc_success=True,
                udc_lock=False,
            )

    def test_run(self):
        with patch(
            "utills.sqs_manager.main.SQSManager.fetch_message_sp",
            return_value=self.central_2_dummy_requests_json_ls(),
        ):
            initial_pq_count = self.get_pq_total_count_from_cache()

            c2_threshold = len(CENTRAL_2_DUMMY_REQUESTS)
            total_req_count = len(CENTRAL_2_DUMMY_REQUESTS)
            initial_shared_cache_val = self.get_shared_cache_value()

            self.manage_udc_api_check_mock.return_value = (
                CENTRAL_2_DUMMY_SUCCESS_UDC_RESPONSE["data"]
            )

            for request in CENTRAL_2_DUMMY_REQUESTS:
                self.create_cri_object_from_dict(request)

            Central2().run((c2_threshold,))

            # testing process_group as it gets called by process_queue function any ways.
            self.__test_process_group(
                initial_pq_count,
                initial_shared_cache_val,
                total_req_count,
                udc_success=True,
                udc_lock=False,
            )

    def test_run_with_call_hold_false(self):
        sqs_patch = patch(
            "utills.sqs_manager.main.SQSManager.fetch_message_sp",
        )
        sqs_mock = sqs_patch.start()
        json_ls = []

        for request in CENTRAL_2_DUMMY_CANCEL_REQUESTS:
            data = {}
            copy_request = deepcopy(request)
            copy_request["call_hold"] = False
            data["Body"] = json.dumps(copy_request)
            data["ReceiptHandle"] = "some receipt handle"
            json_ls.append(data)

        sqs_mock.return_value = json_ls
        initial_pq_count = (
            self.get_pq_total_count_from_cache()
        )  # setting cache values for priority queues

        c2_threshold = len(CENTRAL_2_DUMMY_CANCEL_REQUESTS)
        total_req_count = len(CENTRAL_2_DUMMY_CANCEL_REQUESTS)
        initial_shared_cache_val = (
            self.get_shared_cache_value()
        )  # initial shared cache value to compare

        self.manage_udc_api_check_mock.return_value = (
            {}
        )  # setting udc response to udc unavailable

        for request in CENTRAL_2_DUMMY_CANCEL_REQUESTS:
            self.create_cri_object_from_dict(request)

        Central2().run((c2_threshold,))

        self.assertEqual(
            self.manage_udc_api_check_mock.call_count,
            total_req_count,
            "Manage udc api check wasn't done for all requests!!",
        )
        self.sqs_send_message_to_sqs_mock.assert_not_called(),

        # whether the requests has been deleted from the pq queues
        self.assertEqual(
            self.sqs_delete_message_mock.call_count,
            len(CENTRAL_2_DUMMY_CANCEL_REQUESTS),
            "All requests weren't deleted from priority queue",
        )

        self._test_cancel_request(initial_pq_count, initial_shared_cache_val)
        sqs_mock = sqs_patch.stop()
