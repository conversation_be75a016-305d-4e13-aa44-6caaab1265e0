from typing import Dict

from django.conf import settings

import pytest

from utills.cache_manager.main import CacheManager


@pytest.fixture
def setup_priority_queues_cache():
    def wrap(cache_mapping: Dict):
        CacheManager.set_value(
            settings.CENTRAL_PQ2_COUNT_KEY,
            cache_mapping.get(settings.CENTRAL_PQ2_COUNT_KEY, 0),
        )

        CacheManager.set_value(
            settings.CENTRAL_PQ3_COUNT_KEY,
            cache_mapping.get(settings.CENTRAL_PQ3_COUNT_KEY, 0),
        )

    yield wrap
