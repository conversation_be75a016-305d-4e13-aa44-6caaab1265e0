import json

from django.conf import settings
from rest_framework import status

import pytest
from moto import mock_aws

from handlers.central_2.main_v2 import Central2_V2
from handlers.models import CentralRequestInfo, Group, IvrInfo, Number
from test_suite.factories.groups import (
    GroupFactory,
    GroupIvrInfoFactory,
    NumberFactory,
)
from test_suite.test.factories import IvrInfoFactory
from utills.cache_manager.main import CacheManager
from utills.shared_cache_manager.main import SharedCacheManager
from utills.sqs_manager.sqs_event import CallRequestSqsEvent


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_central_2_v2_get_cri_obj(
    load_json, create_cri_object, mock_create_queue
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )
    sqs_client, res = mock_create_queue(settings.CENTRAL_PQ2)
    mock_create_queue(settings.CENTRAL_PQ3)
    mock_create_queue(settings.ON_HOLD_Q_NAME)
    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(raw_request, res["QueueUrl"], "test")
    handler = Central2_V2()
    assert not handler.get_cri_obj(sqs_event.request_id)
    create_cri_object(sqs_event.body)
    assert handler.get_cri_obj(sqs_event.request_id)


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_central_2_v2_delete_request(
    load_json, mock_create_queue, setup_priority_queues_cache
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )
    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    mock_create_queue(settings.CENTRAL_PQ3)
    mock_create_queue(settings.ON_HOLD_Q_NAME)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})
    handler = Central2_V2()
    handler.delete_request(sqs_event)

    assert not sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(sqs_event.queue_cache_key)


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_central_2_v2_send_to_hold_if_call_hold_is_true(
    load_json,
    mock_create_queue,
    create_cri_object,
    setup_priority_queues_cache,
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )
    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    mock_create_queue(settings.CENTRAL_PQ3)
    _, ohq_res = mock_create_queue(settings.ON_HOLD_Q_NAME)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})

    cri_obj = create_cri_object(sqs_event.body)

    handler = Central2_V2()
    assert handler.send_to_hold(sqs_event, cri_obj)

    assert not sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(sqs_event.queue_cache_key)

    ohq_request = sqs_client.receive_message(
        QueueUrl=ohq_res["QueueUrl"],
    )[
        "Messages"
    ][0]

    ohq_event = CallRequestSqsEvent(
        ohq_request, ohq_res["QueueUrl"], settings.ON_HOLD_COUNT_KEY
    )

    assert CacheManager.get_value(ohq_event.queue_cache_key)

    assert ohq_event.body == sqs_event.body


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_central_2_v2_send_to_hold_if_call_hold_is_false(
    load_json,
    mock_create_queue,
    create_cri_object,
    setup_priority_queues_cache,
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )
    request_body["call_hold"] = False
    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    mock_create_queue(settings.CENTRAL_PQ3)
    _, ohq_res = mock_create_queue(settings.ON_HOLD_Q_NAME)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})

    cri_obj = create_cri_object(sqs_event.body)

    handler = Central2_V2()
    assert not handler.send_to_hold(sqs_event, cri_obj)

    assert sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert CacheManager.get_value(sqs_event.queue_cache_key)

    assert not sqs_client.receive_message(
        QueueUrl=ohq_res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(settings.ON_HOLD_COUNT_KEY)


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_central_2_v2_on_destination_failure(
    load_json,
    mock_create_queue,
    create_cri_object,
    setup_priority_queues_cache,
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )

    obd_api_res = load_json("external_apis/obd_internal_api/400_response.json")

    _, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    mock_create_queue(settings.CENTRAL_PQ3)
    mock_create_queue(settings.ON_HOLD_Q_NAME)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})

    cri_obj: CentralRequestInfo = create_cri_object(sqs_event.body)

    handler = Central2_V2()

    handler.on_destination_failure(
        sqs_event, cri_obj, obd_api_res, str(status.HTTP_400_BAD_REQUEST)
    )

    canceled_request_key = "{version}{prefix}{key}".format(
        version=settings.REDIS_VERSION_KEY,
        prefix=settings.CANCELED_REQUEST_KEY,
        key=cri_obj.request_id,
    )

    assert SharedCacheManager.get_value(canceled_request_key) == "1"

    assert not sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(sqs_event.queue_cache_key)

    assert json.loads(
        sqs_client.receive_message(
            QueueUrl=pm_res["QueueUrl"],
        )["Messages"][
            0
        ]["Body"]
    ) == {
        "company_id": cri_obj.c_id,
        "context": "service_invoker",
        "ivr_id": cri_obj.ivr_id,
        "request_id": cri_obj.request_id,
        "response": {
            "emitter": settings.OBD_SERVICES["source_name"],
            "failure_reason": json.dumps(obd_api_res),
            "message": "",
        },
        "response_status": str(status.HTTP_400_BAD_REQUEST),
        "service_name": settings.OBD_SERVICES["cancel_service"],
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_central_2_v2_on_destination_success(
    load_json,
    mock_create_queue,
    create_cri_object,
    setup_priority_queues_cache,
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )

    obd_api_res = load_json(
        "external_apis/obd_internal_api/success_response.json"
    )

    _, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    mock_create_queue(settings.CENTRAL_PQ3)
    mock_create_queue(settings.ON_HOLD_Q_NAME)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})

    cri_obj: CentralRequestInfo = create_cri_object(sqs_event.body)

    handler = Central2_V2()
    handler.request_handler.setup(cri_obj)

    handler.on_destination_success(
        sqs_event,
        handler.request_handler.udc_helper,
        cri_obj,
        obd_api_res,
    )

    canceled_request_key = "{version}{prefix}{key}".format(
        version=settings.REDIS_VERSION_KEY,
        prefix=settings.CANCELED_REQUEST_KEY,
        key=cri_obj.request_id,
    )

    assert not SharedCacheManager.get_value(canceled_request_key)

    assert not sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(sqs_event.queue_cache_key)

    assert handler.request_handler.udc_helper.cache.get_value(
        handler.request_handler.udc_helper.cache.key
    )

    assert json.loads(
        sqs_client.receive_message(
            QueueUrl=pm_res["QueueUrl"],
        )["Messages"][
            0
        ]["Body"]
    ) == {
        "company_id": cri_obj.c_id,
        "context": "service_invoker",
        "ivr_id": cri_obj.ivr_id,
        "request_id": cri_obj.request_id,
        "response": {
            "emitter": settings.OBD_SERVICES["source_name"],
            "failure_reason": "",
            "message": json.dumps(obd_api_res),
        },
        "response_status": str(status.HTTP_200_OK),
        "service_name": settings.OBD_SERVICES["source_name"],
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_central_2_v2_on_udc_unavailable_if_send_to_hold_is_true(
    load_json,
    mock_create_queue,
    create_cri_object,
    setup_priority_queues_cache,
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )

    _, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )
    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    mock_create_queue(settings.CENTRAL_PQ3)
    _, ohq_res = mock_create_queue(settings.ON_HOLD_Q_NAME)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})

    cri_obj = create_cri_object(sqs_event.body)

    handler = Central2_V2()
    handler.on_udc_unavailable(sqs_event, cri_obj)

    assert not sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(sqs_event.queue_cache_key)

    ohq_request = sqs_client.receive_message(
        QueueUrl=ohq_res["QueueUrl"],
    )[
        "Messages"
    ][0]

    ohq_event = CallRequestSqsEvent(
        ohq_request, ohq_res["QueueUrl"], settings.ON_HOLD_COUNT_KEY
    )

    assert CacheManager.get_value(ohq_event.queue_cache_key)

    assert ohq_event.body == sqs_event.body

    assert not CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        is_req_completed=CentralRequestInfo.YES,
        is_enable=CentralRequestInfo.NO,
    ).exists()

    assert not sqs_client.receive_message(
        QueueUrl=pm_res["QueueUrl"],
    ).get("Messages")


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_central_2_v2_on_udc_unavailable_if_send_to_hold_is_false(
    load_json,
    mock_create_queue,
    create_cri_object,
    setup_priority_queues_cache,
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )
    request_body["call_hold"] = False

    _, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )
    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    mock_create_queue(settings.CENTRAL_PQ3)
    _, ohq_res = mock_create_queue(settings.ON_HOLD_Q_NAME)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})

    cri_obj: CentralRequestInfo = create_cri_object(sqs_event.body)

    handler = Central2_V2()
    handler.on_udc_unavailable(sqs_event, cri_obj)

    assert not sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(sqs_event.queue_cache_key)

    assert not sqs_client.receive_message(
        QueueUrl=ohq_res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(settings.ON_HOLD_COUNT_KEY)

    assert CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        is_req_completed=CentralRequestInfo.YES,
        is_enable=CentralRequestInfo.NO,
    ).exists()

    assert json.loads(
        sqs_client.receive_message(
            QueueUrl=pm_res["QueueUrl"],
        )["Messages"][
            0
        ]["Body"]
    ) == {
        "request_id": cri_obj.request_id,
        "company_id": cri_obj.c_id,
        "ivr_id": cri_obj.ivr_id,
        "response": {
            "emitter": settings.OBD_SERVICES["source_name"],
            "failure_reason": "obd_central group not found!",
            "message": "",
        },
        "response_status": str(status.HTTP_404_NOT_FOUND),
        "service_name": settings.OBD_SERVICES["cancel_service"],
        "context": "service_invoker",
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_central_2_v2_process_request_if_request_not_found_in_db(
    load_json,
    mock_create_queue,
    setup_priority_queues_cache,
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )

    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    _, ohq_res = mock_create_queue(settings.ON_HOLD_Q_NAME)
    mock_create_queue(settings.CENTRAL_PQ3)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    setup_priority_queues_cache({settings.CENTRAL_PQ2_COUNT_KEY: 1})

    handler = Central2_V2()

    handler.process_request(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    assert not sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(settings.CENTRAL_PQ2_COUNT_KEY)

    assert not sqs_client.receive_message(
        QueueUrl=ohq_res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(settings.ON_HOLD_COUNT_KEY)


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_central_2_v2_process_request_if_group_not_available(
    load_json,
    mock_create_queue,
    setup_priority_queues_cache,
    create_cri_object,
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )
    _, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    _, ohq_res = mock_create_queue(settings.ON_HOLD_Q_NAME)
    mock_create_queue(settings.CENTRAL_PQ3)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})

    cri_obj: CentralRequestInfo = create_cri_object(sqs_event.body)
    IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    handler = Central2_V2()

    handler.process_request(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    assert sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert CacheManager.get_value(settings.CENTRAL_PQ2_COUNT_KEY)

    assert not sqs_client.receive_message(
        QueueUrl=ohq_res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(settings.ON_HOLD_COUNT_KEY)

    assert not CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        is_req_completed=CentralRequestInfo.YES,
        is_enable=CentralRequestInfo.NO,
    ).exists()

    assert not sqs_client.receive_message(
        QueueUrl=pm_res["QueueUrl"],
    ).get("Messages")


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_central_2_v2_process_request_if_group_not_available_with_cancel_request(
    load_json,
    mock_create_queue,
    setup_priority_queues_cache,
    create_cri_object,
):
    request_body = load_json(
        "central_request_info/type_2.json",
    )

    request_body["obd_v2_settings"]["failover"] = 0

    _, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )

    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    _, ohq_res = mock_create_queue(settings.ON_HOLD_Q_NAME)
    mock_create_queue(settings.CENTRAL_PQ3)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})

    cri_obj: CentralRequestInfo = create_cri_object(sqs_event.body)
    IvrInfoFactory(ivr_id=cri_obj.ivr_id)

    handler = Central2_V2()

    handler.process_request(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    assert not sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(settings.CENTRAL_PQ2_COUNT_KEY)

    assert not sqs_client.receive_message(
        QueueUrl=ohq_res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(settings.ON_HOLD_COUNT_KEY)

    assert CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        is_req_completed=CentralRequestInfo.YES,
        is_enable=CentralRequestInfo.NO,
    ).exists()

    assert json.loads(
        sqs_client.receive_message(
            QueueUrl=pm_res["QueueUrl"],
        )["Messages"][
            0
        ]["Body"]
    ) == {
        "request_id": cri_obj.request_id,
        "company_id": cri_obj.c_id,
        "ivr_id": cri_obj.ivr_id,
        "response": {
            "emitter": settings.OBD_SERVICES["source_name"],
            "failure_reason": "obd_central group not found!",
            "message": "",
        },
        "response_status": str(status.HTTP_404_NOT_FOUND),
        "service_name": settings.OBD_SERVICES["cancel_service"],
        "context": "service_invoker",
    }


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture,ivr_type,ivr_settings",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_not_available.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 0, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_not_available.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_not_available.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 1, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_not_available.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
    ],
)
def test_central_2_v2_process_request_on_udc_unavailable(
    cri_request_fixture,
    udc_fixture,
    ivr_type,
    ivr_settings,
    load_json,
    mock_create_queue,
    setup_priority_queues_cache,
    create_cri_object,
    mock_udc_apis,
):
    request_body = load_json(cri_request_fixture)

    expected_udc_res = load_json(udc_fixture)

    cri_obj: CentralRequestInfo = create_cri_object(request_body)

    ivr = IvrInfoFactory(
        ivr_id=cri_obj.ivr_id, common_setting=ivr_settings, ivr_type=ivr_type
    )
    group_1 = GroupFactory(settings={"group_type": Group.AWS_GROUP_TYPE})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    mock_udc_apis(
        cri_obj,
        {
            "response": expected_udc_res,
            "status_code": status.HTTP_400_BAD_REQUEST,
        },
        user_view_only=int(
            not ivr.common_setting.get(settings.BOOK_USER_TOGGLE_KEY)
        ),
        user_is_avail=ivr.common_setting.get(settings.UDC_API_TOGGLE_KEY),
    )

    _, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )
    _, ohq_res = mock_create_queue(settings.ON_HOLD_Q_NAME)

    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    mock_create_queue(settings.CENTRAL_PQ3)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})

    handler = Central2_V2()

    handler.process_request(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    assert not sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(sqs_event.queue_cache_key)

    ohq_request = sqs_client.receive_message(
        QueueUrl=ohq_res["QueueUrl"],
    )[
        "Messages"
    ][0]

    ohq_event = CallRequestSqsEvent(
        ohq_request, ohq_res["QueueUrl"], settings.ON_HOLD_COUNT_KEY
    )

    assert CacheManager.get_value(ohq_event.queue_cache_key)

    assert ohq_event.body == sqs_event.body

    assert not CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        is_req_completed=CentralRequestInfo.YES,
        is_enable=CentralRequestInfo.NO,
    ).exists()

    assert not sqs_client.receive_message(
        QueueUrl=pm_res["QueueUrl"],
    ).get("Messages")


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture,ivr_type,ivr_settings",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 0, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 1, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
    ],
)
def test_central_2_v2_process_request_on_destination_success(
    cri_request_fixture,
    udc_fixture,
    ivr_type,
    ivr_settings,
    load_json,
    mock_create_queue,
    setup_priority_queues_cache,
    create_cri_object,
    mock_udc_apis,
    mock_obd_internal_api,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
    setup_truecaller_enabled,
):
    # Mock the TrueCaller APIs
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()
    request_body = load_json(cri_request_fixture)

    expected_udc_res = load_json(udc_fixture)

    expected_obd_res = load_json(
        "external_apis/obd_internal_api/success_response.json"
    )

    cri_obj: CentralRequestInfo = create_cri_object(request_body)

    ivr = IvrInfoFactory(
        ivr_id=cri_obj.ivr_id, common_setting=ivr_settings, ivr_type=ivr_type
    )
    group_1 = GroupFactory(settings={"group_type": Group.AWS_GROUP_TYPE})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    if expected_udc_res["data"].get("user_data"):
        expected_udc_res["data"]["user_data"]["uuid"] = cri_obj.user_id
        expected_udc_res["data"]["user_data"]["company"] = cri_obj.c_id

    expected_udc_res["data"]["channel_data"] = 5

    mock_udc_apis(
        cri_obj,
        {
            "response": expected_udc_res,
            "status_code": status.HTTP_200_OK,
        },
        user_view_only=int(
            not ivr.common_setting.get(settings.BOOK_USER_TOGGLE_KEY)
        ),
        user_is_avail=ivr.common_setting.get(settings.UDC_API_TOGGLE_KEY),
    )

    mock_res = mock_obd_internal_api(api_response=expected_obd_res)

    _, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )
    _, ohq_res = mock_create_queue(settings.ON_HOLD_Q_NAME)

    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    mock_create_queue(settings.CENTRAL_PQ3)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})

    handler = Central2_V2()

    handler.process_request(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    canceled_request_key = "{version}{prefix}{key}".format(
        version=settings.REDIS_VERSION_KEY,
        prefix=settings.CANCELED_REQUEST_KEY,
        key=cri_obj.request_id,
    )

    assert not SharedCacheManager.get_value(canceled_request_key)

    assert not sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(sqs_event.queue_cache_key)

    assert handler.request_handler.udc_helper.cache.get_value(
        handler.request_handler.udc_helper.cache.key
    )

    assert json.loads(
        sqs_client.receive_message(
            QueueUrl=pm_res["QueueUrl"],
        )["Messages"][
            0
        ]["Body"]
    ) == {
        "company_id": cri_obj.c_id,
        "context": "service_invoker",
        "ivr_id": cri_obj.ivr_id,
        "request_id": cri_obj.request_id,
        "response": {
            "emitter": settings.OBD_SERVICES["source_name"],
            "failure_reason": "",
            "message": json.dumps(expected_obd_res),
        },
        "response_status": str(status.HTTP_200_OK),
        "service_name": settings.OBD_SERVICES["source_name"],
    }

    assert mock_res.call_count == 1
    assert token_resp.call_count == 1
    assert resp.call_count == 1


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture,ivr_type,ivr_settings",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 0, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 1, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
    ],
)
def test_central_2_v2_process_request_on_destination_api_failure_400_request(
    cri_request_fixture,
    udc_fixture,
    ivr_type,
    ivr_settings,
    load_json,
    mock_create_queue,
    setup_priority_queues_cache,
    create_cri_object,
    mock_udc_apis,
    mock_obd_internal_api,
    mock_true_caller_token_api,
    mock_true_caller_dial_assist_api,
):
    # Mock the TrueCaller APIs
    token_resp = mock_true_caller_token_api()
    resp = mock_true_caller_dial_assist_api()
    request_body = load_json(cri_request_fixture)

    expected_udc_res = load_json(udc_fixture)

    expected_obd_res = load_json(
        "external_apis/obd_internal_api/400_response.json"
    )

    cri_obj: CentralRequestInfo = create_cri_object(request_body)

    ivr = IvrInfoFactory(
        ivr_id=cri_obj.ivr_id, common_setting=ivr_settings, ivr_type=ivr_type
    )
    group_1 = GroupFactory(settings={"group_type": Group.AWS_GROUP_TYPE})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    if expected_udc_res["data"].get("user_data"):
        expected_udc_res["data"]["user_data"]["uuid"] = cri_obj.user_id
        expected_udc_res["data"]["user_data"]["company"] = cri_obj.c_id

    expected_udc_res["data"]["channel_data"] = 5

    mock_udc_apis(
        cri_obj,
        {
            "response": expected_udc_res,
            "status_code": status.HTTP_200_OK,
        },
        user_view_only=int(
            not ivr.common_setting.get(settings.BOOK_USER_TOGGLE_KEY)
        ),
        user_is_avail=ivr.common_setting.get(settings.UDC_API_TOGGLE_KEY),
    )

    mock_res = mock_obd_internal_api(
        api_response=expected_obd_res, status_code=status.HTTP_400_BAD_REQUEST
    )

    _, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )
    _, ohq_res = mock_create_queue(settings.ON_HOLD_Q_NAME)

    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    mock_create_queue(settings.CENTRAL_PQ3)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})

    handler = Central2_V2()

    handler.process_request(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    canceled_request_key = "{version}{prefix}{key}".format(
        version=settings.REDIS_VERSION_KEY,
        prefix=settings.CANCELED_REQUEST_KEY,
        key=cri_obj.request_id,
    )

    assert SharedCacheManager.get_value(canceled_request_key) == "1"

    assert not sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(sqs_event.queue_cache_key)

    assert json.loads(
        sqs_client.receive_message(
            QueueUrl=pm_res["QueueUrl"],
        )["Messages"][
            0
        ]["Body"]
    ) == {
        "company_id": cri_obj.c_id,
        "context": "service_invoker",
        "ivr_id": cri_obj.ivr_id,
        "request_id": cri_obj.request_id,
        "response": {
            "emitter": settings.OBD_SERVICES["source_name"],
            "failure_reason": json.dumps(expected_obd_res),
            "message": "",
        },
        "response_status": str(status.HTTP_400_BAD_REQUEST),
        "service_name": settings.OBD_SERVICES["cancel_service"],
    }

    assert mock_res.call_count == 1
    assert token_resp.call_count == 0
    assert resp.call_count == 0


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "cri_request_fixture,udc_fixture,ivr_type,ivr_settings",
    [
        (
            "central_request_info/type_2.json",
            "external_apis/udc/channel_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 0, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_2.json",
            "external_apis/udc/dc_success.json",
            IvrInfo.IVR,
            {settings.UDC_API_TOGGLE_KEY: 1, settings.BOOK_USER_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.COC,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 1, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 1},
        ),
        (
            "central_request_info/type_1.json",
            "external_apis/udc/user_view_only_enabled_success.json",
            IvrInfo.P2P,
            {settings.BOOK_USER_TOGGLE_KEY: 0, settings.UDC_API_TOGGLE_KEY: 0},
        ),
    ],
)
def test_central_2_v2_process_request_on_destination_api_failure_500_request(
    cri_request_fixture,
    udc_fixture,
    ivr_type,
    ivr_settings,
    load_json,
    mock_create_queue,
    setup_priority_queues_cache,
    create_cri_object,
    mock_udc_apis,
    mock_obd_internal_api,
):
    request_body = load_json(cri_request_fixture)

    expected_udc_res = load_json(udc_fixture)

    expected_obd_res = load_json(
        "external_apis/obd_internal_api/500_response.json"
    )

    cri_obj: CentralRequestInfo = create_cri_object(request_body)

    ivr = IvrInfoFactory(
        ivr_id=cri_obj.ivr_id, common_setting=ivr_settings, ivr_type=ivr_type
    )
    group_1 = GroupFactory(settings={"group_type": Group.AWS_GROUP_TYPE})
    number_1 = NumberFactory(
        is_fix_did=Number.YES,
        number="9999999990",
        group=group_1,
    )
    GroupIvrInfoFactory(ivr_info=ivr, number=number_1, group=group_1)

    if expected_udc_res["data"].get("user_data"):
        expected_udc_res["data"]["user_data"]["uuid"] = cri_obj.user_id
        expected_udc_res["data"]["user_data"]["company"] = cri_obj.c_id

    expected_udc_res["data"]["channel_data"] = 5

    mock_udc_apis(
        cri_obj,
        {
            "response": expected_udc_res,
            "status_code": status.HTTP_200_OK,
        },
        user_view_only=int(
            not ivr.common_setting.get(settings.BOOK_USER_TOGGLE_KEY)
        ),
        user_is_avail=ivr.common_setting.get(settings.UDC_API_TOGGLE_KEY),
    )

    mock_res = mock_obd_internal_api(
        api_response=expected_obd_res,
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )

    _, pm_res = mock_create_queue(
        settings.PROCESS_MANAGER_QUEUE + ".fifo",
        attrs={
            "FifoQueue": "true",  # Specify that this is a FIFO queue
        },
    )
    _, ohq_res = mock_create_queue(settings.ON_HOLD_Q_NAME)

    sqs_client, res = mock_create_queue(
        settings.CENTRAL_PQ2, attrs={"VisibilityTimeout": "0"}
    )
    mock_create_queue(settings.CENTRAL_PQ3)

    sqs_client.send_message(
        QueueUrl=res["QueueUrl"], MessageBody=json.dumps(request_body)
    )

    raw_request = sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    )[
        "Messages"
    ][0]

    sqs_event = CallRequestSqsEvent(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )

    setup_priority_queues_cache({sqs_event.queue_cache_key: 1})

    handler = Central2_V2()

    handler.process_request(
        raw_request, res["QueueUrl"], settings.CENTRAL_PQ2_COUNT_KEY
    )
    assert not sqs_client.receive_message(
        QueueUrl=res["QueueUrl"],
    ).get("Messages")

    assert not CacheManager.get_value(sqs_event.queue_cache_key)

    ohq_request = sqs_client.receive_message(
        QueueUrl=ohq_res["QueueUrl"],
    )[
        "Messages"
    ][0]

    ohq_event = CallRequestSqsEvent(
        ohq_request, ohq_res["QueueUrl"], settings.ON_HOLD_COUNT_KEY
    )

    assert CacheManager.get_value(ohq_event.queue_cache_key)

    assert ohq_event.body == sqs_event.body

    assert not CentralRequestInfo.objects.filter(
        pk=cri_obj.pk,
        is_req_completed=CentralRequestInfo.YES,
        is_enable=CentralRequestInfo.NO,
    ).exists()

    assert not sqs_client.receive_message(
        QueueUrl=pm_res["QueueUrl"],
    ).get("Messages")

    assert (
        handler.request_handler.destination.get_obd_api_avail_cache().is_failed()
    )
