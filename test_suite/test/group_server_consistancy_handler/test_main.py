from copy import deepcopy
from unittest.mock import patch

from django.test import override_settings, tag

from central.settings.handlers import conf
from handlers.group_server_consistancy_manager.main import (
    GroupServerConsistancyHandler,
)
from handlers.models import Group, GroupServerRelationship, Number, Server
from test_suite.test.base import BaseTestCase
from test_suite.test.data.group_server_relation_handler.data import (
    assigned_queues,
    c_id,
    d_id,
    get_kam_group_server_list_api_response,
    group_data_2,
    group_data_for_already_grp_exists_case,
    group_data_from_kam_api,
    kam_group_id,
    kam_group_id_for_already_case,
    operator,
    server_name_for_already_exists_case,
)


@tag("group_server_consistancy_main", "group_server_consistancy")
@override_settings(CACHEOPS_ENABLED=False)
class GroupServerConsistancyHandlerTestCase(BaseTestCase):
    kam_group_server_list_api_res: dict = (
        {}
    )  # value set from `set_talk_to_kamailio_api_response` method

    group_instance: Group = (
        None  # set group_instance value from `setUpClass` method
    )
    server_instance: Server = (
        None  # set server_instance value from `setUpClass` method
    )

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

        # create Group record
        cls.group_instance: Group = Group.objects.create(
            **group_data_for_already_grp_exists_case
        )

        # create server record
        cls.server_instance: Server = Server.objects.create(
            name=server_name_for_already_exists_case
        )

    def setUp(self):
        super().setUp()

        # variables for mock `ExternalAPIs.talk_to_kamailio_api` method (if required)
        self.talk_to_kamailio_api_patch_path: str = (
            "utills.external_apis.ExternalAPIs.talk_to_kamailio_api"
        )
        self.talk_to_kamailio_api_patch = None
        self.talk_to_kamailio_api_mock = None

        # mock `Helper.get_group_assigned_queues`
        self.get_grp_assigned_q_patch_path: str = (
            "utills.helpers.helper.Helper.get_group_assigned_queues"
        )
        self.get_grp_assigned_q_patch = patch(
            self.get_grp_assigned_q_patch_path, return_value=assigned_queues
        )
        self.get_grp_assigned_q_mock = self.get_grp_assigned_q_patch.start()

    def tearDown(self):
        super().tearDown()

        patch_vars: tuple = (
            self.talk_to_kamailio_api_patch,
            self.get_grp_assigned_q_patch,
        )

        for patch_var in patch_vars:
            if patch_var:
                patch_var.stop()

    def set_talk_to_kamailio_api_response(self, *args, **kwargs):
        if (
            conf.ROUTING_INFO_URL_NAMES["kam_group_servers_route_name"]
            == args[1]
            and kwargs["case"] == "create"
        ):
            return deepcopy(group_data_from_kam_api)

        elif (
            conf.ROUTING_INFO_URL_NAMES["kam_group_servers_route_name"]
            == args[1]
            and kwargs["case"] == "update"
        ):
            return {kam_group_id_for_already_case: group_data_2}

        elif (
            conf.ROUTING_INFO_URL_NAMES["kam_group_servers_list_route_name"]
            == args[1]
        ):
            # this function (get_kam_group_server_list_api_response) return list of ids or names because
            # Group and Server data already created into database from `GroupServerConsistancyHandler.run` method,
            # otherwise, it will return blank list of groups and servers
            self.kam_group_server_list_api_res = (
                get_kam_group_server_list_api_response()
            )
            return self.kam_group_server_list_api_res

    def get_expected_value(self, field: str):
        """ method for return expected value of Group model instance
        Args:
            field (str): Group model field\
        Returns:
            (str, int, dict, Json etc): Expected value of Group field
        """

        if field == "assigned_queues":
            return str(
                assigned_queues
            )  # typecast to str because field type is TextField in django model

        elif field == "kam_group_id":
            return int(kam_group_id_for_already_case)

        elif field == "settings":
            return {
                "c_id": c_id,
                "d_id": d_id,
                "operator": operator,
                "caller_id": "11345491",
                "last_digit": "2",
            }

    # ################################################### TEST CAESE ##############################################
    def test_run_if_group_not_exists(self):
        # mock `Helper.get_group_assigned_queues` method
        self.talk_to_kamailio_api_patch = patch(
            self.talk_to_kamailio_api_patch_path
        )
        self.talk_to_kamailio_api_mock = (
            self.talk_to_kamailio_api_patch.start()
        )

        # set response of ExternalAPIs.talk_to_kamailio_api dynamically
        self.talk_to_kamailio_api_mock.side_effect = (
            lambda *args, **kwargs: self.set_talk_to_kamailio_api_response(
                *args, **kwargs, case="create"
            )
        )

        # make duplicate copy of group_data for testing/matching response
        group_data_from_kam_api_copy: dict = deepcopy(group_data_from_kam_api)

        servers: list = group_data_from_kam_api_copy[kam_group_id]["servers"]
        expected_group_name: str = group_data_from_kam_api_copy[kam_group_id][
            "name"
        ]
        expected_group_alias: str = group_data_from_kam_api_copy[kam_group_id][
            "group_alias"
        ]
        shared_did: str = f"{group_data_from_kam_api_copy[kam_group_id]['caller_id']}{group_data_from_kam_api_copy[kam_group_id]['last_digit']}"

        # calling `GroupServerConsistancyHandler.run`
        GroupServerConsistancyHandler.run()

        # check Group created or not
        self.assertTrue(
            Group.objects.filter(
                name=expected_group_name, group_alias=expected_group_alias
            ).exists(),
            f"Group data of group `{expected_group_name}` not created",
        )

        # check Server created or not
        for server in servers:
            self.assertTrue(
                Server.objects.filter(name=server).exists(),
                f"Server data of name `{server}` not created",
            )

        # check Number created or not
        self.assertTrue(
            Number.objects.filter(number=shared_did).exists(),
            "Number object not created from manage_default_number",
        )

        # check Group and Server Relation created or not
        group_instance: Group = Group.objects.get(
            name=expected_group_name, group_alias=expected_group_alias
        )
        for server in servers:
            server_instance: Server = Server.objects.get(name=server)
            self.assertTrue(
                GroupServerRelationship.objects.filter(
                    group=group_instance.id, server=server_instance.id
                ).exists(),
                f"GroupServerRelationship object not created for group_id {group_instance.id} and server_id {server_instance.id}",
            )

        # check inactive groups disabled or not
        active_group_ids: list = self.kam_group_server_list_api_res["groups"]
        self.assertFalse(
            Group.objects.filter(
                kam_group_id__in=active_group_ids, is_enable=Group.YES
            ).exists(),
            f"Groups of kam_group_ids: {active_group_ids} not disabled",
        )

        # check inactive servers disabled or not
        inactive_server_names: list = self.kam_group_server_list_api_res[
            "inactive_servers"
        ]
        self.assertFalse(
            Server.objects.filter(
                name__in=inactive_server_names, is_enable=Server.YES
            ).exists(),
            f"Server of names: {inactive_server_names} not disabled",
        )

    def test_run_if_group_already_exists(self):
        # mock `Helper.get_group_assigned_queues` method
        self.talk_to_kamailio_api_patch = patch(
            self.talk_to_kamailio_api_patch_path
        )
        self.talk_to_kamailio_api_mock = (
            self.talk_to_kamailio_api_patch.start()
        )

        # set response of ExternalAPIs.talk_to_kamailio_api dynamically
        self.talk_to_kamailio_api_mock.side_effect = (
            lambda *args, **kwargs: self.set_talk_to_kamailio_api_response(
                *args, **kwargs, case="update"
            )
        )

        # make duplicate copy of group_data for testing/matching response
        group_data_from_kam_api_copy: dict = deepcopy(group_data_2)
        group_data_2_copy: dict = deepcopy(group_data_2)

        servers: list = group_data_from_kam_api_copy["servers"]
        expected_group_name: str = group_data_from_kam_api_copy["name"]
        expected_group_alias: str = group_data_from_kam_api_copy["group_alias"]
        shared_did: str = f"{group_data_from_kam_api_copy['caller_id']}{group_data_from_kam_api_copy['last_digit']}"

        # calling `GroupServerConsistancyHandler.run`
        GroupServerConsistancyHandler.run()

        # check group updated or not
        self.group_instance.refresh_from_db()  # refresh group instance

        group_fields: tuple = (
            "name",
            "region",
            "group_alias",
            "assigned_queues",
            "kam_group_id",
            "settings",
        )

        for field in group_fields:
            self.assertEqual(
                getattr(
                    self.group_instance, field
                ),  # instance attribute / property value
                group_data_2_copy.get(
                    field, self.get_expected_value(field)
                ),  # expected value of field
                f"Expected value of `{field}` is {group_data_2_copy.get(field, self.get_expected_value(field))}, but actual value is {getattr(self.group_instance, field)}",
            )

        # check Server created or not
        for server in servers:
            self.assertTrue(
                Server.objects.filter(name=server).exists(),
                f"Server data of name `{server}` not created",
            )

        # check Number created or not
        self.assertTrue(
            Number.objects.filter(number=shared_did).exists(),
            "Number object not created from manage_default_number",
        )

        # check Group and Server Relation created or not
        group_instance: Group = Group.objects.get(
            name=expected_group_name, group_alias=expected_group_alias
        )
        for server in servers:
            server_instance: Server = Server.objects.get(name=server)
            self.assertTrue(
                GroupServerRelationship.objects.filter(
                    group=group_instance.id, server=server_instance.id
                ).exists(),
                f"GroupServerRelationship object not created for group_id {group_instance.id} and server_id {server_instance.id}",
            )

        # check inactive groups disabled or not
        active_group_ids: list = self.kam_group_server_list_api_res["groups"]
        self.assertFalse(
            Group.objects.filter(
                kam_group_id__in=active_group_ids, is_enable=Group.YES
            ).exists(),
            f"Groups of kam_group_ids: {active_group_ids} not disabled",
        )

        # check inactive servers disabled or not
        inactive_server_names: list = self.kam_group_server_list_api_res[
            "inactive_servers"
        ]
        self.assertFalse(
            Server.objects.filter(
                name__in=inactive_server_names, is_enable=Server.YES
            ).exists(),
            f"Server of names: {inactive_server_names} not disabled",
        )
