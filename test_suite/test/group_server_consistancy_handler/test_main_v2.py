from django.conf import settings

import boto3
import pytest
from moto import mock_aws

from handlers.group_server_consistancy_manager.main_v2 import (
    GroupServerConsistancyHandler_V2,
)
from handlers.models import (
    Group,
    GroupServerRelationship,
    Server,
    SQSQueueInfo,
)
from test_suite.factories.groups import GroupFactory, ServerFactory


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gsc_handler_v2_create_or_update_groups_new_group(
    load_json, mock_kam_group_servers_api, mock_fix_did_pilot_list_api
):
    kam_api_response = load_json("external_apis/kamailio/group_servers.json")
    mock_kam_group_servers_api(api_response=kam_api_response)

    kam_group_id = list(kam_api_response["result"].keys())[0]
    group_data = kam_api_response["result"][kam_group_id]

    mock_fix_did_pilot_list_api(
        group_data["pilot_number"],
        server_group=Group.AWS_GROUP_TYPE,
    )

    GroupServerConsistancyHandler_V2.create_or_update_groups()

    sqs_client = boto3.client("sqs", region_name="ap-south-1")

    group_servers = group_data.pop("servers")

    assert not sqs_client.list_queues(
        QueueNamePrefix=settings.GROUPS_GATEWAY
    ).get("QueueUrls")

    assert not SQSQueueInfo.objects.filter(
        gateway_prefix=settings.GROUPS_GATEWAY
    ).exists()
    group_data["group_type"] = Group.AWS_GROUP_TYPE

    assert Group.objects.filter(
        assigned_queues={},
        name=group_data.pop("name"),
        group_alias=group_data.pop("group_alias"),
        region=group_data.pop("region"),
        is_enable=Group.YES,
        is_default=Group.NO,
        kam_group_id=kam_group_id,
        settings=group_data,
    ).exists()

    for server in group_servers:
        assert Server.objects.filter(
            name=server, is_enable=Server.YES
        ).exists()

        GroupServerRelationship.objects.filter(
            group__kam_group_id=kam_group_id, server__name=server
        ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gsc_handler_v2_create_or_update_groups_pilot_number_not_found(
    load_json, mock_kam_group_servers_api, mock_fix_did_pilot_list_api
):
    kam_api_response = load_json("external_apis/kamailio/group_servers.json")

    kam_group_id = list(kam_api_response["result"].keys())[0]
    group_data = kam_api_response["result"][kam_group_id]

    mock_fix_did_pilot_list_api(
        group_data["pilot_number"],
        server_group=Group.AWS_GROUP_TYPE,
    )
    del kam_api_response["result"][kam_group_id]["pilot_number"]
    mock_kam_group_servers_api(api_response=kam_api_response)

    GroupServerConsistancyHandler_V2.create_or_update_groups()

    group_servers = group_data.pop("servers")

    assert not Group.objects.all().exists()
    assert not Server.objects.all().exists()
    assert not GroupServerRelationship.objects.all().exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
@pytest.mark.parametrize(
    "group_type",
    [Group.PHY_GROUP_TYPE, Group.AWS_GROUP_TYPE, Group.PHY_AWS_GROUP_TYPE],
)
def test_gsc_handler_v2_create_or_update_groups_existing_group(
    group_type,
    load_json,
    mock_kam_group_servers_api,
    mock_fix_did_pilot_list_api,
):
    kam_api_response = load_json("external_apis/kamailio/group_servers.json")
    mock_kam_group_servers_api(api_response=kam_api_response)

    kam_group_id = list(kam_api_response["result"].keys())[0]
    group_data = kam_api_response["result"][kam_group_id]

    GroupFactory(
        kam_group_id=kam_group_id,
    )

    mock_fix_did_pilot_list_api(
        group_data["pilot_number"],
        server_group=group_type,
    )

    GroupServerConsistancyHandler_V2.create_or_update_groups()

    group_servers = group_data.pop("servers")
    group_data["group_type"] = group_type
    assert Group.objects.all().count() == 1

    qs = Group.objects.filter(
        name=group_data.pop("name"),
        group_alias=group_data.pop("group_alias"),
        region=group_data.pop("region"),
        is_enable=Group.YES,
        is_default=Group.NO,
        kam_group_id=kam_group_id,
        settings=group_data,
    )
    assert qs.exists()

    for server in group_servers:
        assert Server.objects.filter(
            name=server, is_enable=Server.YES
        ).exists()

        GroupServerRelationship.objects.filter(
            group__kam_group_id=kam_group_id, server__name=server
        ).exists()


@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_gsc_handler_v2_disable_group_servers(
    load_json, mock_kam_group_servers_list_api
):
    kam_api_response = load_json(
        "external_apis/kamailio/group_servers_list.json"
    )
    mock_kam_group_servers_list_api(api_response=kam_api_response)

    existing_group = GroupFactory(kam_group_id=999, is_enable=Group.YES)

    existing_server = ServerFactory(name="test.test.com", is_enable=Server.YES)

    for group in kam_api_response["result"]["groups"]:
        GroupFactory(kam_group_id=group, is_enable=Group.YES)

    for server in kam_api_response["result"]["servers"]:
        ServerFactory(name=server, is_enable=Server.YES)

    GroupServerConsistancyHandler_V2.disable_group_servers()

    assert Group.objects.filter(is_enable=Group.NO).count() == 1
    assert Server.objects.filter(is_enable=Group.NO).count() == 1
    assert Group.objects.filter(is_enable=Group.NO).first() == existing_group
    assert Server.objects.filter(is_enable=Group.NO).first() == existing_server
