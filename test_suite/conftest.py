import json
import os
from copy import deepcopy
from typing import Dict, Generator, List

from django.conf import settings
from django.core.cache import cache
from rest_framework import status

import boto3
import pytest
import responses as responses_
from moto import mock_aws
from moto.core.models import override_responses_real_send
from responses import matchers

from handlers.models import CentralRequestInfo, IvrInfo
from test_suite.factories.cri import CentralRequestInfoFactory
from utills.external_apis import TrueCallerApi
from utills.helpers.helper import Helper
from utills.shared_cache_manager.conf import SHARED_REDIS_CON
from utills.sqs_manager import conf as sqs_conf

TRUE_CALLER_TOKEN = "new_token_from_api"


@pytest.fixture
def load_json():
    """Pass path from the folder tests/fixtures"""

    def wrap(path):
        with open(
            os.path.join(settings.BASE_DIR, f"test_suite/test/fixtures/{path}")
        ) as f:
            return json.load(f)

    return wrap


@pytest.fixture
def responses() -> Generator["responses_.RequestsMock", None, None]:
    with responses_.RequestsMock(assert_all_requests_are_fired=False) as res:
        override_responses_real_send(res)

        yield res
        override_responses_real_send(res)


@pytest.fixture
def mock_channel_api(responses):
    def wrap(company_id, udc_response):
        url = settings.UDC_CHANNEL_API_URL.format(
            company_id=company_id, udc_base=settings.UDC_API_BASE_URL
        )

        responses.add(
            responses.GET,
            url,
            json=udc_response["response"],
            status=udc_response["status_code"],
        )

    yield wrap


@pytest.fixture
def mock_uc_api(responses):
    def wrap(
        company_id,
        user_id,
        ivr_id,
        user_is_avail,
        user_view_only,
        udc_response,
    ):
        ivr_info = IvrInfo.objects.filter(ivr_id=ivr_id).first()

        url = settings.UDC_USER_CHANNEL_API_URL.format(
            user_id=user_id,
            company_id=company_id,
            udc_base=settings.UDC_API_BASE_URL,
            user_is_avail=user_is_avail,
            user_view_only=user_view_only,
            ivr_type=ivr_info.ivr_type,
        )

        responses.add(
            responses.GET,
            url,
            json=udc_response["response"],
            status=udc_response["status_code"],
        )

    yield wrap


@pytest.fixture
def mock_dc_api(responses):
    def wrap(
        company_id,
        ivr_id,
        is_user_required,
        user_book_duration,
        user_view_only,
        udc_response,
    ):
        ivr_info = IvrInfo.objects.filter(ivr_id=ivr_id).first()
        url = settings.UDC_USER_DEPARTMENT_API_URL.format(
            ivr_id=ivr_id,
            company_id=company_id,
            udc_base=settings.UDC_API_BASE_URL,
            is_user_required=is_user_required,
            user_view_only=user_view_only,
            book_duration=user_book_duration,
        )

        responses.add(
            responses.GET,
            url,
            json=udc_response["response"],
            status=udc_response["status_code"],
        )

    yield wrap


@pytest.fixture
def mock_udc_apis(mock_dc_api, mock_uc_api, mock_channel_api):
    def wrap(
        instance: CentralRequestInfo,
        udc_response: Dict,
        user_view_only: int = 0,
        user_is_avail: int = 1,
    ):
        if instance.request_type == "1":
            mock_uc_api(
                instance.c_id,
                instance.user_id,
                instance.ivr_id,
                user_is_avail,
                user_view_only,
                udc_response,
            )

        elif instance.request_type == "2":
            is_user_required = 0

            if (
                Helper.get_ivrinfo_common_setting(
                    instance.ivr_id, "obdv2_call_direction", "agent"
                )
                == "agent"
            ):
                is_user_required = 1
                user_book_duration = Helper.get_ivrinfo_common_setting(
                    instance.ivr_id, "ivr_type_3_user_first_book_dur", 5
                )
            else:
                user_book_duration = Helper.get_ivrinfo_common_setting(
                    instance.ivr_id, "ivr_type_3_customer_first_book_dur", 30
                )
            mock_dc_api(
                instance.c_id,
                instance.ivr_id,
                is_user_required,
                user_book_duration,
                user_view_only,
                udc_response,
            )
        mock_channel_api(instance.c_id, udc_response)

    yield wrap


@pytest.fixture(scope="function", autouse=True)
def clean_cache():
    cache.clear()
    SHARED_REDIS_CON.flushall()
    yield
    SHARED_REDIS_CON.flushall()
    cache.clear()


@pytest.fixture(scope="session", autouse=True)
def patch_sqs():
    from moto.core import patch_client

    patch_client(sqs_conf.sqs_client)
    return sqs_conf.sqs_client


@pytest.fixture()
def mock_create_queue():
    def wrap(queue_name, attrs=None):
        with mock_aws():
            sqs = boto3.client("sqs", region_name="ap-south-1")
            response = sqs.create_queue(
                QueueName=queue_name,
                Attributes=attrs or {},
            )
            return sqs, response

    return wrap


@pytest.fixture
def mock_fix_did_detail_api(responses, load_json):
    def wrap(
        company_id: str,
        fix_dids: List,
        ivr_id: str,
        status_code=status.HTTP_200_OK,
        fix_did_response=None,
        **kwargs,
    ):
        fix_did_response = fix_did_response or load_json(
            "external_apis/fix_did/fix_did_detail.json"
        )

        res_mapping = {}

        for did in fix_dids:
            res = deepcopy(fix_did_response)
            res["data"]["did"] = did
            res["data"]["did_without_std_code"] = did[3:]
            if fix_did_response["data"].get("ivrs"):
                res["data"]["ivrs"][0]["id"] = ivr_id
            res["data"].update(**kwargs)

            url = settings.FIXDID_API_URL.format(
                company_id=company_id,
                did=did,
                fixdid_base=settings.FIXDID_API_BASE_URL,
            )

            responses.add(
                responses.GET,
                url,
                json=res,
                status=status_code,
            )
            res_mapping[did] = res

        return res_mapping

    return wrap


@pytest.fixture
def mock_fix_did_pilot_list_api(responses, load_json):
    def wrap(
        pilot_number: str,
        status_code=status.HTTP_200_OK,
        server_group="phy",  # default is physical
        api_response=None,
    ):
        api_response = api_response or load_json(
            "external_apis/fix_did/pilot_list.json"
        )

        api_response["data"][0]["servers"][0]["server_group"] = server_group

        url = f"{settings.FIXDID_API_BASE_URL}/pri"
        params = {"include": "servers", "pilot_number": pilot_number}

        responses.add(
            responses.GET,
            url,
            json=api_response,
            status=status_code,
            match=[
                matchers.query_param_matcher(params),
            ],
        )

        return api_response

    return wrap


@pytest.fixture
def mock_kam_group_servers_api(
    responses, load_json, mock_routing_manager_kam_group_servers_api
):
    def wrap(
        status_code=status.HTTP_200_OK,
        api_response=None,
    ):
        kam_group_servers_response = load_json(
            "external_apis/routing_info/kam_group_servers.json"
        )
        mock_routing_manager_kam_group_servers_api(
            api_response=kam_group_servers_response
        )

        url = kam_group_servers_response["detail"][0]["url"]

        api_response = api_response or load_json(
            "external_apis/kamailio/group_servers.json"
        )

        responses.add(
            responses.POST,
            url,
            json=api_response,
            status=status_code,
        )

        return api_response

    yield wrap


@pytest.fixture
def mock_kam_group_servers_list_api(
    responses, load_json, mock_routing_manager_kam_group_servers_list_api
):
    def wrap(
        status_code=status.HTTP_200_OK,
        api_response=None,
    ):
        kam_group_servers_response = load_json(
            "external_apis/routing_info/kam_group_servers_list.json"
        )
        mock_routing_manager_kam_group_servers_list_api(
            api_response=kam_group_servers_response
        )

        url = kam_group_servers_response["detail"][0]["url"]

        api_response = api_response or load_json(
            "external_apis/kamailio/group_servers_list.json"
        )

        responses.add(
            responses.POST,
            url,
            json=api_response,
            status=status_code,
        )

        return api_response

    yield wrap


@pytest.fixture
def mock_routing_manager_kam_group_servers_list_api(responses, load_json):
    def wrap(
        status_code=status.HTTP_200_OK,
        api_response=None,
    ):
        api_response = api_response or load_json(
            "external_apis/routing_info/kam_group_servers_list.json"
        )

        url = settings.ROUTING_INFO_API_DATA["url"]
        params = {
            "name": settings.ROUTING_INFO_URL_NAMES[
                "kam_group_servers_list_route_name"
            ]
        }

        responses.add(
            responses.GET,
            url,
            json=api_response,
            status=status_code,
            match=[
                matchers.query_param_matcher(params),
            ],
        )

        return api_response

    yield wrap


@pytest.fixture
def mock_routing_manager_kam_group_servers_api(responses, load_json):
    def wrap(
        status_code=status.HTTP_200_OK,
        api_response=None,
    ):
        api_response = api_response or load_json(
            "external_apis/routing_info/kam_group_servers.json"
        )

        url = settings.ROUTING_INFO_API_DATA["url"]
        params = {
            "name": settings.ROUTING_INFO_URL_NAMES[
                "kam_group_servers_route_name"
            ]
        }

        responses.add(
            responses.GET,
            url,
            json=api_response,
            status=status_code,
            match=[
                matchers.query_param_matcher(params),
            ],
        )

        return api_response

    yield wrap


@pytest.fixture
def mock_obd_internal_api(responses, load_json):
    def wrap(
        api_response: Dict = None,
        status_code: int = status.HTTP_200_OK,
    ):
        if not api_response:
            api_response = load_json(
                "external_apis/obd_internal_api/success_response.json"
            )
        res = responses.add(
            responses.POST,
            settings.OBD_INTERNAL_API_URL,
            json=api_response,
            status=status_code,
        )
        return res

    yield wrap


@pytest.fixture
def mock_ivr_rule_api(responses, load_json):
    def wrap(
        ivr_id, company_id, available=True, status_code=status.HTTP_200_OK
    ):
        if available:
            ivr_rule_response = load_json(
                "external_apis/ivr_rule/available.json",
            )
        else:
            ivr_rule_response = load_json(
                "external_apis/ivr_rule/unavailable.json",
            )

        url = settings.RULE_API_URL.format(
            company_id=company_id,
            ivr_id=ivr_id,
            rule_base=settings.RULE_API_BASE_URL,
        )

        return responses.add(
            responses.GET,
            url,
            json=ivr_rule_response,
            status=status_code,
        )

    return wrap


@pytest.fixture
def create_cri_object():
    def wrap(request_data: Dict, **kwargs):
        data = {
            "request_id": request_data["request_id"],
            "ivr_id": request_data["ivr_id"],
            "c_id": request_data["company_id"],
            "user_id": request_data.get("user_id"),
            "request_type": request_data["type"],
            "raw_data": json.dumps(request_data),
        }
        return CentralRequestInfoFactory(**data, **kwargs)

    return wrap


@pytest.fixture
def mock_external_api(responses):
    def wrap(
        url,
        response_data,
        status_code=status.HTTP_200_OK,
        method=responses.GET,
        **kwargs,
    ):
        """
        Mock any external API call

        Args:
            url: The URL to mock
            response_data: The response data to return
            status_code: The status code to return
            method: The HTTP method to mock (GET, POST, etc.)
            **kwargs: Additional arguments to pass to responses.add
        """
        return responses.add(
            method, url, json=response_data, status=status_code, **kwargs
        )

    yield wrap


@pytest.fixture
def mock_external_api_with_matcher(responses):
    from responses import matchers

    def wrap(
        url,
        response_data,
        request_json=None,
        status_code=status.HTTP_200_OK,
        method=responses.POST,
    ):
        """
        Mock an external API with request body matching

        Args:
            url: The URL to mock
            response_data: The response data to return
            request_json: The expected request JSON to match
            status_code: The status code to return
            method: The HTTP method to mock
        """
        matchers_list = []
        if request_json:
            matchers_list.append(matchers.json_params_matcher(request_json))

        responses.add(
            method,
            url,
            json=response_data,
            status=status_code,
            match=matchers_list if matchers_list else None,
        )
        return response_data

    yield wrap


@pytest.fixture
def truecaller_api() -> TrueCallerApi:
    """Fixture to create a TrueCallerApi instance"""
    return TrueCallerApi()


@pytest.fixture
def setup_truecaller_enabled():
    """Fixture to set up TrueCaller as enabled in the database"""
    from handlers.models import CommonSetting
    from django.conf import settings
    import json

    # Create or update the CommonSetting for TrueCaller
    obj, created = CommonSetting.objects.get_or_create(entity="true_caller")
    obj.settings = json.dumps(
        {
            "enabled": True,
            "company_ids": [
                "60a392bbe3d97152",
                "5a8faa121a57f584",
            ],  # Company IDs from test fixtures
        }
    )
    obj.save()

    yield obj

    # Clean up after test
    obj.delete()


@pytest.fixture
def setup_truecaller_disabled():
    """Fixture to set up TrueCaller as disabled in the database"""
    from handlers.models import CommonSetting
    import json

    # Create or update the CommonSetting for TrueCaller
    obj, created = CommonSetting.objects.get_or_create(entity="true_caller")
    obj.settings = json.dumps(
        {
            "enabled": False,
            "company_ids": [],
        }
    )
    obj.save()

    yield obj

    # Clean up after test
    obj.delete()


@pytest.fixture
def mock_true_caller_token_api(
    responses, truecaller_api: TrueCallerApi, mock_external_api
):
    def wrap(status_code=status.HTTP_200_OK, response=None):
        token_url = truecaller_api._get_url(
            f"clients/{truecaller_api._get_client_id()}/token"
        )
        if status_code == status.HTTP_200_OK:
            if not response:
                response = {"token": TRUE_CALLER_TOKEN}

            return mock_external_api(
                token_url, response, method=responses.POST
            )
        else:
            if not response:
                response = {"error": "Invalid credentials"}
            return mock_external_api(
                token_url,
                response,
                status_code=status_code,
                method=responses.POST,
            )

    yield wrap


@pytest.fixture
def mock_true_caller_dial_assist_api(
    responses, truecaller_api: TrueCallerApi, mock_external_api
):
    def wrap(status_code=status.HTTP_200_OK):
        dial_assist_url = truecaller_api._get_url(
            f"/v1/clients/{truecaller_api._get_client_id()}/dial-assist"
        )

        return mock_external_api(
            dial_assist_url,
            {"prediction": 112344},
            status_code=status_code,
            method=responses.POST,
        )

    yield wrap
