[flake8]
max-line-length = 80
exclude = .tox,.git,__pycache__,.local,old,build,dist,*/migrations/*,*/static/CACHE/*,docs,venv,test_suite,deploy/
ignore = E266, E501, W503, F403, F841, E203, C901
per-file-ignores =
    central/settings/*.py: F405 E731
    central/settings/handlers/conf.py: F401
select = B,C,E,F,W,T4,B9
max-complexity = 10


[pycodestyle]
max-line-length = 80
exclude = .tox,.git,*/migrations/*,*/static/CACHE/*,docs


[mypy]
python_version = 3.7
check_untyped_defs = True
ignore_missing_imports = True
warn_unused_ignores = True
warn_redundant_casts = True
warn_unused_configs = True

[mypy.plugins.django-stubs]
django_settings_module = config.settings.test

[mypy-*.migrations.*]
# Django migrations should not produce any errors:
ignore_errors = True
