import json
import uuid
from datetime import datetime

from django.conf import settings
from django.db.models import Count
from django.forms.models import model_to_dict
from django.http import Http404
from django.utils.encoding import force_text
from rest_framework import status
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from rest_framework.views import APIView

from api import serializers
from central.settings.handlers import conf
from handlers.models import (
    CentralRequestInfo,
    CompanyUsage,
    Group,
    GroupIvrInfoRelationship,
    GroupServerRelationship,
    IvrInfo,
    OnHoldUserTracing,
)
from handlers.tasks import notif_company_usage_check, notif_group_ivr
from utills.cache_manager.main import CacheManager
from utills.helpers.helper import Helper
from utills.shared_cache_manager.main import SharedCacheManager
from utills.sqs_manager.main import SQSManager


class ALBHealthCheck(APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        Helper.sampling_logger_emitter(
            "api - health-check(GET)", 0.01, {"status_code": 200}
        )
        return Response(
            {"detail": "Success-OK.", "code": 200}, status=status.HTTP_200_OK
        )


class IvrInfoDiscovery(APIView):
    """
    Available methods: `POST`, 'GET', 'PUT', 'DELETE'

    Authentication Required: `NO`

    Post data: "method"

    """

    # authentication_classes = (
    # 	authentication.TokenAuthentication,
    # 	authentication.SessionAuthentication
    # )

    # permission_classes = [permissions.IsAuthenticated]

    paginator = PageNumberPagination()

    def get_object(self, pk, source_ip):
        try:
            return IvrInfo.objects.filter(id=pk)
        except Exception as e:
            settings.API_LOGGER.info(
                "api - ivrinfo invalid id:{id}, e: {e}".format(id=pk, e=e),
                extra={"status_code": 400, "source_ip": source_ip},
            )
            raise Http404

    def get_ivrinfo_object(self, key, value, source_ip, obj=None):
        try:
            if obj:
                return IvrInfo.objects.filter(**{key: str(value)}).first()

            return IvrInfo.objects.filter(**{key: str(value)})
        except Exception as e:
            settings.API_LOGGER.info(
                "api - ivrinfo {key}: {value} not found!, e: {e}".format(
                    key=key, value=value, e=e
                ),
                extra={"status_code": 404, "source_ip": source_ip},
            )
            raise Http404

    def get(self, request, *args, **kwargs):  # noqa
        ivrinfo_pk = kwargs.get("id", None)
        ivr_info_id = self.request.query_params.get("id", None)
        ivr_id = self.request.query_params.get("ivr_id", None)
        c_id = self.request.query_params.get("company_id", None)
        company_display_number = self.request.query_params.get(
            "company_display_number", None
        )

        source_ip = Helper.get_client_ip(request)

        if ivrinfo_pk:
            # for obd_settings_panel
            ivr_info = self.get_object(ivrinfo_pk, source_ip)
            serializer = serializers.serializers.IvrInfoSerializer(
                ivr_info, many=True
            )

        elif ivr_info_id:
            ivr_info = self.get_object(ivr_info_id, source_ip)
            serializer = serializers.serializers.IvrInfoSerializer(
                ivr_info, many=True
            )

        elif ivr_id:
            ivr_info = self.get_ivrinfo_object("ivr_id", ivr_id, source_ip)
            serializer = serializers.serializers.IvrInfoSerializer(
                ivr_info, many=True
            )

        elif c_id:
            ivr_info = self.get_ivrinfo_object("c_id", c_id, source_ip)
            serializer = serializers.serializers.IvrInfoSerializer(
                ivr_info, many=True
            )

        elif company_display_number:
            ivr_info = self.get_ivrinfo_object(
                "company_display_number", company_display_number, source_ip
            )
            serializer = serializers.serializers.IvrInfoSerializer(
                ivr_info, many=True
            )

        else:
            ivr_info = IvrInfo.objects.all()
            serializer = serializers.serializers.IvrInfoSerializer(
                ivr_info, many=True
            )

        page = self.request.query_params.get("page", None)

        result_page = self.paginator.paginate_queryset(
            ivr_info.order_by("id"), request
        )
        try:
            total_page_count = self.paginator.page.paginator.num_pages
        except Exception:
            total_page_count = 0

        if page is not None:
            serializer = serializers.serializers.IvrInfoSerializer(
                ivr_info, result_page, many=True, context={"request": request}
            )
            if serializer.is_valid():
                return Response(
                    {
                        "detail": serializer.data,
                        "total_page_count": total_page_count,
                        "code": 200,
                    },
                    status=status.HTTP_200_OK,
                )

        if not serializer.data:
            if ivrinfo_pk:
                settings.API_LOGGER.info(
                    "api - ivrinfo(GET) id: {id} not found!".format(
                        id=ivrinfo_pk
                    ),
                    extra={"status_code": 404, "source_ip": source_ip},
                )
            else:
                settings.API_LOGGER.info(
                    "api - ivrinfo(GET) query_param: {query_params} not found!".format(
                        query_params=dict(self.request.query_params)
                    ),
                    extra={"status_code": 404, "source_ip": source_ip},
                )

            return Response(
                {"detail": "data not found!", "code": 404},
                status=status.HTTP_404_NOT_FOUND,
            )

        try:
            total_page_count = self.paginator.page.paginator.num_pages
        except Exception:
            total_page_count = 0

        return Response(
            {
                "detail": serializer.data,
                "total_page_count": total_page_count,
                "code": 200,
            },
            status=status.HTTP_200_OK,
        )

    def post(self, request, *args, **kwargs):
        serializer = serializers.serializers.IvrInfoSerializer(
            data=request.data
        )
        source_ip = Helper.get_client_ip(request)

        if serializer.is_valid():
            serializer.save()
            return Response(
                {"detail": serializer.data, "code": 201},
                status=status.HTTP_201_CREATED,
            )

        settings.API_LOGGER.info(
            "api - ivrinfo(POST) error: {e}".format(e=serializer.errors),
            extra={"status_code": 400, "source_ip": source_ip},
        )
        return Response(
            {"detail": serializer.errors, "code": 400},
            status=status.HTTP_400_BAD_REQUEST,
        )

    def put(self, request, *args, **kwargs):
        ivr_id = request.data.get("ivr_id", None)
        source_ip = Helper.get_client_ip(request)

        request_id_obj = self.get_ivrinfo_object(
            "ivr_id", ivr_id, source_ip, obj=True
        )

        if not request_id_obj:
            settings.API_LOGGER.info(
                "api - ivrinfo(PUT) ivr_id: {ivr_id} not found!".format(
                    ivr_id=ivr_id
                ),
                extra={"status_code": 404, "source_ip": source_ip},
            )
            return Response(
                {
                    "detail": "ivr_id {ivr_id} not found!".format(
                        ivr_id=ivr_id
                    ),
                    "code": 404,
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = serializers.serializers.IvrInfoSerializer(
            request_id_obj, data=request.data, context={"request": request}
        )

        if serializer.is_valid():
            serializer.save()

            return Response(
                {"detail": serializer.data, "code": 200},
                status=status.HTTP_200_OK,
            )

        settings.API_LOGGER.info(
            "api - ivrinfo(PUT) errors: {e}".format(e=serializer.errors),
            extra={"status_code": 400, "source_ip": source_ip},
        )
        return Response(
            {"detail": serializer.errors, "code": 400},
            status=status.HTTP_400_BAD_REQUEST,
        )

    def delete(self, request, *args, **kwargs):
        ivrinfo_pk = kwargs.get("id", None)
        ivr_id = self.request.query_params.get("ivr_id", None)
        req_id = self.request.query_params.get("id", None)
        common_field_setting = self.request.query_params.get(
            "common_setting", None
        )
        source_ip = Helper.get_client_ip(request)

        delete_param = None

        if ivrinfo_pk:
            service = self.get_object(ivrinfo_pk, source_ip)
            delete_param = {"id": ivrinfo_pk}

        elif ivr_id:
            service = self.get_ivrinfo_object("ivr_id", ivr_id, source_ip)
            delete_param = {"ivr_id": ivr_id}
        elif req_id:
            service = self.get_object(req_id, source_ip)
            delete_param = {"id": req_id}
        else:
            settings.API_LOGGER.info(
                "api - ivrinfo(DELETE) Please provide ivr_id or id and common_field_setting[optional]",
                extra={"status_code": 400, "source_ip": source_ip},
            )
            return Response(
                {
                    "detail": "Please provide ivr_id or id and common_field_setting[optional]",
                    "code": 400,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if service.exists():
            if common_field_setting:
                common_field_setting_list = [
                    field.strip(" ")
                    for field in common_field_setting.strip(", ").split(",")
                ]
                common_setting = dict(service.first().common_setting)
                missing_fields = []

                for setting_field in common_field_setting_list:
                    if setting_field in common_setting:
                        common_setting.pop(setting_field)
                    else:
                        missing_fields.append(setting_field)

                if missing_fields:
                    settings.API_LOGGER.info(
                        "api - ivrinfo(DELETE) common_setting(s): {missing_fields} not found for {dp}".format(
                            dp=delete_param, missing_fields=missing_fields
                        ),
                        extra={"status_code": 404, "source_ip": source_ip},
                    )
                    return Response(
                        {
                            "detail": "common_setting(s): {missing_fields} not found for {dp}".format(
                                dp=delete_param, missing_fields=missing_fields
                            ),
                            "code": 404,
                        },
                        status=status.HTTP_404_NOT_FOUND,
                    )

                service.invalidated_update(
                    common_setting=common_setting, updated_on=datetime.now()
                )
                service_ = model_to_dict(service.first())
                service_["common_setting"] = json.loads(
                    service_["common_setting"]
                )
                return Response(
                    {"detail": service_, "code": 200},
                    status=status.HTTP_200_OK,
                )

            service.delete()
            return Response(
                {"detail": "OK", "code": 200}, status=status.HTTP_200_OK
            )

        settings.API_LOGGER.info(
            "api - ivrinfo(DELETE) not found for {dp}".format(dp=delete_param),
            extra={"status_code": 404, "source_ip": source_ip},
        )
        return Response(
            {
                "detail": "data not found for {dp}".format(dp=delete_param),
                "code": 404,
            },
            status=status.HTTP_404_NOT_FOUND,
        )


class CompanyUsageCheck(APIView):
    """
    Available methods: `POST`, 'GET', 'PUT', 'DELETE'

    Authentication Required: `NO`

    Post data: "method"

    """

    def get_object(self, c_id, source_ip):
        try:
            return CompanyUsage.objects.filter(c_id=str(c_id))
        except Exception as e:
            settings.API_LOGGER.info(
                "api - companyusage-check(GET) c_id: {c_id} not found!, e: {e}".format(
                    c_id=c_id, e=e
                ),
                extra={"status_code": 404, "source_ip": source_ip},
            )
            raise Http404

    def get(self, request, *args, **kwargs):
        c_id = self.request.query_params.get("c_id", None)

        source_ip = Helper.get_client_ip(request)

        if c_id:
            com_usage_obj = self.get_object(c_id, source_ip)
            if not com_usage_obj.exists():
                settings.API_LOGGER.info(
                    "api - companyusage-check(GET) c_id: {c_id} not found!".format(
                        c_id=c_id
                    ),
                    extra={"status_code": 404, "source_ip": source_ip},
                )
                return Response(
                    {
                        "detail": "c_id: {c_id} not found!".format(c_id=c_id),
                        "code": 404,
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

            com_usage = com_usage_obj.first()
            return Response(
                {"is_exist": com_usage.event, "code": 200},
                status=status.HTTP_200_OK,
            )

        settings.API_LOGGER.info(
            "api - companyusage-check(GET) {c_id} is required! status_code=400".format(
                c_id=c_id
            ),
            extra={"status_code": 400, "source_ip": source_ip},
        )
        return Response(
            {"detail": "Please Provide valid c_id.", "code": 400},
            status=status.HTTP_400_BAD_REQUEST,
        )


class ServerGroups(APIView):
    def get_server_object(self, server_name, source_ip):
        try:
            return GroupServerRelationship.objects.filter(
                server__name=server_name, group__is_enable=Group.YES
            )
        except GroupServerRelationship.DoesNotExist:
            settings.API_LOGGER.info(
                "api - server-groups(GET) server_name:{server_name} not found!".format(
                    server_name=server_name
                ),
                extra={"status_code": 404, "source_ip": source_ip},
            )
            raise Http404

    def get_gp_object(self, group_name, source_ip):
        try:
            return GroupServerRelationship.objects.filter(
                group__name=group_name, group__is_enable=Group.YES
            )
        except GroupServerRelationship.DoesNotExist:
            settings.API_LOGGER.info(
                "api - server-groups(GET) group_name:{group_name} not found!".format(
                    group_name=group_name
                ),
                extra={"status_code": 404, "source_ip": source_ip},
            )
            raise Http404

    def get_kam_gp_object(self, kam_group_id, source_ip):
        try:
            return GroupServerRelationship.objects.filter(
                group__kam_group_id=kam_group_id, group__is_enable=Group.YES
            )
        except GroupServerRelationship.DoesNotExist:
            settings.API_LOGGER.info(
                "api - server-groups(GET) kam_group_id:{kam_group_id} not found!".format(
                    kam_group_id=kam_group_id
                ),
                extra={"status_code": 404, "source_ip": source_ip},
            )
            raise Http404

    def get(self, request, *args, **kwargs):  # noqa
        server_name = self.request.query_params.get("server", None)
        group_name = self.request.query_params.get("group", None)
        kam_group_id = self.request.query_params.get("kam_group_id", None)
        source_ip = Helper.get_client_ip(request)

        response = {}
        if server_name:
            server_name = server_name.strip(" \n")
            server_group_info = self.get_server_object(server_name, source_ip)
            if not server_group_info.exists():
                settings.API_LOGGER.info(
                    "api - server-groups(GET) no group found for server:{server_name}".format(
                        server_name=server_name
                    ),
                    extra={"status_code": 404, "source_ip": source_ip},
                )
                return Response(
                    {
                        "detail": "no group found for server: {server}".format(
                            server=server_name
                        ),
                        "code": 404,
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

            group_servers = server_group_info.all()
            response["server"] = server_name
            response["group"] = []
            for group_server_obj in group_servers:
                response["group"].append(
                    {
                        "name": group_server_obj.group.name,
                        "assigned_queues": group_server_obj.group.get_assigned_queues,
                        "settings": group_server_obj.group.settings,
                        "kam_group_id": group_server_obj.group.kam_group_id,
                    }
                )
            return Response(
                {"detail": response, "code": 200}, status=status.HTTP_200_OK
            )

        if group_name:
            group_name = group_name.strip(" \n")
            group_server_info = self.get_gp_object(group_name, source_ip)
            if not group_server_info.exists():
                settings.API_LOGGER.info(
                    "api - server-groups(GET) no server found for group:{group_name}".format(
                        group_name=group_name
                    ),
                    extra={"status_code": 404, "source_ip": source_ip},
                )
                return Response(
                    {
                        "detail": "no server found for group: {group_name}".format(
                            group_name=group_name
                        ),
                        "code": 404,
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

            group_servers = group_server_info.all()
            response["group"] = group_name
            response["server"] = []
            for group_server_obj in group_servers:
                response["server"].append(group_server_obj.server.name)
            return Response(
                {"detail": response, "code": 200}, status=status.HTTP_200_OK
            )

        if kam_group_id:
            try:
                kam_group_id = int(kam_group_id)
            except Exception:
                settings.API_LOGGER.info(
                    "api - server-groups(GET) kam_group_id: {kam_group_id} must be an integer!".format(
                        kam_group_id=kam_group_id
                    ),
                    extra={"status_code": 400, "source_ip": source_ip},
                )
                return Response(
                    {
                        "detail": "kam_group_id: {kam_group_id} must be an integer!".format(
                            kam_group_id=kam_group_id
                        ),
                        "code": 400,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            group_server_info = self.get_kam_gp_object(kam_group_id, source_ip)
            if not group_server_info.exists():
                settings.API_LOGGER.info(
                    "api - server-groups(GET) no server found for kam_group_id: {kam_group_id}".format(
                        kam_group_id=kam_group_id
                    ),
                    extra={"status_code": 404, "source_ip": source_ip},
                )
                return Response(
                    {
                        "detail": "no server found for kam_group_id: {kam_group_id}".format(
                            kam_group_id=kam_group_id
                        ),
                        "code": 404,
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

            group_servers = group_server_info.all()
            response["kam_group_id"] = kam_group_id
            response["server"] = []
            for group_server_obj in group_servers:
                response["server"].append(group_server_obj.server.name)
            return Response(
                {"detail": response, "code": 200}, status=status.HTTP_200_OK
            )

        settings.API_LOGGER.info(
            "api - server-groups(GET) Please Provide valid server or group",
            extra={"status_code": 404, "source_ip": source_ip},
        )
        return Response(
            {"detail": "Please Provide valid server or  group .", "code": 400},
            status=status.HTTP_400_BAD_REQUEST,
        )


class GroupIVR(APIView):
    def get_ivr_id_object(self, ivr_id, source_ip):
        try:
            return GroupIvrInfoRelationship.objects.filter(
                ivr_info__ivr_id=ivr_id
            )
        except GroupIvrInfoRelationship.DoesNotExist:
            settings.API_LOGGER.info(
                "api - group-ivr(GET) ivr_id: {ivr_id} not found!".format(
                    ivr_id=ivr_id
                ),
                extra={"status_code": 404, "source_ip": source_ip},
            )
            raise Http404

    def get_gp_object(self, gp_name, source_ip):
        try:
            return GroupIvrInfoRelationship.objects.filter(group__name=gp_name)
        except GroupIvrInfoRelationship.DoesNotExist:
            settings.API_LOGGER.info(
                "api - group-ivr(GET) group: {gp_name} not found!".format(
                    gp_name=gp_name
                ),
                extra={"status_code": 404, "source_ip": source_ip},
            )
            raise Http404

    def get(self, request, *args, **kwargs):
        ivr_id = self.request.query_params.get("ivr_id", None)
        group_name = self.request.query_params.get("group", None)
        source_ip = Helper.get_client_ip(request)

        response = {}

        if ivr_id:
            ivr_group_info_obj = self.get_ivr_id_object(ivr_id, source_ip)
            if not ivr_group_info_obj.exists():
                settings.API_LOGGER.info(
                    "api - group-ivr(GET) ivr_id: {ivr_id} not found!".format(
                        ivr_id=ivr_id
                    ),
                    extra={"status_code": 404, "source_ip": source_ip},
                )
                return Response(
                    {
                        "detail": "ivr_id {ivr_id} not found!".format(
                            ivr_id=ivr_id
                        ),
                        "code": 404,
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

            ivr_group_info = ivr_group_info_obj.all()
            response["ivr_info"] = {}
            response["group"] = []

            for ivr_group_obj in ivr_group_info:
                response["ivr_info"] = {
                    "company_id": ivr_group_obj.ivr_info.c_id,
                    "ivr_id": ivr_group_obj.ivr_info.ivr_id,
                    "common_setting": ivr_group_obj.ivr_info.common_setting,
                    "company_priority": ivr_group_obj.ivr_info.company_priority,
                    "ivr_priority": ivr_group_obj.ivr_info.ivr_priority,
                    "company_display_number": ivr_group_obj.ivr_info.company_display_number,
                }

                if any(
                    ivr_group_obj.group.name == d["name"]
                    for d in response["group"]
                ):
                    continue

                response["group"].append(
                    {
                        "name": ivr_group_obj.group.name,
                        "assigned_queues": ivr_group_obj.group.get_assigned_queues,
                        "settings": ivr_group_obj.group.settings,
                        "kam_group_id": ivr_group_obj.group.kam_group_id,
                    }
                )
            return Response(
                {"detail": response, "code": 200}, status=status.HTTP_200_OK
            )

        if group_name:
            group_ivr_info_obj = self.get_gp_object(group_name, source_ip)
            if not group_ivr_info_obj.exists():
                settings.API_LOGGER.info(
                    "api - group-ivr(GET) group: {group_name} not found!".format(
                        group_name=group_name
                    ),
                    extra={"status_code": 404, "source_ip": source_ip},
                )
                return Response(
                    {
                        "detail": "group {group_name} not found!".format(
                            group_name=group_name
                        ),
                        "code": 404,
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

            group_ivr_info = group_ivr_info_obj.all()

            response["ivr_info"] = []
            response["group"] = {}

            for ivr_group_obj in group_ivr_info:
                response["group"] = {
                    "name": ivr_group_obj.group.name,
                    "assigned_queues": ivr_group_obj.group.get_assigned_queues,
                    "settings": ivr_group_obj.group.settings,
                    "kam_group_id": ivr_group_obj.group.kam_group_id,
                }

                if any(
                    ivr_group_obj.ivr_info.ivr_id == d["ivr_id"]
                    for d in response["ivr_info"]
                ):
                    continue

                response["ivr_info"].append(
                    {
                        "company_id": ivr_group_obj.ivr_info.c_id,
                        "ivr_id": ivr_group_obj.ivr_info.ivr_id,
                        "common_setting": ivr_group_obj.ivr_info.common_setting,
                        "company_priority": ivr_group_obj.ivr_info.company_priority,
                        "ivr_priority": ivr_group_obj.ivr_info.ivr_priority,
                        "company_display_number": ivr_group_obj.ivr_info.company_display_number,
                    }
                )

            return Response(
                {"detail": response, "code": 200}, status=status.HTTP_200_OK
            )

        settings.API_LOGGER.info(
            "api - group-ivr(GET) Please Provide a valid ivr_id or group!",
            extra={"status_code": 400, "source_ip": source_ip},
        )
        return Response(
            {"detail": "Please Provide a valid ivr_id or group", "code": 400},
            status=status.HTTP_400_BAD_REQUEST,
        )


class CompanyDisplayNumbers(APIView):
    def get(self, request, *args, **kwargs):
        company_display_numbers = (
            IvrInfo.objects.exclude(company_display_number=None)
            .distinct()
            .values_list("company_display_number", flat=True)
        )
        return Response(
            {"detail": company_display_numbers, "code": 200},
            status=status.HTTP_200_OK,
        )


class CompanyIvrsList(APIView):
    def get(self, request, *args, **kwargs):
        company_ivrs_list = (
            IvrInfo.objects.exclude(ivr_id=None)
            .distinct()
            .values_list("ivr_id", flat=True)
        )
        return Response(
            {"detail": company_ivrs_list, "code": 200},
            status=status.HTTP_200_OK,
        )


class NotifGroupIVR(APIView):
    throttle_scope = "notif"

    def get(self, request, *args, **kwargs):
        try:
            source_ip = Helper.get_client_ip(request)
            notif_group_ivr.delay()
            return Response(
                {"detail": "success", "code": 200}, status=status.HTTP_200_OK
            )
        except Exception as e:
            settings.API_LOGGER.error(
                "api - notif-group-ivr(GET) some error occured in async req, e={e}".format(
                    e=e
                ),
                extra={"status_code": 500, "source_ip": source_ip},
            )
            return Response(
                {"detail": "failed", "code": 500},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class NotifCompanyUsageCheck(APIView):
    throttle_scope = "notif"

    def get(self, request, *args, **kwargs):
        try:
            source_ip = Helper.get_client_ip(request)
            notif_company_usage_check.delay()
            return Response(
                {"detail": "success", "code": 200}, status=status.HTTP_200_OK
            )
        except Exception as e:
            settings.API_LOGGER.error(
                "api - notif-companyusage-check(GET) some error occured in async req, e={e}".format(
                    e=e
                ),
                extra={"status_code": 500, "source_ip": source_ip},
            )
            return Response(
                {"detail": "failed", "code": 500},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class IvrActions(APIView):
    def post(self, request, *args, **kwargs):
        ivr_id = request.data.get("ivr_id", None)
        ivr_status = str(request.data.get("status", ""))
        prefix = settings.CACHE_IVR_KEY_NAME

        settings.API_LOGGER.info(
            "api - ivr-actions(POST) ivr_id: {ivr_id}, status: {s}".format(
                ivr_id=ivr_id, s=ivr_status
            )
        )

        if ivr_status not in ("1", "2", "3", "4"):
            return Response(
                {
                    "detail": "invalid status({s})!".format(s=ivr_status),
                    "code": 400,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if ivr_status == "3":
            return Response(
                {"detail": "ignoring action", "code": 200},
                status=status.HTTP_200_OK,
            )

        ivr_info_obj = IvrInfo.objects.filter(ivr_id=ivr_id)
        if not ivr_info_obj.exists():
            settings.API_LOGGER.info(
                "ivr({ivr_id}) does not exists!".format(ivr_id=ivr_id)
            )
            return Response(
                {
                    "detail": "ivr({ivr_id}) doesn't exists!".format(
                        ivr_id=ivr_id
                    ),
                    "code": 400,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        queue_name = "{prefix}{ivr_id}".format(prefix=prefix, ivr_id=ivr_id)
        # safety-check cause of delete permission of sqs-queues
        try:
            if prefix and ivr_id:
                queue_url = SQSManager.get_queue_url(queue_name)
        except Exception:
            settings.API_LOGGER.info(
                "api-ivr_actions: unable to get sqs queue url, queue: {q}".format(
                    q=queue_name
                )
            )
            queue_url = None

        if ivr_status == "1":
            if not queue_url:
                try:
                    SQSManager.create_queue(
                        queue_name, settings.IVR_Q_VISIBILITY_TIMEOUT
                    )
                except Exception:
                    pass
                    # for warmup requirements of 60 sec by aws for creating the queue with same name
                settings.API_LOGGER.info(
                    "sqs queue created, queue: {q}".format(q=queue_name)
                )
                return Response(
                    {"detail": "sqs queue created", "code": 201},
                    status=status.HTTP_201_CREATED,
                )
            return Response(
                {"detail": "sqs queue already exists!", "code": 200},
                status=status.HTTP_200_OK,
            )

        if not queue_url:
            return Response(
                {"detail": "sqs queue doesn't exists!", "code": 200},
                status=status.HTTP_200_OK,
            )

        # ivr-action = 2 and 4
        sqs_msg_count = SQSManager.get_queue_attributes(queue_url)[
            "ApproximateNumberOfMessages"
        ]
        cache_count = CacheManager.get_value(queue_name)
        if str(sqs_msg_count) == "0" and not cache_count:
            deleted_obj = SQSManager.sqs_delete_queue(queue_url)
            if not deleted_obj:
                return Response(
                    {
                        "detail": "Some permission-issue/non-existed queue deletion Error Occured",
                        "code": 403,
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            settings.API_LOGGER.info(
                "sqs queue deleted, queue: {q}".format(q=queue_url)
            )
            return Response(
                {"detail": "sqs queue deleted", "code": 202},
                status=status.HTTP_202_ACCEPTED,
            )

        return Response(
            {
                "detail": "ignoring ivr as queue is not empty, msg_count={mc}, cache_count={cc}".format(
                    mc=sqs_msg_count, cc=cache_count
                ),
                "code": 200,
            },
            status=status.HTTP_200_OK,
        )


class IvrRequestsCount(APIView):
    def get(self, request, *args, **kwargs):
        ivr_id = self.request.query_params.get("ivr_id", None)

        if not ivr_id:
            return Response(
                {"detail": "please provide an ivr_id.", "code": 400},
                status=status.HTTP_400_BAD_REQUEST,
            )

        ivr_info_obj = IvrInfo.objects.filter(ivr_id=ivr_id)
        if not ivr_info_obj.exists():
            settings.API_LOGGER.info(
                "ivr({ivr_id}) does not exists!".format(ivr_id=ivr_id)
            )
            return Response(
                {
                    "detail": "ivr({ivr_id}) doesn't exists!".format(
                        ivr_id=ivr_id
                    ),
                    "code": 404,
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        ivr_requests = CentralRequestInfo.objects.filter(
            ivr_id=ivr_id,
            is_onhold_req=CentralRequestInfo.ENTER,
            is_enable=CentralRequestInfo.YES,
            is_req_completed=CentralRequestInfo.NO,
        )  # , added_on__date__gte=datetime.today().date() - timedelta(days=1))

        response_data = {
            "detail": {"incomplete_req_count": ivr_requests.count()},
            "code": 200,
        }

        return Response(response_data, status=status.HTTP_200_OK)


class DisabledUsers(APIView):
    def get(self, request, *args, **kwargs):
        ivr_id = self.request.query_params.get("ivr_id", None)

        if not ivr_id:
            return Response(
                {"detail": "please provide an ivr_id.", "code": 400},
                status=status.HTTP_400_BAD_REQUEST,
            )

        ivr_ids_list = [
            field.strip(" ") for field in ivr_id.strip(", ").split(",")
        ]

        user_tracing = OnHoldUserTracing.objects.filter(
            ivr_id__in=ivr_ids_list, count_disabled_req__gte=3
        ).values_list("ivr_id", "user_id")

        response_dict = {}

        for ivr in ivr_ids_list:
            response_dict.setdefault(ivr, [])

        for user_trace in user_tracing:
            response_dict[user_trace[0]].append(user_trace[1])

        response_data = {"detail": response_dict, "code": 200}

        return Response(response_data, status=status.HTTP_200_OK)


class JunctionRequestsCount(APIView):
    def get(self, request, *args, **kwargs):
        ivr_id = self.request.query_params.get("ivr_id", None)

        if not ivr_id:
            return Response(
                {"detail": "please provide an ivr_id.", "code": 400},
                status=status.HTTP_400_BAD_REQUEST,
            )

        required_on_hold_stages = (
            CentralRequestInfo.NA,
            CentralRequestInfo.ENTER,
            CentralRequestInfo.IVR_TIMING_FAIL,
        )
        response_keys = {
            "na": "central in processing requests",
            "ivr_timing_fail": "onhold ivr_timing failed requests",
            "Enter": "on_hold_requests",
            "ivr_queue_req": "central ivr queue cnt",
        }

        cri_requests = (
            CentralRequestInfo.objects.filter(
                ivr_id=ivr_id,
                is_onhold_req__in=required_on_hold_stages,
                is_req_completed=CentralRequestInfo.NO,
            )
            .values("is_onhold_req")
            .annotate(count=Count("is_onhold_req"))
        )

        on_hold_stages = dict(
            CentralRequestInfo._meta.get_field("is_onhold_req").flatchoices
        )

        output_dict = {}
        for cri_request in cri_requests:
            output_dict[
                force_text(
                    on_hold_stages[cri_request["is_onhold_req"]],
                    strings_only=True,
                )
            ] = cri_request["count"]

        for choice in required_on_hold_stages:
            if not output_dict.get(on_hold_stages[choice], None):
                output_dict[on_hold_stages[choice]] = 0

        output_dict["ivr_queue_req"] = (
            CacheManager.get_value(f"{conf.CACHE_IVR_KEY_NAME}{ivr_id}") or 0
        )

        output_dict = {
            response_keys.get(key): output_dict.get(key) for key in output_dict
        }

        return Response(
            {"status": "success", "detail": output_dict, "code": 200},
            status=200,
        )


class JobEventAPIView(APIView):
    """
    This API jobs is to basically set and remove Abort Job cache key for a job_id based on the job_status.
    """

    ABORT = conf.JP_TRI_REQUEST_STATUS["abort"]

    def abort_job(self, job_id):
        """
        Adding the abort job key in SharedCache and Central Cache
        Reason for adding the abort job key in SharedCache, is to read the key in IVR Processor
        """

        shared_cache_key = f"{conf.JP_JOB_ABORT_CACHE_KEY}{job_id}"
        central_cache_key = f"{conf.JP_JOB_ABORT_CENTRAL_CACHE_KEY}{job_id}"

        CacheManager.set_value(
            central_cache_key,
            "true",
            expiry=conf.JP_JOB_ABORT_CENTRAL_CACHE_KEY_TTL,
        )

        SharedCacheManager.set_value(
            shared_cache_key, "true", conf.JP_JOB_ABORT_CACHE_KEY_TTL
        )

        settings.API_LOGGER.info(
            f"Successfully set abort cache key for job - {job_id}"
        )

    def post(self, *args, **kwargs):
        """This api expects job_id as UUID string"""

        job_id = self.request.data.get("job_id", None)
        job_status = self.request.data.get("job_status", self.ABORT)

        try:
            job_id = uuid.UUID(job_id)
            if job_status == self.ABORT:
                self.abort_job(job_id)

        except Exception as e:
            settings.API_LOGGER.error(
                "api - JobEventAPIView(POST) with job_id - {job_id} and job_status - {job_status} ,  error: {e}".format(
                    e=e, job_id=job_id, job_status=job_status
                )
            )
            return Response(
                {"detail": "please provide a valid job_id.", "code": 400},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(
            {
                "status": "success",
                "detail": "Cache successfully updated.",
                "code": 200,
            },
            status=200,
        )
