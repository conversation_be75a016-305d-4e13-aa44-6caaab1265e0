from rest_framework.authtoken.models import Token
from rest_framework.decorators import detail_route
from rest_framework.decorators import list_route
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework.reverse import reverse

from rest_framework.decorators import api_view
from django.utils.decorators import method_decorator

from rest_framework import views
from rest_framework_extensions.cache.decorators import (cache_response)

from django.views.decorators.cache import cache_page
from rest_framework.views import APIView
from collections import OrderedDict
from copy import deepcopy

from api.apidocs.conf import IvrInfo



def return_response(request):
	response = OrderedDict()
	response['IvrInfo'] = doc_ivr_info(request)
	return response


def doc_ivr_info(request):

	response = OrderedDict()

	response['GET'] = doc_manipulator(IvrInfo['GET'],request)

	response['POST'] = doc_manipulator(IvrInfo['POST'],request)
	
	response['PUT'] = doc_manipulator(IvrInfo['PUT'],request)

	response['DELETE'] = doc_manipulator(IvrInfo['DELETE'],request)

	return response

def doc_manipulator(doc_dict, request):
	created_dict=deepcopy(doc_dict)
	for data in created_dict:
		if(data=='url'):
			created_dict[data] = reverse(created_dict[data],kwargs=created_dict['kwargs'],request=request)

	created_dict.pop('kwargs')

	return created_dict
