IvrInfo = {
    'GET': {

        'Description': 'Get data of IvrInfo',
        'url': 'api.ivr_info',
        'kwargs': {
            'version': 1
        },
        'Authentication Required': 'NO',
        'Method': 'GET',
        'Optional Params': ["page"],
        'Request': {
        },
        'Response': {
            "detail": [
                {
                    "id": 1,
                    "c_id": "c1",
                            "ivr_id": "1",
                            "common_setting": None,
                            "company_priority": 0,
                            "ivr_priority": 4
                }
            ]
        }
    },
    'POST': {
        'Description': 'Register a new ivr',
        'url': 'api.ivr_info',
        'kwargs': {
            'version': 1
        },
        'Authentication Required': 'NO',
        'Method': 'POST',
        'Request': {
            "c_id": "1",
                    "ivr_id": "106",
                    "ivr_priority": "1",
                    "company_priority": "1",
                                        "common_setting": "{\"max_retry\": \"2\"}"
        },
        'Response': {
            "id": 11,
            "c_id": "1",
                    "ivr_id": "106",
                    "common_setting": "{\"max_retry\": \"2\"}",
                    "company_priority": 1,
            "ivr_priority": 1
        }
    },
    'PUT': {

        'Description': 'Update ivr data',
        'url': 'api.ivr_info',
        'kwargs': {
            'version': 1
        },
        'Authentication Required': 'NO',
        'Method': 'PUT',
        'Request': {
            "ivr_id": "106",
            "company_priority": "4"
        },
        'Response': {
            "id": 11,
            "c_id": "1",
                    "ivr_id": "106",
                    "common_setting": "{\"max_retry\": \"2\"}",
                    "company_priority": 4,
            "ivr_priority": 1
        }
    },
    'DELETE': {
        'Description': 'Delete an ivr or its common_settings',
        'url': 'api.ivr_info',
        'kwargs': {
            'version': 1
        },
        'Authentication Required': 'NO',
        'Method': 'DELETE',
        'Optional Params': ["id", "name"],
        'Request': {
            "ivr_id": "1"
        },
        'Response': {
            "detail": "OK"
        }
    }
}
