# -*- coding: utf-8 -*-
from __future__ import unicode_literals
import ast
import copy
import json
import random
import re
import urllib
from collections import defaultdict, OrderedDict
from datetime import datetime

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import User
from django.db import transaction
from django.db.models import Q, Max, Min, Count

from rest_framework import serializers
from rest_framework.authtoken.models import Token
from rest_framework.reverse import reverse
from rest_framework.settings import api_settings
from rest_framework.utils.field_mapping import get_nested_relation_kwargs

from handlers.models import CompanyUsage, Group, GroupIvrInfoRelationship, GroupServerRelationship, IvrInfo, Server


class DocCompanyUsageSerializer(serializers.ModelSerializer):

	class Meta:
		model = CompanyUsage
		fields = '__all__'


class DocGroupServerRelationshipSerializer(serializers.ModelSerializer):

	class Meta:
		model = GroupServerRelationship
		fields = '__all__'

class DocGroupIvrInfoRelationshipSerializer(serializers.ModelSerializer):

	class Meta:
		model = GroupIvrInfoRelationship
		fields = '__all__'


class IvrInfoSerializer(serializers.ModelSerializer):

	common_setting = serializers.JSONField()

	class Meta:
		model = IvrInfo
		fields = (
			'id', 'c_id', 'ivr_id', 'ivr_type', 'common_setting', 'company_priority', 'ivr_priority', 'company_display_number', 'country_code'
		)

	def create(self, validated_data):
		if "common_setting" in validated_data:
			for setting in validated_data['common_setting']:
				try:
					if isinstance(validated_data['common_setting'][setting], int): continue
					if '[' in validated_data['common_setting'][setting] or ']' in validated_data['common_setting'][setting]:
						validated_data['common_setting'][setting] = validated_data['common_setting'][setting].strip('][').replace('\"', '').split(', ')
				except Exception as e:
					pass

		with transaction.atomic():
			return IvrInfo.objects.create(**validated_data)

	def to_internal_value(self, data):
		instance = super(IvrInfoSerializer, self).to_internal_value(data)
		if "common_setting" in data:
			ivr_info = IvrInfo.objects.filter(ivr_id= instance.get('ivr_id'))
			if ivr_info.exists():
				cs = instance.get('common_setting')
				old_common_settings = dict(ivr_info.first().common_setting or {})
				old_common_settings = copy.deepcopy(old_common_settings)
				old_common_settings.update(cs)

				if old_common_settings.get('company_ip_list', None):
					old_common_settings['company_ip_list'] = self.make_ip_list(old_common_settings.get('company_ip_list'))

				instance['common_setting'] = old_common_settings
		return instance

	def make_ip_list(self, list_str):
		list_obj = []
		try:
			list_str = ast.literal_eval(list_str)
		except Exception:
			pass

		if isinstance(list_str, str):
			list_obj = [item.strip() for item in list_str.split(",") if item != '']

		if isinstance(list_str, list):
			list_obj = list_str

		return list_obj


class ServerSerializer(serializers.ModelSerializer):
	class Meta:
		model = Server
		fields = (
			'name',
		)

class GroupSerializer(serializers.ModelSerializer):
	assigned_queues = serializers.JSONField()

	class Meta:
		model = Group
		fields = (
			'name', 'assigned_queues'
		)

class GroupServerSerializer(serializers.ModelSerializer):
	group = GroupSerializer()
	server = ServerSerializer()

	class Meta:
		model = GroupServerRelationship
		fields = (
			'server', 'group'
		)

class GroupIvrInfoSerializer(serializers.ModelSerializer):
	ivr_info = IvrInfoSerializer()
	group = GroupSerializer()

	class Meta:
		model = GroupIvrInfoRelationship
		fields = (
			'ivr_info', 'group'
		)