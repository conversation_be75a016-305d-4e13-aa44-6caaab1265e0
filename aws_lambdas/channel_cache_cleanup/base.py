import logging
import os

from pythonjsonlogger import jsonlogger


def setup_logging(log_level):
    logger = logging.getLogger()

    # Testing showed lambda sets up one default handler. If there are more,
    # something has changed and we want to fail so an operator can investigate.

    logger.setLevel(log_level)
    json_handler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter(
        fmt="%(asctime)s %(levelname)s %(name)s [%(pathname)s:%(lineno)d in function %(funcName)s]  %(message)s %(aws_request_id)s"
    )
    json_handler.setFormatter(formatter)
    logger.addHandler(json_handler)
    if len(logger.handlers) > 1:
        #  removing default handler of lambda
        logger.removeHandler(logger.handlers[0])


setup_logging(logging.INFO)
logger = logging.getLogger("channel_cache_cleanup")

REDIS_HOST = os.getenv("REDIS_HOST")
REDIS_PORT = os.getenv("REDIS_PORT")

REDIS_DB = os.getenv("REDIS_DB")

CACHE_VERSION = os.getenv("CACHE_VERSION", "cs")


CACHE_TTL = 3600
