import json

import pytest
from cache_handler import UDCCacheHandler
from exceptions import InvalidPayload
from handler import get_payload_from_sns, lambda_handler


@pytest.mark.unittest
def test_get_payload_from_sns(load_json):
    sns_payload = load_json("test-data.json")
    message = get_payload_from_sns(sns_payload)
    assert message == json.loads(sns_payload["Records"][0]["Sns"]["Message"])


@pytest.mark.unittest
def test_get_payload_from_sns_invalid_payload(load_json):
    sns_payload = load_json("test-data.json")
    sns_payload["Records"][0]["Sns"]["Message"] = "'"
    with pytest.raises(InvalidPayload):
        get_payload_from_sns(sns_payload)
    sns_payload["Records"] = []
    with pytest.raises(InvalidPayload, match="payload not found"):
        get_payload_from_sns(sns_payload)


@pytest.mark.unittest
def test_lambda_handler(load_json):
    sns_payload = load_json("test-data.json")
    payload = get_payload_from_sns(sns_payload)
    company_id = payload["company_id"]
    reference_id = payload["ref_id"]
    cache_handler = UDCCacheHandler(company_id, reference_id)
    cache_handler.save("1")
    assert cache_handler.get() == "1"
    assert lambda_handler(sns_payload, {}) == {
        "statusCode": 200,
        "body": "Success",
    }
    assert not cache_handler.get()


@pytest.mark.unittest
def test_lambda_handler_invalid_payload(load_json):
    sns_payload = load_json("test-data.json")
    payload = get_payload_from_sns(sns_payload)
    company_id = payload["company_id"]
    reference_id = payload["ref_id"]
    del payload["company_id"]

    cache_handler = UDCCacheHandler(company_id, reference_id)
    cache_handler.save("1")

    sns_payload["Records"][0]["Sns"]["Message"] = json.dumps(payload)
    assert lambda_handler(sns_payload, {}) == {
        "statusCode": 200,
        "body": "'company_id'",
    }
    assert cache_handler.get() == "1"
