import json

from base import logger
from cache_handler import UDCCacheHandler
from exceptions import InvalidPayload


def get_payload_from_sns(event):
    records = event["Records"]
    if len(records) <= 0:
        raise InvalidPayload("payload not found")
    try:
        body = records[0]["Sns"]["Message"]
        json_body = json.loads(body)
        return json_body
    except json.JSONDecodeError as e:
        raise InvalidPayload from e


def lambda_handler(event, context):
    try:
        payload = get_payload_from_sns(event)
        company_id = payload["company_id"]
        reference_id = payload["ref_id"]
    except (KeyError, TypeError, InvalidPayload) as error:
        logger.error(f"Invalid event: {event} error: {error}", exc_info=True)
        return {
            "statusCode": 200,
            "body": str(error),
        }

    cache_handler = UDCCacheHandler(company_id, reference_id)
    cache_handler.delete()
    return {
        "statusCode": 200,
        "body": "Success",
    }
