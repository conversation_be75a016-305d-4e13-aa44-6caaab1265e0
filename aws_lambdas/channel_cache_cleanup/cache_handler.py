import os

import redis
from base import (
    CACH<PERSON>_TTL,
    CACHE_VERSION,
    REDIS_DB,
    REDIS_HOST,
    REDIS_PORT,
    logger,
)
from fakeredis import FakeRedis

_CACHE_CON = None


def get_cache_instance():
    global _CACHE_CON

    try:
        if not _CACHE_CON:
            if os.getenv("PYTEST_RUNNING"):
                _CACHE_CON = FakeRedis(
                    decode_responses=True,
                )

            else:
                _CACHE_CON = redis.Redis(
                    host=REDIS_HOST,
                    port=REDIS_PORT,
                    db=REDIS_DB,
                    decode_responses=True,
                    socket_timeout=10,
                )
            _CACHE_CON.ping()
        return _CACHE_CON
    except Exception as err:
        logger.critical(
            f"Error connecting to redis, ERROR:-  {err}", exc_info=True
        )


class UDCCacheHandler:
    def __init__(self, company_id: str, request_id: str):
        self.key = f":{CACHE_VERSION}:channel_{company_id}_{request_id}"
        self.cache = get_cache_instance()
        self.request_id = request_id
        self.company_id = company_id
        logger.info(
            f"UDCCacheHandler:- Task Key - {self.key}, request_id- {request_id}, company_id- {company_id}"
        )

    def save(self, value: str, ttl: int = CACHE_TTL) -> str:
        self.cache.set(self.key, value, ttl)

    def get(self):
        logger.info(
            f"UDCCacheHandler:- Fetching key:- {self.key}, request_id- {self.request_id}, company_id- {self.company_id}"
        )
        res = self.cache.get(self.key)
        return res

    def delete(self):
        logger.info(
            f"UDCCacheHandler:- Deleting key:- {self.key}, request_id- {self.request_id}, company_id- {self.company_id}"
        )
        res = self.cache.delete(self.key)
        return res
