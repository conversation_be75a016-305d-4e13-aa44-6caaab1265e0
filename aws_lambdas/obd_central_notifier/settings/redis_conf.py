import os
import sys

import redis
from fakeredis import FakeRedis
from settings.conf import APP_LOGGER, REDIS_HOST, REDIS_PORT

try:
    if os.getenv("PYTEST_RUNNING"):
        REDIS_CONN = FakeRedis(decode_responses=True)
    else:
        REDIS_CONN = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=0,
            decode_responses=True,
            socket_timeout=5,
        )
    REDIS_CONN.ping()
except redis.exceptions.ConnectionError as e:
    APP_LOGGER.error(
        "obd_central_notifier_lambda - exiting... problem in connecting to redis!, e: {e}".format(
            e=e
        )
    )
    sys.exit()
