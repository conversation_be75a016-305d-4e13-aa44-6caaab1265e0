import os

from utils import logging_manager

APP = os.environ.get("app_name", "obd_central_notifier")

########################## Logger###########################
APP_LOGGER = logging_manager.get_logger()

########################### SNS ##########################
GROUP_IVR_RELETION_SNS_NAME = os.environ.get(
    "GROUP_IVR_RELETION_SNS_NAME", "obd_group_ivr_relation_notifier"
)
COMPANYUSAGE_CHECK_SNS_NAME = os.environ.get(
    "COMPANYUSAGE_CHECK_SNS_NAME", "obd_companyusage_check_notifier"
)

########################### Redis ##########################
REDIS_HOST = os.environ.get("REDIS_HOST", "127.0.0.1")
REDIS_PORT = os.environ.get("REDIS_PORT", "6379")

REDIS_VERSION_KEY = os.environ.get("REDIS_VERSION_KEY", "cs")
GROUP_IVRINFO_RELETION_COUNT_KEY = os.environ.get(
    "GROUP_IVRINFO_RELETION_CACHE_COUNT_KEY",
    "obd_central_group_ivrinfo_reletion_msg_count",
)
COMAPNY_USAGE_CHECK_COUNT_KEY = os.environ.get(
    "COMAPNY_USAGE_CHECK_CACHE_COUNT_KEY",
    "obd_central_company_usage_check_msg_count",
)
