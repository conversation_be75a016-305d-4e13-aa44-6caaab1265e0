import logging

from pythonjsonlogger import jsonlogger


def get_logger(log_level=logging.INFO):
    logger = logging.getLogger()

    logger.setLevel(log_level)
    json_handler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter(
        fmt="%(asctime)s %(levelname)s %(name)s [%(pathname)s:%(lineno)d in function %(funcName)s]  %(message)s %(aws_request_id)s"
    )
    json_handler.setFormatter(formatter)
    logger.addHandler(json_handler)
    if len(logger.handlers) > 1:
        #  removing default handler of lambda
        logger.removeHandler(logger.handlers[0])

    return logger
