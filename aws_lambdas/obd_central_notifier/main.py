from cache_manager.main import CacheManager
from settings import conf


class Notifier(object):
    def __init__(self, event):
        try:
            self.sns_topic_arn = event["Records"][0]["Sns"]["TopicArn"]
        except Exception as e:
            conf.APP_LOGGER.error(
                "obd_central_notifier_lambda - exiting... unable to get sns topic arn, e:{e} event: {event}".format(
                    e=e, event=event
                )
            )

    def notify(self):
        if conf.GROUP_IVR_RELETION_SNS_NAME in self.sns_topic_arn:
            cache_key = ":{prefix}:{value}".format(
                prefix=conf.REDIS_VERSION_KEY,
                value=conf.GROUP_IVRINFO_RELETION_COUNT_KEY,
            )
        elif conf.COMPANYUSAGE_CHECK_SNS_NAME in self.sns_topic_arn:
            cache_key = ":{prefix}:{value}".format(
                prefix=conf.REDIS_VERSION_KEY,
                value=conf.COMAPNY_USAGE_CHECK_COUNT_KEY,
            )
        else:
            conf.APP_LOGGER.error(
                "obd_central_notifier_lambda - exiting... no sns_name matching with sns_arn name1: {n1}, name2:{n2}, sns_arn: {arn}".format(
                    n1=conf.GROUP_IVR_RELETION_SNS_NAME,
                    n2=conf.COMPANYUSAGE_CHECK_SNS_NAME,
                    arn=self.sns_topic_arn,
                )
            )
            return

        CacheManager.incr_value(cache_key)
        conf.APP_LOGGER.info(
            f"obd-central-notifier - Incremented cache key - {cache_key}"
        )


def action(event):
    conf.APP_LOGGER.info(f"event: {event}")
    Notifier(event).notify()
