# Central Trigger Lambdas

This module is a part of **MyOperator OBD**, it contains a lambda function that increments cache keys of "obd_central_cache" for
"GroupIvrRelation Handler" and "CompanyUsageCheck Handler".

## Lambda Handler

* main.action

## Flow

* Fetch SNS_ARN
* Decide CACHE_KEY based on SNS_NAME
* INCREMENT CACHE KEY

### Prerequisites

* Python3.10

## Environment Variables

```.env

REDIS_HOST                             - 127.0.0.1 (must)
REDIS_PORT                             - 6379
REDIS_VERSION_KEY                      - cs
GROUP_IVRINFO_RELETION_CACHE_COUNT_KEY - obd_central_group_ivrinfo_reletion_msg_count
COMAPNY_USAGE_CHECK_CACHE_COUNT_KEY    - obd_central_company_usage_check_msg_count
GROUP_IVR_RELETION_SNS_NAME            - obd_group_ivr_relation_notifier
COMPANYUSAGE_CHECK_SNS_NAME            - obd_companyusage_check_notifier

```

## Testing

**Prerequisite: Redis server for caching and configure REDIS_HOST and REDIS_PORT in `settings.conf` file.**

**Command:** `python -m pytest -vv`

## Coverage

***Command:***

`coverage report -m`
