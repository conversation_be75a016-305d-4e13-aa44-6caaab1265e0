import uuid

from settings.redis_conf import REDIS_CONN


def get_unique_cache_key() -> str:
    """function for getting unique cache key"""

    cache_key: str = str(uuid.uuid4()).replace("-", "")

    # create another cache key if cache_name already exist in Redis
    key_exists: bool = True
    while key_exists:
        if REDIS_CONN.exists(cache_key) == 1:
            cache_key: str = str(uuid.uuid4()).replace("-", "")
        else:
            key_exists = False

    return cache_key
