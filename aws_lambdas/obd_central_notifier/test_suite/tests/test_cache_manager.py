import uuid
from unittest.mock import patch

import pytest
from cache_manager.main import <PERSON><PERSON><PERSON><PERSON><PERSON>
from settings.redis_conf import REDIS_CONN

from test_suite.utils import get_unique_cache_key


@pytest.mark.cache
class TestCacheMain:
    cache: CacheManager = CacheManager()
    expiry: int = 100

    @pytest.fixture()
    def mock_logger(self):
        logger_patch = patch("settings.conf.APP_LOGGER")
        yield logger_patch.start()
        logger_patch.stop()

    def test_get_value(self, mock_logger):
        cache_key: str = get_unique_cache_key()
        cache_value: str = "value of test_get_value cache key"
        REDIS_CONN.set(cache_key, cache_value, ex=self.expiry)  # set cache key

        value: str = self.cache.get_value(cache_key)
        assert cache_value == value

    def test_set_value(self, mock_logger):
        cache_key: str = get_unique_cache_key()
        cache_value: str = str(uuid.uuid4())

        # call `set_value` method and check key set or not
        self.cache.set_value(cache_key, cache_value, self.expiry)
        assert REDIS_CONN.exists(cache_key) >= 1

    def test_delete_key(self, mock_logger):
        cache_key: str = get_unique_cache_key()
        cache_value: str = str(uuid.uuid4())
        REDIS_CONN.set(cache_key, cache_value, ex=self.expiry)

        self.cache.delete_key(cache_key)
        result: int = REDIS_CONN.exists(cache_key)
        assert not result

    def test_incr_value(self, mock_logger):
        cache_key: str = get_unique_cache_key()
        cache_value: int = 108

        # set cache key and value
        REDIS_CONN.set(cache_key, cache_value, ex=self.expiry)

        # call `incr_value` method and check
        inc_value: int = self.cache.incr_value(cache_key)
        assert inc_value == cache_value + 1

    def test_decr_value(self, mock_logger):
        cache_key: str = get_unique_cache_key()
        cache_value: int = 55
        REDIS_CONN.set(cache_key, cache_value, ex=self.expiry)

        # call `decr_value` method without delete and check
        dec_value: int = self.cache.decr_value(cache_key)
        assert dec_value == 1
        assert int(REDIS_CONN.get(cache_key)) == cache_value - 1

        cache_key_2: str = get_unique_cache_key()
        cache_value_2: int = 100
        REDIS_CONN.set(cache_key_2, cache_value_2, ex=self.expiry)

        # call `decr_value` method with delete and check
        dec_value: int = self.cache.decr_value(cache_key_2, delete=True)
        assert dec_value == 1
        assert int(REDIS_CONN.get(cache_key_2)) == cache_value_2 - 1
        assert REDIS_CONN.exists(cache_key_2)

        cache_key_3: str = get_unique_cache_key()
        cache_value_3: int = 1
        REDIS_CONN.set(cache_key_3, cache_value_3, ex=self.expiry)

        # call `decr_value` method with delete and check
        dec_value: int = self.cache.decr_value(cache_key_3, delete=True)
        assert dec_value == 1
        assert not REDIS_CONN.exists(cache_key_3)
