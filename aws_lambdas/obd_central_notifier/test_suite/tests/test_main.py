from unittest.mock import patch

import pytest
from main import action
from settings import conf
from settings.redis_conf import REDIS_CONN

from test_suite.data.main_data import sns_data_1, sns_data_2


@pytest.mark.main
class TestMain:
    @pytest.fixture()
    def mock_logger(self):
        logger_patch = patch("settings.conf.APP_LOGGER")
        yield logger_patch.start()
        logger_patch.stop()

    def test_action_if_obd_group_ivr_relation_notifier_in_sns(
        self, mock_logger
    ):
        cache_key: str = f":{conf.REDIS_VERSION_KEY}:{conf.GROUP_IVRINFO_RELETION_COUNT_KEY}"
        cache_value: int = (
            int(REDIS_CONN.get(cache_key)) if REDIS_CONN.get(cache_key) else 0
        )

        action(sns_data_1)

        assert REDIS_CONN.exists(cache_key)
        assert int(REDIS_CONN.get(cache_key)) == cache_value + 1

    def test_action_if_obd_company_usage_check_notifier_in_sns(
        self, mock_logger
    ):
        cache_key: str = (
            f":{conf.REDIS_VERSION_KEY}:{conf.COMAPNY_USAGE_CHECK_COUNT_KEY}"
        )
        cache_value: int = (
            int(REDIS_CONN.get(cache_key)) if REDIS_CONN.get(cache_key) else 0
        )

        action(sns_data_2)

        assert REDIS_CONN.exists(cache_key)
        assert int(REDIS_CONN.get(cache_key)) == cache_value + 1
