from settings.conf import APP_LOGGER
from settings.redis_conf import REDIS_CONN


class CacheManager:
    @staticmethod
    def get_value(key):
        try:
            return REDIS_CONN.get(key)
        except Exception as e:
            APP_LOGGER.error(
                "obd_central_notifier_lambda - Unable to get from cache e:{e}  key:{key}".format(
                    e=e, key=key
                )
            )
            return None

    @staticmethod
    def set_value(key, value, expiry):
        try:
            REDIS_CONN.set(key, value, ex=expiry)
        except Exception as e:
            APP_LOGGER.error(
                "obd_central_notifier_lambda - Unable to set to cache e:{e}  key:{key}, expiry: {expiry}".format(
                    e=e, key=key, expiry=expiry
                )
            )
            pass

    @staticmethod
    def delete_key(key):
        try:
            return REDIS_CONN.delete(key)
        except Exception as e:
            APP_LOGGER.error(
                "obd_central_notifier_lambda - Unable to delete key: {key} from cache e:{e}".format(
                    e=e, key=key
                )
            )
            pass

    @staticmethod
    def incr_value(key):
        try:
            return REDIS_CONN.incr(key)
        except Exception as e:
            APP_LOGGER.error(
                "obd_central_notifier_lambda - Unable to incr key: {key} in cache e:{e}".format(
                    e=e, key=key
                )
            )
            pass

    @staticmethod
    def decr_value(key, delete=False):
        try:
            REDIS_CONN.decr(key)
            if delete and int(CacheManager.get_value(key)) <= 0:
                CacheManager.delete_key(key)
            return 1
        except Exception as e:
            APP_LOGGER.error(
                "obd_central_notifier_lambda - Unable to decr key: {key} in cache e:{e}".format(
                    e=e, key=key
                )
            )
            pass
